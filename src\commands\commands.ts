// Al-Bayan Connect - Ribbon Commands Handler
// Author: Dr. <PERSON>
// Copyright: © 2025 Al-Bayan AI Platform

import { DictationService } from '@services/DictationService';
import { OfficeIntegrationService } from '@services/OfficeIntegrationService';
import { Language, DEFAULT_SETTINGS } from '@types/index';

// Global services for command functions
let dictationService: DictationService | null = null;
let officeService: OfficeIntegrationService | null = null;
let isInitialized = false;

// Initialize services
async function initializeServices() {
  if (isInitialized) return;
  
  try {
    // Load settings from localStorage
    let settings = DEFAULT_SETTINGS;
    try {
      const stored = localStorage.getItem('al-bayan-settings');
      if (stored) {
        settings = { ...DEFAULT_SETTINGS, ...JSON.parse(stored) };
      }
    } catch (error) {
      console.warn('Failed to load settings, using defaults:', error);
    }

    // Initialize services
    officeService = new OfficeIntegrationService();
    dictationService = new DictationService(settings);
    
    isInitialized = true;
    console.log('Al-Bayan Connect services initialized');
  } catch (error) {
    console.error('Failed to initialize services:', error);
    
    // Show error to user
    Office.context.ui.displayDialogAsync(
      'data:text/html,<html><body><h3>Al-Bayan Connect Error</h3><p>Failed to initialize dictation services. Please try refreshing the add-in.</p></body></html>',
      { height: 200, width: 300 }
    );
  }
}

// Quick dictation toggle function
async function toggleQuickDictation(event: Office.AddinCommands.Event) {
  try {
    await initializeServices();
    
    if (!dictationService) {
      throw new Error('Dictation service not available');
    }

    // Toggle dictation state
    if (dictationService.isCurrentlyListening()) {
      dictationService.stopDictation();
      
      // Show notification
      Office.context.ui.displayDialogAsync(
        'data:text/html,<html><body style="font-family: Segoe UI; padding: 20px; text-align: center;"><h3>🎤 Al-Bayan Connect</h3><p>Dictation stopped</p></body></html>',
        { height: 150, width: 250 },
        (result) => {
          if (result.status === Office.AsyncResultStatus.Succeeded) {
            setTimeout(() => {
              result.value.close();
            }, 1500);
          }
        }
      );
    } else {
      dictationService.startDictation();
      
      // Show notification
      Office.context.ui.displayDialogAsync(
        'data:text/html,<html><body style="font-family: Segoe UI; padding: 20px; text-align: center;"><h3>🎤 Al-Bayan Connect</h3><p>Dictation started - speak now!</p></body></html>',
        { height: 150, width: 250 },
        (result) => {
          if (result.status === Office.AsyncResultStatus.Succeeded) {
            setTimeout(() => {
              result.value.close();
            }, 2000);
          }
        }
      );
    }
    
    // Complete the event
    event.completed();
  } catch (error) {
    console.error('Quick dictation error:', error);
    
    // Show error dialog
    Office.context.ui.displayDialogAsync(
      `data:text/html,<html><body style="font-family: Segoe UI; padding: 20px; text-align: center;"><h3>❌ Error</h3><p>${error}</p></body></html>`,
      { height: 200, width: 300 }
    );
    
    event.completed();
  }
}

// Language switching function
async function switchLanguage(event: Office.AddinCommands.Event) {
  try {
    await initializeServices();
    
    if (!dictationService) {
      throw new Error('Dictation service not available');
    }

    // Cycle through languages: Auto -> English -> Arabic -> Auto
    const currentLanguage = dictationService.getCurrentLanguage();
    let nextLanguage: Language;
    
    switch (currentLanguage) {
      case Language.AUTO_DETECT:
        nextLanguage = Language.ENGLISH;
        break;
      case Language.ENGLISH:
        nextLanguage = Language.ARABIC;
        break;
      case Language.ARABIC:
        nextLanguage = Language.AUTO_DETECT;
        break;
      default:
        nextLanguage = Language.AUTO_DETECT;
    }
    
    dictationService.updateLanguage(nextLanguage);
    
    // Show language change notification
    const languageNames = {
      [Language.AUTO_DETECT]: 'Auto Detection',
      [Language.ENGLISH]: 'English',
      [Language.ARABIC]: 'العربية (Arabic)'
    };
    
    Office.context.ui.displayDialogAsync(
      `data:text/html,<html><body style="font-family: Segoe UI; padding: 20px; text-align: center;"><h3>🌍 Language Changed</h3><p>Now using: ${languageNames[nextLanguage]}</p></body></html>`,
      { height: 150, width: 250 },
      (result) => {
        if (result.status === Office.AsyncResultStatus.Succeeded) {
          setTimeout(() => {
            result.value.close();
          }, 2000);
        }
      }
    );
    
    event.completed();
  } catch (error) {
    console.error('Language switch error:', error);
    
    Office.context.ui.displayDialogAsync(
      `data:text/html,<html><body style="font-family: Segoe UI; padding: 20px; text-align: center;"><h3>❌ Error</h3><p>${error}</p></body></html>`,
      { height: 200, width: 300 }
    );
    
    event.completed();
  }
}

// Format Arabic text function
async function formatArabicText(event: Office.AddinCommands.Event) {
  try {
    await initializeServices();
    
    if (!officeService) {
      throw new Error('Office integration service not available');
    }

    // Get current application
    const app = Office.context.host;
    
    if (app === Office.HostType.Word) {
      await Word.run(async (context) => {
        const selection = context.document.getSelection();
        
        // Apply Arabic formatting
        selection.font.name = 'Traditional Arabic';
        selection.paragraphFormat.alignment = Word.Alignment.right;
        selection.font.size = 14;
        
        await context.sync();
      });
    } else if (app === Office.HostType.PowerPoint) {
      await PowerPoint.run(async (context) => {
        const slides = context.presentation.slides;
        const currentSlide = slides.getActiveSlide();
        const shapes = currentSlide.shapes;
        const textBoxes = shapes.getTextBoxes();
        
        context.load(textBoxes);
        await context.sync();
        
        if (textBoxes.items.length > 0) {
          const textRange = textBoxes.items[0].textFrame.textRange;
          textRange.font.name = 'Traditional Arabic';
          textRange.font.size = 14;
          
          await context.sync();
        }
      });
    }
    
    // Show success notification
    Office.context.ui.displayDialogAsync(
      'data:text/html,<html><body style="font-family: Segoe UI; padding: 20px; text-align: center;"><h3>✅ Arabic Formatting Applied</h3><p>Text formatted for Arabic (RTL, Traditional Arabic font)</p></body></html>',
      { height: 150, width: 300 },
      (result) => {
        if (result.status === Office.AsyncResultStatus.Succeeded) {
          setTimeout(() => {
            result.value.close();
          }, 2000);
        }
      }
    );
    
    event.completed();
  } catch (error) {
    console.error('Arabic formatting error:', error);
    
    Office.context.ui.displayDialogAsync(
      `data:text/html,<html><body style="font-family: Segoe UI; padding: 20px; text-align: center;"><h3>❌ Error</h3><p>${error}</p></body></html>`,
      { height: 200, width: 300 }
    );
    
    event.completed();
  }
}

// Format English text function
async function formatEnglishText(event: Office.AddinCommands.Event) {
  try {
    await initializeServices();
    
    if (!officeService) {
      throw new Error('Office integration service not available');
    }

    // Get current application
    const app = Office.context.host;
    
    if (app === Office.HostType.Word) {
      await Word.run(async (context) => {
        const selection = context.document.getSelection();
        
        // Apply English formatting
        selection.font.name = 'Calibri';
        selection.paragraphFormat.alignment = Word.Alignment.left;
        selection.font.size = 12;
        
        await context.sync();
      });
    } else if (app === Office.HostType.PowerPoint) {
      await PowerPoint.run(async (context) => {
        const slides = context.presentation.slides;
        const currentSlide = slides.getActiveSlide();
        const shapes = currentSlide.shapes;
        const textBoxes = shapes.getTextBoxes();
        
        context.load(textBoxes);
        await context.sync();
        
        if (textBoxes.items.length > 0) {
          const textRange = textBoxes.items[0].textFrame.textRange;
          textRange.font.name = 'Calibri';
          textRange.font.size = 12;
          
          await context.sync();
        }
      });
    }
    
    // Show success notification
    Office.context.ui.displayDialogAsync(
      'data:text/html,<html><body style="font-family: Segoe UI; padding: 20px; text-align: center;"><h3>✅ English Formatting Applied</h3><p>Text formatted for English (LTR, Calibri font)</p></body></html>',
      { height: 150, width: 300 },
      (result) => {
        if (result.status === Office.AsyncResultStatus.Succeeded) {
          setTimeout(() => {
            result.value.close();
          }, 2000);
        }
      }
    );
    
    event.completed();
  } catch (error) {
    console.error('English formatting error:', error);
    
    Office.context.ui.displayDialogAsync(
      `data:text/html,<html><body style="font-family: Segoe UI; padding: 20px; text-align: center;"><h3>❌ Error</h3><p>${error}</p></body></html>`,
      { height: 200, width: 300 }
    );
    
    event.completed();
  }
}

// Register command functions
Office.onReady(() => {
  // Register the functions that can be called from ribbon buttons
  (Office as any).actions = {
    toggleQuickDictation,
    switchLanguage,
    formatArabicText,
    formatEnglishText
  };
  
  console.log('Al-Bayan Connect commands registered');
});
