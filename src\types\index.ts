// Al-Bayan Connect - Type Definitions
// Author: Dr. <PERSON>
// Copyright: © 2025 Al-Bayan AI Platform

export interface SpeechRecognitionResult {
  transcript: string;
  confidence: number;
  isFinal: boolean;
  language: Language;
  timestamp: Date;
}

export interface VoiceCommand {
  id: string;
  phrase: string;
  action: CommandAction;
  language: Language;
  category: CommandCategory;
  isCustom: boolean;
  createdAt: Date;
  usageCount: number;
}

export interface CustomCommand extends VoiceCommand {
  userId: string;
  description: string;
  parameters?: Record<string, any>;
  isActive: boolean;
}

export interface DictationSession {
  id: string;
  startTime: Date;
  endTime?: Date;
  duration: number;
  wordsCount: number;
  accuracy: number;
  language: Language;
  commands: VoiceCommand[];
  errors: DictationError[];
}

export interface DictationError {
  id: string;
  type: ErrorType;
  message: string;
  timestamp: Date;
  context?: string;
}

export interface LanguageDetectionResult {
  language: Language;
  confidence: number;
  alternatives: Array<{
    language: Language;
    confidence: number;
  }>;
}

export interface UserSettings {
  defaultLanguage: Language;
  autoLanguageDetection: boolean;
  confidenceThreshold: number;
  enableCustomCommands: boolean;
  privacyMode: PrivacyMode;
  voiceTraining: boolean;
  analytics: AnalyticsSettings;
}

export interface AnalyticsSettings {
  enabled: boolean;
  shareUsageData: boolean;
  retentionDays: number;
}

export interface AnalyticsData {
  totalSessions: number;
  totalWords: number;
  averageAccuracy: number;
  languageUsage: Record<Language, number>;
  commandUsage: Record<string, number>;
  productivityMetrics: ProductivityMetrics;
}

export interface ProductivityMetrics {
  wordsPerMinute: number;
  timeSaved: number; // in minutes
  accuracyTrend: Array<{
    date: Date;
    accuracy: number;
  }>;
  usagePattern: Array<{
    hour: number;
    sessions: number;
  }>;
}

export interface DocumentContext {
  application: OfficeApplication;
  documentId: string;
  selectionRange?: TextRange;
  currentLanguage?: Language;
  formatting?: TextFormatting;
}

export interface TextRange {
  start: number;
  end: number;
  text: string;
}

export interface TextFormatting {
  bold: boolean;
  italic: boolean;
  underline: boolean;
  fontSize: number;
  fontFamily: string;
  color: string;
  direction: TextDirection;
}

export interface ServiceStatus {
  speechRecognition: ServiceState;
  languageDetection: ServiceState;
  documentIntegration: ServiceState;
  analytics: ServiceState;
  cloudServices?: ServiceState;
}

export interface ServiceState {
  isAvailable: boolean;
  isActive: boolean;
  lastError?: string;
  performance: {
    responseTime: number;
    accuracy: number;
  };
}

// Enums
export enum Language {
  ARABIC = 'ar',
  ENGLISH = 'en',
  AUTO_DETECT = 'auto'
}

export enum CommandAction {
  INSERT_TEXT = 'insertText',
  FORMAT_TEXT = 'formatText',
  NAVIGATE = 'navigate',
  DELETE = 'delete',
  UNDO = 'undo',
  CUSTOM = 'custom'
}

export enum CommandCategory {
  PUNCTUATION = 'punctuation',
  FORMATTING = 'formatting',
  NAVIGATION = 'navigation',
  EDITING = 'editing',
  CUSTOM = 'custom'
}

export enum ErrorType {
  SPEECH_RECOGNITION = 'speechRecognition',
  LANGUAGE_DETECTION = 'languageDetection',
  DOCUMENT_INTEGRATION = 'documentIntegration',
  NETWORK = 'network',
  PERMISSION = 'permission',
  UNKNOWN = 'unknown'
}

export enum PrivacyMode {
  LOCAL_ONLY = 'localOnly',
  CLOUD_ENHANCED = 'cloudEnhanced',
  HYBRID = 'hybrid'
}

export enum OfficeApplication {
  WORD = 'word',
  POWERPOINT = 'powerpoint',
  OUTLOOK = 'outlook'
}

export enum TextDirection {
  LTR = 'ltr',
  RTL = 'rtl'
}

export enum DictationState {
  IDLE = 'idle',
  LISTENING = 'listening',
  PROCESSING = 'processing',
  ERROR = 'error'
}

// Event Types
export interface DictationEvent {
  type: DictationEventType;
  data: any;
  timestamp: Date;
}

export enum DictationEventType {
  STARTED = 'started',
  STOPPED = 'stopped',
  RESULT = 'result',
  ERROR = 'error',
  LANGUAGE_DETECTED = 'languageDetected',
  COMMAND_EXECUTED = 'commandExecuted'
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: Date;
}

export interface SpeechToTextResponse extends ApiResponse {
  data: {
    transcript: string;
    confidence: number;
    language: Language;
    alternatives?: string[];
  };
}

export interface LanguageDetectionResponse extends ApiResponse {
  data: LanguageDetectionResult;
}

// Component Props Types
export interface DictationPanelProps {
  isVisible: boolean;
  onClose: () => void;
  settings: UserSettings;
  onSettingsChange: (settings: UserSettings) => void;
}

export interface CommandBuilderProps {
  commands: CustomCommand[];
  onCommandCreate: (command: Omit<CustomCommand, 'id' | 'createdAt' | 'usageCount'>) => void;
  onCommandUpdate: (id: string, command: Partial<CustomCommand>) => void;
  onCommandDelete: (id: string) => void;
}

export interface AnalyticsDashboardProps {
  data: AnalyticsData;
  dateRange: {
    start: Date;
    end: Date;
  };
  onDateRangeChange: (range: { start: Date; end: Date }) => void;
}

// Utility Types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type EventHandler<T = any> = (event: T) => void;

export type AsyncEventHandler<T = any> = (event: T) => Promise<void>;

// Constants
export const SUPPORTED_LANGUAGES = [Language.ARABIC, Language.ENGLISH] as const;

export const DEFAULT_SETTINGS: UserSettings = {
  defaultLanguage: Language.AUTO_DETECT,
  autoLanguageDetection: true,
  confidenceThreshold: 0.8,
  enableCustomCommands: true,
  privacyMode: PrivacyMode.HYBRID,
  voiceTraining: false,
  analytics: {
    enabled: true,
    shareUsageData: false,
    retentionDays: 30
  }
};

export const COMMAND_CATEGORIES = [
  CommandCategory.PUNCTUATION,
  CommandCategory.FORMATTING,
  CommandCategory.NAVIGATION,
  CommandCategory.EDITING,
  CommandCategory.CUSTOM
] as const;
