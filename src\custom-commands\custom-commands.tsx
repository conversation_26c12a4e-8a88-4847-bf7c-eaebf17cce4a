// Al-Bayan Connect - Custom Commands Interface
// Author: Dr. <PERSON>
// Copyright: © 2025 Al-Bayan AI Platform

import React, { useState, useEffect } from 'react';
import ReactDOM from 'react-dom/client';
import { FluentProvider, webLightTheme } from '@fluentui/react-components';
import { CustomCommandBuilder } from '../components/CustomCommandBuilder';
import { ErrorBoundary } from '../components/ErrorBoundary';
import { VoiceCommandProcessor } from '../services/VoiceCommandProcessor';
import { CustomCommand } from '@types/index';

const CustomCommandsApp: React.FC = () => {
  const [commands, setCommands] = useState<CustomCommand[]>([]);
  const [commandProcessor, setCommandProcessor] = useState<VoiceCommandProcessor | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const initializeCommandProcessor = async () => {
      try {
        setIsLoading(true);
        const processor = new VoiceCommandProcessor();
        setCommandProcessor(processor);
        
        // Load existing custom commands
        const existingCommands = processor.getCustomCommands();
        setCommands(existingCommands);
      } catch (error) {
        console.error('Failed to initialize command processor:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeCommandProcessor();
  }, []);

  const handleCommandCreate = (command: Omit<CustomCommand, 'id' | 'createdAt' | 'usageCount'>) => {
    if (!commandProcessor) return;

    const newCommand = commandProcessor.addCustomCommand(command);
    setCommands(prev => [...prev, newCommand]);
  };

  const handleCommandUpdate = (id: string, updates: Partial<CustomCommand>) => {
    if (!commandProcessor) return;

    const success = commandProcessor.updateCustomCommand(id, updates);
    if (success) {
      setCommands(prev => prev.map(cmd => 
        cmd.id === id ? { ...cmd, ...updates } : cmd
      ));
    }
  };

  const handleCommandDelete = (id: string) => {
    if (!commandProcessor) return;

    const success = commandProcessor.deleteCustomCommand(id);
    if (success) {
      setCommands(prev => prev.filter(cmd => cmd.id !== id));
    }
  };

  if (isLoading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        flexDirection: 'column',
        gap: '16px'
      }}>
        <div className="loading-spinner" />
        <div>Loading custom commands...</div>
      </div>
    );
  }

  return (
    <FluentProvider theme={webLightTheme}>
      <ErrorBoundary>
        <CustomCommandBuilder
          commands={commands}
          onCommandCreate={handleCommandCreate}
          onCommandUpdate={handleCommandUpdate}
          onCommandDelete={handleCommandDelete}
        />
      </ErrorBoundary>
    </FluentProvider>
  );
};

// Initialize the React app when Office is ready
Office.onReady(() => {
  const container = document.getElementById('commands-root');
  if (container) {
    const root = ReactDOM.createRoot(container);
    root.render(<CustomCommandsApp />);
  }
});
