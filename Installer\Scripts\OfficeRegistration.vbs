' Al-Bayan Connect - Office Add-in Registration Script
' Author: Dr. <PERSON>
' Copyright: © 2025 Al-Bayan AI Platform

Option Explicit

Dim shell, fso, manifestPath, installPath, logFile
Dim officeVersions, version, catalogId, addinId

' Initialize objects
Set shell = CreateObject("WScript.Shell")
Set fso = CreateObject("Scripting.FileSystemObject")

' Configuration
addinId = "{12345678-1234-1234-1234-123456789012}"
catalogId = "{al-bayan-connect-catalog}"

' Get installation path from registry or parameter
installPath = GetInstallationPath()
manifestPath = installPath & "manifest.xml"
logFile = installPath & "Logs\registration.log"

' Ensure log directory exists
If Not fso.FolderExists(installPath & "Logs") Then
    fso.CreateFolder(installPath & "Logs")
End If

' Main registration process
Sub RegisterOfficeAddin()
    On Error Resume Next
    
    WriteLog "Starting Office Add-in registration..."
    WriteLog "Manifest path: " & manifestPath
    
    ' Verify manifest file exists
    If Not fso.FileExists(manifestPath) Then
        WriteLog "ERROR: Manifest file not found at " & manifestPath
        WScript.Quit 1
    End If
    
    ' Detect installed Office versions
    officeVersions = DetectOfficeVersions()
    
    If UBound(officeVersions) = -1 Then
        WriteLog "ERROR: No supported Office versions found"
        WScript.Quit 1
    End If
    
    ' Register for each detected Office version
    For Each version In officeVersions
        RegisterForOfficeVersion version
    Next
    
    ' Create additional registry entries
    CreateAdditionalRegistryEntries()
    
    ' Verify registration
    If VerifyRegistration() Then
        WriteLog "Office Add-in registration completed successfully"
        WScript.Quit 0
    Else
        WriteLog "ERROR: Registration verification failed"
        WScript.Quit 1
    End If
    
End Sub

' Detect installed Office versions
Function DetectOfficeVersions()
    Dim versions(), versionCount, regKey, testKey
    versionCount = 0
    
    ' Office 2016/2019/365 (Version 16.0)
    testKey = "HKLM\SOFTWARE\Microsoft\Office\16.0\Common\InstallRoot\"
    If RegKeyExists(testKey) Then
        ReDim Preserve versions(versionCount)
        versions(versionCount) = "16.0"
        versionCount = versionCount + 1
        WriteLog "Detected Office 16.0 (2016/2019/365)"
    End If
    
    ' Office 2013 (Version 15.0)
    testKey = "HKLM\SOFTWARE\Microsoft\Office\15.0\Common\InstallRoot\"
    If RegKeyExists(testKey) Then
        ReDim Preserve versions(versionCount)
        versions(versionCount) = "15.0"
        versionCount = versionCount + 1
        WriteLog "Detected Office 15.0 (2013)"
    End If
    
    ' Office 2010 (Version 14.0) - Limited support
    testKey = "HKLM\SOFTWARE\Microsoft\Office\14.0\Common\InstallRoot\"
    If RegKeyExists(testKey) Then
        ReDim Preserve versions(versionCount)
        versions(versionCount) = "14.0"
        versionCount = versionCount + 1
        WriteLog "Detected Office 14.0 (2010) - Limited support"
    End If
    
    DetectOfficeVersions = versions
End Function

' Register add-in for specific Office version
Sub RegisterForOfficeVersion(version)
    Dim regPath, catalogKey, uniqueCatalogId
    
    WriteLog "Registering for Office version " & version
    
    ' Generate unique catalog ID for this version
    uniqueCatalogId = catalogId & "-" & Replace(version, ".", "")
    
    ' Create trusted catalog entry
    regPath = "HKCU\Software\Microsoft\Office\" & version & "\WEF\TrustedCatalogs\" & uniqueCatalogId & "\"
    
    On Error Resume Next
    shell.RegWrite regPath & "Id", addinId, "REG_SZ"
    shell.RegWrite regPath & "Url", manifestPath, "REG_SZ"
    shell.RegWrite regPath & "Flags", 1, "REG_DWORD"
    
    If Err.Number = 0 Then
        WriteLog "Successfully registered trusted catalog for Office " & version
    Else
        WriteLog "ERROR: Failed to register trusted catalog for Office " & version & " - " & Err.Description
        Err.Clear
    End If
    
    ' Add to trusted locations (for enhanced security)
    regPath = "HKCU\Software\Microsoft\Office\" & version & "\Common\Security\Trusted Locations\Location99\"
    shell.RegWrite regPath & "Path", installPath, "REG_SZ"
    shell.RegWrite regPath & "Description", "Al-Bayan Connect Installation Directory", "REG_SZ"
    shell.RegWrite regPath & "AllowSubFolders", 1, "REG_DWORD"
    
    If Err.Number = 0 Then
        WriteLog "Successfully added trusted location for Office " & version
    Else
        WriteLog "WARNING: Failed to add trusted location for Office " & version & " - " & Err.Description
        Err.Clear
    End If
    
End Sub

' Create additional registry entries
Sub CreateAdditionalRegistryEntries()
    Dim regPath
    
    WriteLog "Creating additional registry entries..."
    
    ' Al-Bayan Connect application registry
    regPath = "HKCU\Software\Al-Bayan\Connect\"
    shell.RegWrite regPath & "InstallPath", installPath, "REG_SZ"
    shell.RegWrite regPath & "Version", "*******", "REG_SZ"
    shell.RegWrite regPath & "InstallDate", Now(), "REG_SZ"
    shell.RegWrite regPath & "ManifestPath", manifestPath, "REG_SZ"
    
    ' File association for .albayan files
    shell.RegWrite "HKCU\Software\Classes\.albayan\", "AlBayanConnect.CommandFile", "REG_SZ"
    shell.RegWrite "HKCU\Software\Classes\AlBayanConnect.CommandFile\", "Al-Bayan Connect Command File", "REG_SZ"
    shell.RegWrite "HKCU\Software\Classes\AlBayanConnect.CommandFile\DefaultIcon\", installPath & "AlBayanConnectLauncher.exe,0", "REG_SZ"
    shell.RegWrite "HKCU\Software\Classes\AlBayanConnect.CommandFile\shell\open\command\", """" & installPath & "AlBayanConnectLauncher.exe"" ""%1""", "REG_SZ"
    
    WriteLog "Additional registry entries created successfully"
End Sub

' Verify registration was successful
Function VerifyRegistration()
    Dim regPath, testValue, isValid
    isValid = True
    
    WriteLog "Verifying registration..."
    
    ' Check if at least one Office version has the trusted catalog
    For Each version In officeVersions
        regPath = "HKCU\Software\Microsoft\Office\" & version & "\WEF\TrustedCatalogs\"
        testValue = shell.RegRead(regPath & catalogId & "-" & Replace(version, ".", "") & "\Url")
        
        If testValue = manifestPath Then
            WriteLog "Verification successful for Office " & version
            VerifyRegistration = True
            Exit Function
        End If
    Next
    
    WriteLog "ERROR: Verification failed - no valid registrations found"
    VerifyRegistration = False
End Function

' Unregister Office Add-in
Sub UnregisterOfficeAddin()
    WriteLog "Starting Office Add-in unregistration..."
    
    ' Remove trusted catalog entries for all Office versions
    For Each version In Array("16.0", "15.0", "14.0")
        RemoveRegistrationForVersion version
    Next
    
    ' Remove Al-Bayan Connect registry entries
    On Error Resume Next
    shell.RegDelete "HKCU\Software\Al-Bayan\Connect\"
    shell.RegDelete "HKCU\Software\Classes\.albayan\"
    shell.RegDelete "HKCU\Software\Classes\AlBayanConnect.CommandFile\"
    
    WriteLog "Office Add-in unregistration completed"
End Sub

' Remove registration for specific Office version
Sub RemoveRegistrationForVersion(version)
    Dim regPath, uniqueCatalogId
    
    uniqueCatalogId = catalogId & "-" & Replace(version, ".", "")
    regPath = "HKCU\Software\Microsoft\Office\" & version & "\WEF\TrustedCatalogs\" & uniqueCatalogId & "\"
    
    On Error Resume Next
    shell.RegDelete regPath
    
    ' Remove trusted location
    regPath = "HKCU\Software\Microsoft\Office\" & version & "\Common\Security\Trusted Locations\Location99\"
    shell.RegDelete regPath
    
    WriteLog "Removed registration for Office " & version
End Sub

' Utility Functions
Function GetInstallationPath()
    Dim path
    
    ' Try to get from registry first
    On Error Resume Next
    path = shell.RegRead("HKCU\Software\Al-Bayan\Connect\InstallPath")
    
    If Err.Number <> 0 Or path = "" Then
        ' Fallback to default location
        path = shell.ExpandEnvironmentStrings("%LOCALAPPDATA%") & "\Al-Bayan Connect\"
        Err.Clear
    End If
    
    ' Ensure path ends with backslash
    If Right(path, 1) <> "\" Then
        path = path & "\"
    End If
    
    GetInstallationPath = path
End Function

Function RegKeyExists(keyPath)
    On Error Resume Next
    shell.RegRead keyPath
    RegKeyExists = (Err.Number = 0)
    Err.Clear
End Function

Sub WriteLog(message)
    Dim logFileHandle
    
    On Error Resume Next
    Set logFileHandle = fso.OpenTextFile(logFile, 8, True) ' 8 = ForAppending, True = Create if not exists
    
    If Err.Number = 0 Then
        logFileHandle.WriteLine Now() & " - " & message
        logFileHandle.Close
    End If
    
    ' Also output to console if running interactively
    WScript.Echo message
End Sub

' Main execution
If WScript.Arguments.Count > 0 Then
    Select Case LCase(WScript.Arguments(0))
        Case "register", "/register", "-register"
            RegisterOfficeAddin()
        Case "unregister", "/unregister", "-unregister"
            UnregisterOfficeAddin()
        Case Else
            WScript.Echo "Usage: OfficeRegistration.vbs [register|unregister]"
            WScript.Quit 1
    End Select
Else
    ' Default action is register
    RegisterOfficeAddin()
End If
