// Al-Bayan Connect - Language Detection Service
// Author: Dr. <PERSON>
// Copyright: © 2025 Al-Bayan AI Platform

import {
  Language,
  LanguageDetectionResult,
  UserSettings
} from '@types/index';

export class LanguageDetectionService {
  private settings: UserSettings | null = null;

  // Arabic Unicode ranges and patterns
  private readonly ARABIC_UNICODE_RANGES = [
    [0x0600, 0x06FF], // Arabic
    [0x0750, 0x077F], // Arabic Supplement
    [0x08A0, 0x08FF], // Arabic Extended-A
    [0xFB50, 0xFDFF], // Arabic Presentation Forms-A
    [0xFE70, 0xFEFF]  // Arabic Presentation Forms-B
  ];

  // Common Arabic words and patterns
  private readonly ARABIC_COMMON_WORDS = [
    'في', 'من', 'إلى', 'على', 'هذا', 'هذه', 'التي', 'الذي', 'كان', 'كانت',
    'يكون', 'تكون', 'هو', 'هي', 'أن', 'إن', 'لا', 'ما', 'قد', 'لقد',
    'والله', 'الله', 'محمد', 'عبد', 'أحمد', 'علي', 'حسن', 'حسين'
  ];

  // Common English words and patterns
  private readonly ENGLISH_COMMON_WORDS = [
    'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of',
    'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during',
    'before', 'after', 'above', 'below', 'between', 'among', 'is', 'are',
    'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do',
    'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might'
  ];

  // Arabic linguistic patterns
  private readonly ARABIC_PATTERNS = [
    /^ال.+/, // Definite article
    /^و.+/, // Conjunction
    /^ب.+/, // Preposition
    /^ل.+/, // Preposition
    /^ف.+/, // Conjunction
    /.+ة$/, // Feminine ending
    /.+ات$/, // Plural ending
    /.+ين$/, // Masculine plural ending
    /.+ون$/, // Masculine plural ending
  ];

  // English linguistic patterns
  private readonly ENGLISH_PATTERNS = [
    /^[a-zA-Z]+ing$/, // Present participle
    /^[a-zA-Z]+ed$/, // Past tense
    /^[a-zA-Z]+ly$/, // Adverb
    /^[a-zA-Z]+tion$/, // Noun ending
    /^[a-zA-Z]+ness$/, // Noun ending
    /^un[a-zA-Z]+/, // Negative prefix
    /^re[a-zA-Z]+/, // Prefix
  ];

  constructor() {
    // Initialize with default settings
  }

  public async detectLanguage(text: string): Promise<LanguageDetectionResult> {
    const cleanText = this.preprocessText(text);
    
    if (cleanText.length < 2) {
      return {
        language: Language.ENGLISH, // Default fallback
        confidence: 0.5,
        alternatives: [
          { language: Language.ARABIC, confidence: 0.5 }
        ]
      };
    }

    const arabicScore = this.calculateArabicScore(cleanText);
    const englishScore = this.calculateEnglishScore(cleanText);
    
    // Normalize scores
    const totalScore = arabicScore + englishScore;
    const normalizedArabicScore = totalScore > 0 ? arabicScore / totalScore : 0;
    const normalizedEnglishScore = totalScore > 0 ? englishScore / totalScore : 0;

    // Determine primary language
    let primaryLanguage: Language;
    let primaryConfidence: number;
    let secondaryLanguage: Language;
    let secondaryConfidence: number;

    if (normalizedArabicScore > normalizedEnglishScore) {
      primaryLanguage = Language.ARABIC;
      primaryConfidence = normalizedArabicScore;
      secondaryLanguage = Language.ENGLISH;
      secondaryConfidence = normalizedEnglishScore;
    } else {
      primaryLanguage = Language.ENGLISH;
      primaryConfidence = normalizedEnglishScore;
      secondaryLanguage = Language.ARABIC;
      secondaryConfidence = normalizedArabicScore;
    }

    // Apply confidence threshold and adjustments
    primaryConfidence = Math.min(primaryConfidence * 1.2, 1.0); // Boost confidence slightly
    
    return {
      language: primaryLanguage,
      confidence: primaryConfidence,
      alternatives: [
        { language: secondaryLanguage, confidence: secondaryConfidence }
      ]
    };
  }

  private preprocessText(text: string): string {
    return text
      .trim()
      .toLowerCase()
      .replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFFa-zA-Z\s]/g, ' ')
      .replace(/\s+/g, ' ');
  }

  private calculateArabicScore(text: string): number {
    let score = 0;
    const words = text.split(/\s+/).filter(word => word.length > 0);
    
    if (words.length === 0) return 0;

    // Unicode character analysis
    const arabicCharCount = this.countArabicCharacters(text);
    const totalCharCount = text.replace(/\s/g, '').length;
    const arabicCharRatio = totalCharCount > 0 ? arabicCharCount / totalCharCount : 0;
    
    score += arabicCharRatio * 50; // Heavy weight for Arabic characters

    // Common words analysis
    const arabicWordMatches = words.filter(word => 
      this.ARABIC_COMMON_WORDS.includes(word)
    ).length;
    score += (arabicWordMatches / words.length) * 30;

    // Pattern analysis
    const arabicPatternMatches = words.filter(word =>
      this.ARABIC_PATTERNS.some(pattern => pattern.test(word))
    ).length;
    score += (arabicPatternMatches / words.length) * 20;

    // Text direction hints (RTL markers, Arabic punctuation)
    if (this.hasRTLMarkers(text)) {
      score += 10;
    }

    return Math.min(score, 100);
  }

  private calculateEnglishScore(text: string): number {
    let score = 0;
    const words = text.split(/\s+/).filter(word => word.length > 0);
    
    if (words.length === 0) return 0;

    // Latin character analysis
    const latinCharCount = this.countLatinCharacters(text);
    const totalCharCount = text.replace(/\s/g, '').length;
    const latinCharRatio = totalCharCount > 0 ? latinCharCount / totalCharCount : 0;
    
    score += latinCharRatio * 50; // Heavy weight for Latin characters

    // Common words analysis
    const englishWordMatches = words.filter(word => 
      this.ENGLISH_COMMON_WORDS.includes(word.toLowerCase())
    ).length;
    score += (englishWordMatches / words.length) * 30;

    // Pattern analysis
    const englishPatternMatches = words.filter(word =>
      this.ENGLISH_PATTERNS.some(pattern => pattern.test(word.toLowerCase()))
    ).length;
    score += (englishPatternMatches / words.length) * 20;

    return Math.min(score, 100);
  }

  private countArabicCharacters(text: string): number {
    let count = 0;
    for (const char of text) {
      const charCode = char.charCodeAt(0);
      if (this.ARABIC_UNICODE_RANGES.some(([start, end]) => 
        charCode >= start && charCode <= end
      )) {
        count++;
      }
    }
    return count;
  }

  private countLatinCharacters(text: string): number {
    let count = 0;
    for (const char of text) {
      const charCode = char.charCodeAt(0);
      if ((charCode >= 65 && charCode <= 90) || // A-Z
          (charCode >= 97 && charCode <= 122)) { // a-z
        count++;
      }
    }
    return count;
  }

  private hasRTLMarkers(text: string): boolean {
    // Check for RTL markers and Arabic punctuation
    const rtlMarkers = ['\u200F', '\u202E', '؟', '؛', '،'];
    return rtlMarkers.some(marker => text.includes(marker));
  }

  public detectMixedLanguageContent(text: string): {
    segments: Array<{
      text: string;
      language: Language;
      confidence: number;
    }>;
    overallLanguage: Language;
  } {
    const sentences = this.splitIntoSentences(text);
    const segments = sentences.map(sentence => {
      const detection = this.detectLanguage(sentence);
      return {
        text: sentence,
        language: detection.language,
        confidence: detection.confidence
      };
    });

    // Determine overall language based on segment lengths and confidences
    let arabicWeight = 0;
    let englishWeight = 0;

    segments.forEach(segment => {
      const weight = segment.text.length * segment.confidence;
      if (segment.language === Language.ARABIC) {
        arabicWeight += weight;
      } else {
        englishWeight += weight;
      }
    });

    const overallLanguage = arabicWeight > englishWeight ? Language.ARABIC : Language.ENGLISH;

    return {
      segments,
      overallLanguage
    };
  }

  private splitIntoSentences(text: string): string[] {
    // Split by common sentence endings in both languages
    return text
      .split(/[.!?؟؛]+/)
      .map(s => s.trim())
      .filter(s => s.length > 0);
  }

  public updateSettings(settings: UserSettings): void {
    this.settings = settings;
  }

  public isLanguageSupported(language: Language): boolean {
    return language === Language.ARABIC || 
           language === Language.ENGLISH || 
           language === Language.AUTO_DETECT;
  }

  public getConfidenceThreshold(): number {
    return this.settings?.confidenceThreshold || 0.8;
  }

  // Advanced detection for specific Arabic dialects
  public detectArabicDialect(text: string): {
    dialect: string;
    confidence: number;
  } {
    const dialectPatterns = {
      'Gulf': ['شلون', 'وين', 'شنو', 'هاي', 'ذيج'],
      'Egyptian': ['إيه', 'ازاي', 'فين', 'ده', 'دي'],
      'Levantine': ['شو', 'وين', 'هاد', 'هاي', 'كيف'],
      'Maghrebi': ['شنو', 'فين', 'هذا', 'هذي', 'كيفاش'],
      'Standard': ['ما', 'أين', 'كيف', 'هذا', 'هذه']
    };

    let bestMatch = { dialect: 'Standard', confidence: 0 };

    Object.entries(dialectPatterns).forEach(([dialect, patterns]) => {
      const matches = patterns.filter(pattern => text.includes(pattern)).length;
      const confidence = matches / patterns.length;
      
      if (confidence > bestMatch.confidence) {
        bestMatch = { dialect, confidence };
      }
    });

    return bestMatch;
  }
}
