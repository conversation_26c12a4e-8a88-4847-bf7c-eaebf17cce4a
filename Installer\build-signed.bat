@echo off
REM Al-Bayan Connect - Signed Build Script
REM Author: Dr<PERSON> <PERSON> Esmail
REM Copyright: © 2025 Al-Bayan AI Platform

setlocal enabledelayedexpansion

echo ===============================================
echo Al-Bayan Connect Signed Build
echo ===============================================
echo Date: %DATE% %TIME%
echo ===============================================

REM Configuration
set "PROJECT_FILE=AlBayanConnectInstaller.wixproj"
set "BUILD_CONFIG=Release"
set "BUILD_PLATFORM=x86"
set "SIGN_OUTPUT=true"
set "VALIDATE_CERTS=true"

REM Certificate configuration (set these environment variables or modify here)
REM set "CODE_SIGNING_CERT_THUMBPRINT=YOUR_CERT_THUMBPRINT_HERE"
REM set "CODE_SIGNING_CERT_PATH=path\to\certificate.pfx"
REM set "CODE_SIGNING_CERT_PASSWORD=your_password_here"

echo Checking build environment...

REM Check if MSBuild is available
where msbuild.exe >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: MSBuild not found in PATH
    echo Please ensure Visual Studio Build Tools or Visual Studio is installed
    goto :error_exit
)

REM Check if WiX Toolset is available
where candle.exe >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: WiX Toolset not found in PATH
    echo Please ensure WiX Toolset v4.0 is installed
    goto :error_exit
)

REM Check if project file exists
if not exist "%PROJECT_FILE%" (
    echo ERROR: Project file not found: %PROJECT_FILE%
    echo Please run this script from the installer directory
    goto :error_exit
)

echo Build environment check: PASSED
echo.

REM Check certificate configuration
if "%CODE_SIGNING_CERT_THUMBPRINT%"=="" if "%CODE_SIGNING_CERT_PATH%"=="" (
    echo WARNING: No code signing certificate configured
    echo The installer will be built but not signed
    echo.
    echo To enable code signing, set one of these environment variables:
    echo   CODE_SIGNING_CERT_THUMBPRINT - for certificates in the certificate store
    echo   CODE_SIGNING_CERT_PATH - for certificate files (.pfx/.p12)
    echo.
    set "SIGN_OUTPUT=false"
) else (
    echo Code signing certificate configured
    if not "%CODE_SIGNING_CERT_THUMBPRINT%"=="" (
        echo   Using certificate from store: %CODE_SIGNING_CERT_THUMBPRINT%
    )
    if not "%CODE_SIGNING_CERT_PATH%"=="" (
        echo   Using certificate file: %CODE_SIGNING_CERT_PATH%
    )
    echo.
)

echo Starting build process...
echo Configuration: %BUILD_CONFIG%
echo Platform: %BUILD_PLATFORM%
echo Sign Output: %SIGN_OUTPUT%
echo Validate Certificates: %VALIDATE_CERTS%
echo.

REM Clean previous build
echo Cleaning previous build...
msbuild "%PROJECT_FILE%" /p:Configuration=%BUILD_CONFIG% /p:Platform=%BUILD_PLATFORM% /t:Clean /v:minimal
if %errorlevel% neq 0 (
    echo ERROR: Clean failed
    goto :error_exit
)

REM Build the installer
echo Building installer...
msbuild "%PROJECT_FILE%" ^
    /p:Configuration=%BUILD_CONFIG% ^
    /p:Platform=%BUILD_PLATFORM% ^
    /p:SignOutput=%SIGN_OUTPUT% ^
    /p:ValidateCerts=%VALIDATE_CERTS% ^
    /p:CodeSigningCertThumbprint="%CODE_SIGNING_CERT_THUMBPRINT%" ^
    /p:CodeSigningCertPath="%CODE_SIGNING_CERT_PATH%" ^
    /p:CodeSigningCertPassword="%CODE_SIGNING_CERT_PASSWORD%" ^
    /v:normal

if %errorlevel% neq 0 (
    echo ERROR: Build failed
    goto :error_exit
)

echo.
echo Build completed successfully!

REM Display build output information
set "OUTPUT_DIR=bin\%BUILD_CONFIG%"
set "OUTPUT_FILE=%OUTPUT_DIR%\AlBayanConnectInstaller.msi"

if exist "%OUTPUT_FILE%" (
    echo.
    echo ===============================================
    echo Build Output Information
    echo ===============================================
    echo Output File: %OUTPUT_FILE%
    
    REM Get file size
    for %%A in ("%OUTPUT_FILE%") do (
        set "FILE_SIZE=%%~zA"
        set /a "FILE_SIZE_MB=!FILE_SIZE! / 1024 / 1024"
        echo File Size: !FILE_SIZE_MB! MB
    )
    
    REM Get file version (if available)
    powershell.exe -Command "try { $v = [System.Diagnostics.FileVersionInfo]::GetVersionInfo('%OUTPUT_FILE%'); Write-Host 'File Version:' $v.FileVersion } catch { Write-Host 'File Version: Not available' }" 2>nul
    
    REM Check digital signature
    echo.
    echo Checking digital signature...
    powershell.exe -ExecutionPolicy Bypass -Command "try { $sig = Get-AuthenticodeSignature '%OUTPUT_FILE%'; Write-Host 'Signature Status:' $sig.Status; if ($sig.SignerCertificate) { Write-Host 'Signer:' $sig.SignerCertificate.Subject } } catch { Write-Host 'Signature check failed' }" 2>nul
    
    echo ===============================================
    echo.
    echo SUCCESS: Al-Bayan Connect installer has been built successfully!
    echo.
    echo The installer is ready for distribution.
    echo For enterprise deployment, use the scripts in the Scripts folder.
    echo.
) else (
    echo ERROR: Output file not found: %OUTPUT_FILE%
    goto :error_exit
)

goto :success_exit

:error_exit
echo.
echo ===============================================
echo Build Failed
echo ===============================================
echo Please check the error messages above and try again.
echo.
echo Common issues:
echo - WiX Toolset not installed or not in PATH
echo - Visual Studio Build Tools not installed
echo - Missing dependencies or source files
echo - Certificate configuration issues
echo.
echo For support, visit: https://github.com/al-bayan-ai/al-bayan-connect
echo ===============================================
exit /b 1

:success_exit
echo For support and documentation, visit:
echo   GitHub: https://github.com/al-bayan-ai/al-bayan-connect
echo   Website: https://al-bayan.ai
echo.
echo Thank you for building Al-Bayan Connect!
exit /b 0
