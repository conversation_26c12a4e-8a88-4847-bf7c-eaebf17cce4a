// Al-Bayan Connect - Main Dictation Panel Component
// Author: Dr. <PERSON>smail
// Copyright: © 2025 Al-Bayan AI Platform

import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  <PERSON><PERSON>,
  Text,
  Spinner,
  MessageBar,
  MessageBarType
} from '@fluentui/react-components';
import {
  Mic24Regular,
  MicOff24Regular,
  Settings24Regular,
  Clear24Regular
} from '@fluentui/react-icons';

import {
  DictationState,
  Language,
  SpeechRecognitionResult,
  DictationEventType,
  UserSettings,
  DEFAULT_SETTINGS
} from '@types/index';

import { DictationService } from '@services/DictationService';
import { OfficeIntegrationService } from '@services/OfficeIntegrationService';
import { LanguageSelector } from './LanguageSelector';
import { ResultsDisplay } from './ResultsDisplay';
import { SettingsPanel } from './SettingsPanel';
import { useSettings } from './SettingsProvider';

export const DictationPanel: React.FC = () => {
  const { settings, updateSettings } = useSettings();
  const [dictationState, setDictationState] = useState<DictationState>(DictationState.IDLE);
  const [currentLanguage, setCurrentLanguage] = useState<Language>(Language.AUTO_DETECT);
  const [confidence, setConfidence] = useState<number>(0);
  const [results, setResults] = useState<SpeechRecognitionResult[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [showSettings, setShowSettings] = useState<boolean>(false);
  const [isInitializing, setIsInitializing] = useState<boolean>(true);

  const dictationServiceRef = useRef<DictationService | null>(null);
  const officeServiceRef = useRef<OfficeIntegrationService | null>(null);

  // Initialize services
  useEffect(() => {
    const initializeServices = async () => {
      try {
        setIsInitializing(true);
        
        // Initialize Office integration
        officeServiceRef.current = new OfficeIntegrationService();
        
        // Initialize dictation service
        dictationServiceRef.current = new DictationService(settings);
        
        // Set up event listeners
        setupEventListeners();
        
        setIsInitializing(false);
      } catch (err) {
        setError(`Failed to initialize services: ${err}`);
        setIsInitializing(false);
      }
    };

    initializeServices();

    return () => {
      // Cleanup
      if (dictationServiceRef.current) {
        dictationServiceRef.current.dispose();
      }
    };
  }, []);

  // Setup event listeners for dictation service
  const setupEventListeners = useCallback(() => {
    const service = dictationServiceRef.current;
    if (!service) return;

    service.addEventListener(DictationEventType.STARTED, () => {
      setDictationState(DictationState.LISTENING);
      setError(null);
    });

    service.addEventListener(DictationEventType.STOPPED, () => {
      setDictationState(DictationState.IDLE);
    });

    service.addEventListener(DictationEventType.RESULT, (event) => {
      const result = event.data as SpeechRecognitionResult;
      
      // Update confidence
      setConfidence(result.confidence);
      
      // Add result to list
      if (result.isFinal) {
        setResults(prev => [...prev, result]);
        
        // Insert text into Office document
        if (officeServiceRef.current && result.transcript.trim()) {
          officeServiceRef.current.insertText(result.transcript, result.language);
        }
      }
    });

    service.addEventListener(DictationEventType.LANGUAGE_DETECTED, (event) => {
      const { language } = event.data;
      setCurrentLanguage(language);
    });

    service.addEventListener(DictationEventType.COMMAND_EXECUTED, (event) => {
      const { command } = event.data;
      
      // Execute command through Office integration
      if (officeServiceRef.current) {
        officeServiceRef.current.executeCommand(command);
      }
    });

    service.addEventListener(DictationEventType.ERROR, (event) => {
      const { message } = event.data;
      setError(message);
      setDictationState(DictationState.ERROR);
    });
  }, []);

  // Update services when settings change
  useEffect(() => {
    if (dictationServiceRef.current) {
      dictationServiceRef.current.updateSettings(settings);
    }
  }, [settings]);

  // Handle microphone button click
  const handleMicrophoneClick = useCallback(() => {
    const service = dictationServiceRef.current;
    if (!service) return;

    if (dictationState === DictationState.LISTENING) {
      service.stopDictation();
    } else {
      service.startDictation();
    }
  }, [dictationState]);

  // Handle language change
  const handleLanguageChange = useCallback((language: Language) => {
    setCurrentLanguage(language);
    if (dictationServiceRef.current) {
      dictationServiceRef.current.updateLanguage(language);
    }
  }, []);

  // Clear results
  const handleClearResults = useCallback(() => {
    setResults([]);
    setConfidence(0);
  }, []);

  // Get microphone button props
  const getMicrophoneButtonProps = () => {
    const baseProps = {
      className: `microphone-button ${dictationState}`,
      onClick: handleMicrophoneClick,
      disabled: isInitializing || dictationState === DictationState.ERROR,
      'aria-label': dictationState === DictationState.LISTENING ? 'Stop dictation' : 'Start dictation'
    };

    switch (dictationState) {
      case DictationState.LISTENING:
        return { ...baseProps, children: <MicOff24Regular /> };
      case DictationState.PROCESSING:
        return { ...baseProps, children: <Spinner size="medium" />, disabled: true };
      case DictationState.ERROR:
        return { ...baseProps, children: <MicOff24Regular />, disabled: true };
      default:
        return { ...baseProps, children: <Mic24Regular /> };
    }
  };

  // Get status text
  const getStatusText = () => {
    switch (dictationState) {
      case DictationState.LISTENING:
        return 'Listening... Speak now';
      case DictationState.PROCESSING:
        return 'Processing speech...';
      case DictationState.ERROR:
        return 'Error occurred';
      default:
        return 'Click to start dictation';
    }
  };

  // Get status text in Arabic if current language is Arabic
  const getLocalizedStatusText = () => {
    if (currentLanguage === Language.ARABIC) {
      switch (dictationState) {
        case DictationState.LISTENING:
          return 'جاري الاستماع... تحدث الآن';
        case DictationState.PROCESSING:
          return 'جاري معالجة الكلام...';
        case DictationState.ERROR:
          return 'حدث خطأ';
        default:
          return 'انقر لبدء الإملاء';
      }
    }
    return getStatusText();
  };

  if (isInitializing) {
    return (
      <div className="dictation-panel">
        <div className="loading-overlay">
          <Spinner size="large" />
          <Text>Initializing Al-Bayan Connect...</Text>
        </div>
      </div>
    );
  }

  return (
    <div className="dictation-panel">
      {/* Error Message */}
      {error && (
        <MessageBar
          messageBarType={MessageBarType.error}
          onDismiss={() => setError(null)}
        >
          {error}
        </MessageBar>
      )}

      {/* Main Controls */}
      <div className="dictation-controls">
        {/* Microphone Section */}
        <div className="microphone-section">
          <Button {...getMicrophoneButtonProps()} />
          
          <Text 
            className={`status-text ${dictationState}`}
            style={{ 
              direction: currentLanguage === Language.ARABIC ? 'rtl' : 'ltr',
              fontFamily: currentLanguage === Language.ARABIC ? 
                "'Traditional Arabic', 'Arabic Typesetting', 'Tahoma', sans-serif" : 
                "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif"
            }}
          >
            {getLocalizedStatusText()}
          </Text>
        </div>

        {/* Language Controls */}
        <LanguageSelector
          currentLanguage={currentLanguage}
          onLanguageChange={handleLanguageChange}
          autoDetectionEnabled={settings.autoLanguageDetection}
        />

        {/* Confidence Meter */}
        {dictationState === DictationState.LISTENING && (
          <div className="confidence-meter">
            <Text className="confidence-label">
              Confidence: {Math.round(confidence * 100)}%
            </Text>
            <div className="confidence-bar">
              <div
                className="confidence-fill"
                style={{ width: `${confidence * 100}%` }}
              />
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="quick-actions">
          <Button
            icon={<Clear24Regular />}
            onClick={handleClearResults}
            disabled={results.length === 0}
          >
            Clear Results
          </Button>
          
          <Button
            icon={<Settings24Regular />}
            onClick={() => setShowSettings(!showSettings)}
          >
            Settings
          </Button>
        </div>
      </div>

      {/* Results Display */}
      <ResultsDisplay
        results={results}
        currentLanguage={currentLanguage}
        onClear={handleClearResults}
      />

      {/* Settings Panel */}
      {showSettings && (
        <SettingsPanel
          settings={settings}
          onSettingsChange={updateSettings}
          onClose={() => setShowSettings(false)}
        />
      )}
    </div>
  );
};
