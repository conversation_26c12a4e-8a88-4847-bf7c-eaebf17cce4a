/* <PERSON><PERSON><PERSON><PERSON> Connect - Taskpane Styles */
/* Author: Dr. <PERSON> */
/* Copyright: © 2025 Al-Bayan AI Platform */

.al-bayan-app {
  height: 100%;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Dictation Panel Styles */
.dictation-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 16px;
  gap: 16px;
}

.dictation-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.microphone-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.microphone-button {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.microphone-button.idle {
  background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
  color: white;
}

.microphone-button.listening {
  background: linear-gradient(135deg, #107c10 0%, #0e6e0e 100%);
  color: white;
  animation: pulse 1.5s infinite;
}

.microphone-button.processing {
  background: linear-gradient(135deg, #ff8c00 0%, #e67e00 100%);
  color: white;
}

.microphone-button.error {
  background: linear-gradient(135deg, #d13438 0%, #b52e31 100%);
  color: white;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(16, 124, 16, 0.7);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(16, 124, 16, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(16, 124, 16, 0);
  }
}

.status-text {
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  margin: 0;
}

.status-text.idle {
  color: #323130;
}

.status-text.listening {
  color: #107c10;
}

.status-text.processing {
  color: #ff8c00;
}

.status-text.error {
  color: #d13438;
}

/* Language Controls */
.language-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.language-selector {
  display: flex;
  gap: 8px;
  align-items: center;
}

.language-toggle {
  display: flex;
  background: #f3f2f1;
  border-radius: 20px;
  padding: 2px;
  gap: 2px;
}

.language-option {
  padding: 6px 12px;
  border: none;
  background: transparent;
  border-radius: 18px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.language-option.active {
  background: #0078d4;
  color: white;
}

.language-option:hover:not(.active) {
  background: #e1dfdd;
}

.auto-detect-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #605e5c;
}

.auto-detect-indicator.active {
  color: #107c10;
}

/* Confidence Meter */
.confidence-meter {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.confidence-label {
  font-size: 12px;
  color: #605e5c;
  margin: 0;
}

.confidence-bar {
  height: 6px;
  background: #f3f2f1;
  border-radius: 3px;
  overflow: hidden;
}

.confidence-fill {
  height: 100%;
  background: linear-gradient(90deg, #d13438 0%, #ff8c00 50%, #107c10 100%);
  transition: width 0.3s ease;
  border-radius: 3px;
}

/* Results Display */
.results-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-height: 200px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.results-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #323130;
}

.clear-results-button {
  padding: 4px 8px;
  border: 1px solid #d2d0ce;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  color: #605e5c;
}

.clear-results-button:hover {
  background: #f3f2f1;
}

.results-container {
  flex: 1;
  background: white;
  border: 1px solid #d2d0ce;
  border-radius: 4px;
  padding: 12px;
  overflow-y: auto;
  min-height: 150px;
}

.result-item {
  margin-bottom: 8px;
  padding: 8px;
  border-radius: 4px;
  border-left: 3px solid #0078d4;
  background: #f8f9fa;
}

.result-item.arabic {
  direction: rtl;
  text-align: right;
  font-family: 'Traditional Arabic', 'Arabic Typesetting', 'Tahoma', sans-serif;
  border-left-color: #107c10;
}

.result-item.english {
  direction: ltr;
  text-align: left;
  border-left-color: #0078d4;
}

.result-text {
  font-size: 14px;
  line-height: 1.4;
  margin: 0 0 4px 0;
  color: #323130;
}

.result-meta {
  font-size: 11px;
  color: #605e5c;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-confidence {
  font-weight: 500;
}

.result-confidence.high {
  color: #107c10;
}

.result-confidence.medium {
  color: #ff8c00;
}

.result-confidence.low {
  color: #d13438;
}

/* Quick Actions */
.quick-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.quick-action-button {
  padding: 6px 12px;
  border: 1px solid #d2d0ce;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  color: #323130;
  display: flex;
  align-items: center;
  gap: 4px;
}

.quick-action-button:hover {
  background: #f3f2f1;
  border-color: #0078d4;
}

.quick-action-button:active {
  background: #e1dfdd;
}

/* Settings Panel */
.settings-panel {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;
  margin-top: 16px;
}

.settings-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: #323130;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f3f2f1;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  font-size: 14px;
  color: #323130;
}

.setting-description {
  font-size: 12px;
  color: #605e5c;
  margin-top: 2px;
}

/* Error States */
.error-message {
  background: #fde7e9;
  border: 1px solid #f1aeb5;
  color: #a4262c;
  padding: 12px;
  border-radius: 4px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.error-icon {
  font-size: 16px;
}

/* Loading States */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 12px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f2f1;
  border-top: 3px solid #0078d4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 320px) {
  .dictation-panel {
    padding: 12px;
    gap: 12px;
  }
  
  .microphone-button {
    width: 60px;
    height: 60px;
    font-size: 24px;
  }
  
  .quick-actions {
    flex-direction: column;
  }
  
  .quick-action-button {
    width: 100%;
    justify-content: center;
  }
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus styles */
.microphone-button:focus,
.quick-action-button:focus,
.language-option:focus {
  outline: 2px solid #0078d4;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .dictation-controls,
  .settings-panel {
    border: 2px solid;
  }
  
  .microphone-button {
    border: 2px solid;
  }
}
