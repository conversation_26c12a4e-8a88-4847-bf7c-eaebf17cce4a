# 🚀 Al-Bayan Connect - Setup & Deployment Guide

*"Together, we build a better community for Al-Bayan"*

## 📋 Prerequisites

### System Requirements
- **Windows 10/11** or **macOS 10.14+**
- **Microsoft Office 2016** or later (Word, PowerPoint, Outlook)
- **Node.js 16+** and **npm 8+**
- **Modern web browser** with microphone support
- **Internet connection** (for cloud features)

### Development Tools
- **Visual Studio Code** (recommended)
- **Office Add-in Development Kit** extension
- **Git** for version control

## 🛠️ Installation & Setup

### 1. Clone and Install Dependencies

```bash
# Clone the repository
git clone https://github.com/al-bayan-ai/al-bayan-connect.git
cd "Al-Bayan Connect"

# Install dependencies
npm install

# Install development certificates for HTTPS
npm run install-certs
```

### 2. Development Setup

```bash
# Start development server
npm run dev

# In a separate terminal, start the dev server
npm run dev-server

# Sideload the add-in for testing
npm run sideload
```

### 3. Production Build

```bash
# Create production build
npm run build

# Validate manifest
npm run validate

# Deploy to your hosting service
npm run deploy
```

## 🔧 Configuration

### Environment Variables
Create a `.env` file in the root directory:

```env
# Development
NODE_ENV=development
REACT_APP_API_URL=https://localhost:3000

# Production
# NODE_ENV=production
# REACT_APP_API_URL=https://your-domain.com
# REACT_APP_ANALYTICS_KEY=your-analytics-key
```

### Manifest Configuration
Update `manifest.xml` with your domain:

```xml
<!-- Replace localhost with your production domain -->
<SourceLocation DefaultValue="https://your-domain.com/taskpane.html" />
```

## 📦 Deployment Options

### Option 1: Microsoft AppSource (Recommended)
1. **Prepare for submission:**
   ```bash
   npm run build
   npm run validate
   ```

2. **Package the add-in:**
   - Zip the `dist` folder contents
   - Include the `manifest.xml` file
   - Prepare store assets (icons, screenshots, descriptions)

3. **Submit to AppSource:**
   - Go to [Partner Center](https://partner.microsoft.com/)
   - Create a new Office Add-in submission
   - Upload your package and assets
   - Complete the certification process

### Option 2: Enterprise Deployment
1. **Build and host:**
   ```bash
   npm run build
   # Upload dist folder to your web server
   ```

2. **Deploy via Group Policy:**
   - Use Office Deployment Tool
   - Configure centralized deployment
   - Push to domain computers

3. **Deploy via Microsoft 365 Admin Center:**
   - Upload manifest to admin center
   - Assign to users/groups
   - Monitor deployment status

### Option 3: Manual Sideloading
1. **For individual users:**
   ```bash
   # Share the manifest.xml file
   # Users can sideload via Office > Insert > Add-ins > Upload My Add-in
   ```

## 🧪 Testing

### Unit Tests
```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Watch mode for development
npm run test:watch
```

### Integration Testing
```bash
# Test in different Office applications
npm run sideload

# Test the add-in in:
# - Microsoft Word
# - Microsoft PowerPoint  
# - Microsoft Outlook
```

### Browser Testing
- **Chrome 90+** ✅
- **Edge 90+** ✅
- **Firefox 88+** ✅
- **Safari 14+** ✅

## 🔍 Troubleshooting

### Common Issues

#### 1. Certificate Issues
```bash
# Reinstall certificates
npm run install-certs

# Clear browser cache and restart Office
```

#### 2. Manifest Validation Errors
```bash
# Validate manifest
npm run validate

# Check for common issues:
# - Incorrect URLs
# - Missing required fields
# - Invalid XML syntax
```

#### 3. Speech Recognition Not Working
- **Check microphone permissions** in browser
- **Verify HTTPS** is enabled (required for Web Speech API)
- **Test in supported browsers** (Chrome, Edge recommended)

#### 4. Office Integration Issues
```bash
# Clear Office cache
# Windows: %LOCALAPPDATA%\Microsoft\Office\16.0\Wef\
# macOS: ~/Library/Containers/com.microsoft.*/Data/Library/Caches/
```

### Debug Mode
```bash
# Enable debug logging
localStorage.setItem('al-bayan-debug', 'true');

# Check browser console for detailed logs
# Check Office developer console (F12)
```

## 📊 Monitoring & Analytics

### Built-in Analytics
- **Usage statistics** tracked locally
- **Performance metrics** monitored
- **Error reporting** with stack traces
- **User feedback** collection

### External Analytics (Optional)
```javascript
// Add to your analytics service
window.gtag('config', 'GA_MEASUREMENT_ID');
```

## 🔒 Security & Privacy

### Data Protection
- **Local processing** by default
- **GDPR compliant** data handling
- **User consent** for cloud features
- **Encrypted storage** for sensitive data

### Security Features
- **Content Security Policy** implemented
- **HTTPS only** communication
- **Input validation** and sanitization
- **Regular security audits**

## 🆘 Support & Maintenance

### Getting Help
- **Documentation:** [Wiki](https://github.com/al-bayan-ai/al-bayan-connect/wiki)
- **Issues:** [GitHub Issues](https://github.com/al-bayan-ai/al-bayan-connect/issues)
- **Email:** <EMAIL>
- **Phone:** +249912867327, +966538076790

### Updates
```bash
# Check for updates
npm outdated

# Update dependencies
npm update

# Update Office.js
npm install @microsoft/office-js@latest
```

### Backup & Recovery
```bash
# Backup user data
localStorage.getItem('al-bayan-settings');
localStorage.getItem('al-bayan-custom-commands');
localStorage.getItem('al-bayan-analytics-sessions');
```

## 🎯 Performance Optimization

### Best Practices
- **Lazy loading** for components
- **Code splitting** for better performance
- **Caching strategies** for assets
- **Minification** and compression

### Monitoring
```bash
# Bundle analysis
npm run build
npx webpack-bundle-analyzer dist/static/js/*.js
```

## 📈 Scaling & Growth

### Feature Roadmap
- **Teams integration** (Q2 2025)
- **SharePoint support** (Q3 2025)
- **Mobile app** (Q4 2025)
- **API for third-party integration** (2026)

### Community
- **Contribute:** [Contributing Guidelines](CONTRIBUTING.md)
- **Discussions:** [GitHub Discussions](https://github.com/al-bayan-ai/al-bayan-connect/discussions)
- **Community Forum:** Coming soon!

---

## ✅ Quick Start Checklist

- [ ] Install Node.js 16+ and npm 8+
- [ ] Clone the repository
- [ ] Run `npm install`
- [ ] Run `npm run install-certs`
- [ ] Run `npm run dev` and `npm run dev-server`
- [ ] Run `npm run sideload`
- [ ] Test in Microsoft Word
- [ ] Create your first custom command
- [ ] Check analytics dashboard
- [ ] Ready for production deployment!

**🎉 Congratulations! Al-Bayan Connect is now ready to revolutionize your bilingual productivity!**

---

**© 2025 Dr. Mohammed Yagoub Esmail - Al-Bayan AI Platform**  
*"Empowering bilingual productivity through innovative voice technology"*
