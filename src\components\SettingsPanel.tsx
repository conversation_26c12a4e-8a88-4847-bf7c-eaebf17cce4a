// Al-Bayan Connect - Settings Panel Component
// Author: Dr. <PERSON>il
// Copyright: © 2025 Al-Bayan AI Platform

import React from 'react';
import {
  Button,
  Text,
  Switch,
  Slider,
  Dropdown,
  Option,
  Card,
  CardHeader,
  CardPreview
} from '@fluentui/react-components';
import {
  Dismiss24Regular,
  Save24Regular,
  ArrowReset24Regular
} from '@fluentui/react-icons';

import { UserSettings, Language, PrivacyMode } from '@types/index';

interface SettingsPanelProps {
  settings: UserSettings;
  onSettingsChange: (settings: Partial<UserSettings>) => void;
  onClose: () => void;
}

export const SettingsPanel: React.FC<SettingsPanelProps> = ({
  settings,
  onSettingsChange,
  onClose
}) => {
  const handleLanguageChange = (language: Language) => {
    onSettingsChange({ defaultLanguage: language });
  };

  const handleAutoDetectionChange = (enabled: boolean) => {
    onSetti<PERSON>sChange({ autoLanguageDetection: enabled });
  };

  const handleConfidenceThresholdChange = (value: number) => {
    onSettingsChange({ confidenceThreshold: value / 100 });
  };

  const handleCustomCommandsChange = (enabled: boolean) => {
    onSettingsChange({ enableCustomCommands: enabled });
  };

  const handlePrivacyModeChange = (mode: PrivacyMode) => {
    onSettingsChange({ privacyMode: mode });
  };

  const handleVoiceTrainingChange = (enabled: boolean) => {
    onSettingsChange({ voiceTraining: enabled });
  };

  const handleAnalyticsChange = (enabled: boolean) => {
    onSettingsChange({
      analytics: {
        ...settings.analytics,
        enabled
      }
    });
  };

  const handleShareDataChange = (enabled: boolean) => {
    onSettingsChange({
      analytics: {
        ...settings.analytics,
        shareUsageData: enabled
      }
    });
  };

  const handleRetentionDaysChange = (days: number) => {
    onSettingsChange({
      analytics: {
        ...settings.analytics,
        retentionDays: days
      }
    });
  };

  return (
    <div className="settings-panel">
      <div className="settings-header" style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '16px'
      }}>
        <Text className="settings-title">Settings</Text>
        <Button
          icon={<Dismiss24Regular />}
          appearance="subtle"
          onClick={onClose}
          aria-label="Close settings"
        />
      </div>

      <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
        {/* Language Settings */}
        <Card>
          <CardHeader
            header={<Text weight="semibold">Language Settings</Text>}
            description={<Text size={200}>Configure language detection and preferences</Text>}
          />
          <CardPreview>
            <div style={{ padding: '12px', display: 'flex', flexDirection: 'column', gap: '12px' }}>
              <div className="setting-item">
                <div>
                  <Text className="setting-label">Default Language</Text>
                  <Text className="setting-description" size={200}>
                    Primary language for dictation
                  </Text>
                </div>
                <Dropdown
                  value={settings.defaultLanguage}
                  onOptionSelect={(_, data) => handleLanguageChange(data.optionValue as Language)}
                >
                  <Option value={Language.AUTO_DETECT}>Auto Detect</Option>
                  <Option value={Language.ENGLISH}>English</Option>
                  <Option value={Language.ARABIC}>العربية (Arabic)</Option>
                </Dropdown>
              </div>

              <div className="setting-item">
                <div>
                  <Text className="setting-label">Auto Language Detection</Text>
                  <Text className="setting-description" size={200}>
                    Automatically detect and switch languages
                  </Text>
                </div>
                <Switch
                  checked={settings.autoLanguageDetection}
                  onChange={(_, data) => handleAutoDetectionChange(data.checked)}
                />
              </div>

              <div className="setting-item">
                <div>
                  <Text className="setting-label">
                    Confidence Threshold: {Math.round(settings.confidenceThreshold * 100)}%
                  </Text>
                  <Text className="setting-description" size={200}>
                    Minimum confidence for accepting speech results
                  </Text>
                </div>
                <Slider
                  value={settings.confidenceThreshold * 100}
                  onChange={(_, data) => handleConfidenceThresholdChange(data.value)}
                  min={50}
                  max={95}
                  step={5}
                  style={{ width: '120px' }}
                />
              </div>
            </div>
          </CardPreview>
        </Card>

        {/* Voice Commands */}
        <Card>
          <CardHeader
            header={<Text weight="semibold">Voice Commands</Text>}
            description={<Text size={200}>Configure voice command behavior</Text>}
          />
          <CardPreview>
            <div style={{ padding: '12px', display: 'flex', flexDirection: 'column', gap: '12px' }}>
              <div className="setting-item">
                <div>
                  <Text className="setting-label">Enable Custom Commands</Text>
                  <Text className="setting-description" size={200}>
                    Allow creation and use of custom voice commands
                  </Text>
                </div>
                <Switch
                  checked={settings.enableCustomCommands}
                  onChange={(_, data) => handleCustomCommandsChange(data.checked)}
                />
              </div>

              <div className="setting-item">
                <div>
                  <Text className="setting-label">Voice Training</Text>
                  <Text className="setting-description" size={200}>
                    Improve recognition accuracy through voice training
                  </Text>
                </div>
                <Switch
                  checked={settings.voiceTraining}
                  onChange={(_, data) => handleVoiceTrainingChange(data.checked)}
                />
              </div>
            </div>
          </CardPreview>
        </Card>

        {/* Privacy Settings */}
        <Card>
          <CardHeader
            header={<Text weight="semibold">Privacy & Security</Text>}
            description={<Text size={200}>Control data processing and privacy</Text>}
          />
          <CardPreview>
            <div style={{ padding: '12px', display: 'flex', flexDirection: 'column', gap: '12px' }}>
              <div className="setting-item">
                <div>
                  <Text className="setting-label">Privacy Mode</Text>
                  <Text className="setting-description" size={200}>
                    Choose how your voice data is processed
                  </Text>
                </div>
                <Dropdown
                  value={settings.privacyMode}
                  onOptionSelect={(_, data) => handlePrivacyModeChange(data.optionValue as PrivacyMode)}
                >
                  <Option value={PrivacyMode.LOCAL_ONLY}>Local Only</Option>
                  <Option value={PrivacyMode.HYBRID}>Hybrid</Option>
                  <Option value={PrivacyMode.CLOUD_ENHANCED}>Cloud Enhanced</Option>
                </Dropdown>
              </div>
            </div>
          </CardPreview>
        </Card>

        {/* Analytics Settings */}
        <Card>
          <CardHeader
            header={<Text weight="semibold">Analytics & Usage</Text>}
            description={<Text size={200}>Configure usage tracking and analytics</Text>}
          />
          <CardPreview>
            <div style={{ padding: '12px', display: 'flex', flexDirection: 'column', gap: '12px' }}>
              <div className="setting-item">
                <div>
                  <Text className="setting-label">Enable Analytics</Text>
                  <Text className="setting-description" size={200}>
                    Track usage statistics and performance metrics
                  </Text>
                </div>
                <Switch
                  checked={settings.analytics.enabled}
                  onChange={(_, data) => handleAnalyticsChange(data.checked)}
                />
              </div>

              {settings.analytics.enabled && (
                <>
                  <div className="setting-item">
                    <div>
                      <Text className="setting-label">Share Usage Data</Text>
                      <Text className="setting-description" size={200}>
                        Help improve Al-Bayan Connect by sharing anonymous usage data
                      </Text>
                    </div>
                    <Switch
                      checked={settings.analytics.shareUsageData}
                      onChange={(_, data) => handleShareDataChange(data.checked)}
                    />
                  </div>

                  <div className="setting-item">
                    <div>
                      <Text className="setting-label">
                        Data Retention: {settings.analytics.retentionDays} days
                      </Text>
                      <Text className="setting-description" size={200}>
                        How long to keep analytics data locally
                      </Text>
                    </div>
                    <Dropdown
                      value={settings.analytics.retentionDays.toString()}
                      onOptionSelect={(_, data) => handleRetentionDaysChange(parseInt(data.optionValue as string))}
                    >
                      <Option value="7">7 days</Option>
                      <Option value="30">30 days</Option>
                      <Option value="90">90 days</Option>
                      <Option value="365">1 year</Option>
                    </Dropdown>
                  </div>
                </>
              )}
            </div>
          </CardPreview>
        </Card>

        {/* Action Buttons */}
        <div style={{ 
          display: 'flex', 
          gap: '8px', 
          justifyContent: 'flex-end',
          marginTop: '16px',
          paddingTop: '16px',
          borderTop: '1px solid #d2d0ce'
        }}>
          <Button
            icon={<ArrowReset24Regular />}
            appearance="subtle"
            onClick={() => {
              // Reset to defaults would be handled by parent
              console.log('Reset settings');
            }}
          >
            Reset to Defaults
          </Button>
          
          <Button
            icon={<Save24Regular />}
            appearance="primary"
            onClick={onClose}
          >
            Save & Close
          </Button>
        </div>
      </div>
    </div>
  );
};
