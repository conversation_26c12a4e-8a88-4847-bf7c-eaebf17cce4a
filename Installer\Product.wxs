<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://wixtoolset.org/schemas/v4/wxs"
     xmlns:util="http://wixtoolset.org/schemas/v4/wxs/util"
     xmlns:netfx="http://wixtoolset.org/schemas/v4/wxs/netfx">

  <!-- Product Definition -->
  <Product Id="*" 
           Name="!(loc.ProductName)" 
           Language="!(loc.Language)" 
           Version="*******" 
           Manufacturer="!(loc.Manufacturer)" 
           UpgradeCode="12345678-1234-1234-1234-123456789012">
    
    <!-- Package Information -->
    <Package InstallerVersion="500" 
             Compressed="yes" 
             InstallScope="perUser"
             Description="!(loc.ProductDescription)"
             Comments="!(loc.ProductComments)"
             Keywords="Office,Add-in,Dictation,Arabic,English,Voice,Al-Bayan" />

    <!-- Media and Cabinet -->
    <Media Id="1" Cabinet="AlBayanConnect.cab" EmbedCab="yes" />

    <!-- Upgrade Logic -->
    <MajorUpgrade DowngradeErrorMessage="!(loc.DowngradeError)" 
                  Schedule="afterInstallInitialize" />

    <!-- Properties -->
    <Property Id="ARPPRODUCTICON" Value="ProductIcon" />
    <Property Id="ARPHELPLINK" Value="https://github.com/al-bayan-ai/al-bayan-connect" />
    <Property Id="ARPURLINFOABOUT" Value="https://al-bayan.ai" />
    <Property Id="ARPCONTACT" Value="<EMAIL>" />
    <Property Id="ARPCOMMENTS" Value="!(loc.ProductComments)" />
    <Property Id="ARPNOMODIFY" Value="1" />

    <!-- Silent Installation Properties -->
    <Property Id="LANGUAGE_SELECTION" Value="en" />
    <Property Id="STARTMENU_SHORTCUTS" Value="1" />
    <Property Id="AUTO_UPDATE_ENABLED" Value="1" />
    <Property Id="ENTERPRISE_MODE" Value="0" />
    <Property Id="TELEMETRY_ENABLED" Value="1" />
    <Property Id="CUSTOM_INSTALL_PATH" Value="" />

    <!-- UI Mode Detection -->
    <Property Id="MSIUSEREALADMINDETECTION" Value="1" />
    <Property Id="ALLUSERS" Value="2" />
    <Property Id="MSIINSTALLPERUSER" Value="1" />
    
    <!-- Installation Directory -->
    <Property Id="INSTALLFOLDER">
      <DirectorySearch Id="LocalAppDataSearch" Path="[LocalAppDataFolder]">
        <DirectorySearch Id="AlBayanSearch" Path="Al-Bayan Connect" />
      </DirectorySearch>
    </Property>

    <!-- Office Version Detection -->
    <Property Id="OFFICEVERSION">
      <RegistrySearch Id="OfficeVersionSearch"
                      Root="HKLM"
                      Key="SOFTWARE\Microsoft\Office\ClickToRun\Configuration"
                      Name="VersionToReport"
                      Type="raw" />
    </Property>

    <!-- .NET Framework Detection -->
    <PropertyRef Id="NETFRAMEWORK48" />

    <!-- Custom Actions -->
    <CustomAction Id="CheckPrerequisites"
                  BinaryKey="PrerequisiteChecker"
                  DllEntry="CheckSystemRequirements"
                  Execute="immediate"
                  Return="check" />

    <!-- Auto-Updater Setup Custom Action -->
    <CustomAction Id="SetupAutoUpdater"
                  Script="vbscript"
                  Execute="deferred"
                  Impersonate="yes">
      <![CDATA[
        ' Setup automatic updates
        Dim shell, fso, scriptPath, installPath
        Set shell = CreateObject("WScript.Shell")
        Set fso = CreateObject("Scripting.FileSystemObject")

        installPath = Session.Property("CustomActionData")
        scriptPath = installPath & "Scripts\UpdateScheduler.vbs"

        If fso.FileExists(scriptPath) Then
            shell.Run "cscript.exe """ & scriptPath & """ setup //NoLogo", 0, True
        End If
      ]]>
    </CustomAction>

    <!-- Auto-Updater Removal Custom Action -->
    <CustomAction Id="RemoveAutoUpdater"
                  Script="vbscript"
                  Execute="deferred"
                  Impersonate="yes">
      <![CDATA[
        ' Remove automatic updates
        Dim shell, fso, scriptPath, installPath
        Set shell = CreateObject("WScript.Shell")
        Set fso = CreateObject("Scripting.FileSystemObject")

        installPath = Session.Property("CustomActionData")
        scriptPath = installPath & "Scripts\UpdateScheduler.vbs"

        If fso.FileExists(scriptPath) Then
            shell.Run "cscript.exe """ & scriptPath & """ remove //NoLogo", 0, True
        End If
      ]]>
    </CustomAction>

    <CustomAction Id="RegisterOfficeAddin"
                  Script="vbscript"
                  Execute="deferred"
                  Impersonate="yes">
      <![CDATA[
        ' Office Add-in Registration Script
        Dim shell, fso, manifestPath
        Set shell = CreateObject("WScript.Shell")
        Set fso = CreateObject("Scripting.FileSystemObject")

        manifestPath = Session.Property("CustomActionData")

        ' Create registry entries for Office Add-in trust
        shell.RegWrite "HKCU\Software\Microsoft\Office\16.0\WEF\TrustedCatalogs\{" & CreateObject("Scriptlet.TypeLib").Guid & "}\Id", "{12345678-1234-1234-1234-123456789012}", "REG_SZ"
        shell.RegWrite "HKCU\Software\Microsoft\Office\16.0\WEF\TrustedCatalogs\{" & CreateObject("Scriptlet.TypeLib").Guid & "}\Url", manifestPath, "REG_SZ"
        shell.RegWrite "HKCU\Software\Microsoft\Office\16.0\WEF\TrustedCatalogs\{" & CreateObject("Scriptlet.TypeLib").Guid & "}\Flags", 1, "REG_DWORD"
      ]]>
    </CustomAction>

    <CustomAction Id="UnregisterOfficeAddin"
                  Script="vbscript"
                  Execute="deferred"
                  Impersonate="yes">
      <![CDATA[
        ' Office Add-in Unregistration Script
        Dim shell
        Set shell = CreateObject("WScript.Shell")

        ' Remove registry entries
        On Error Resume Next
        shell.RegDelete "HKCU\Software\Microsoft\Office\16.0\WEF\TrustedCatalogs\"
        On Error GoTo 0
      ]]>
    </CustomAction>

    <!-- Comprehensive Cleanup Custom Action -->
    <CustomAction Id="CompleteCleanup"
                  Script="vbscript"
                  Execute="deferred"
                  Impersonate="yes">
      <![CDATA[
        ' Complete cleanup script execution
        Dim shell, fso, scriptPath, installPath
        Set shell = CreateObject("WScript.Shell")
        Set fso = CreateObject("Scripting.FileSystemObject")

        installPath = Session.Property("CustomActionData")
        scriptPath = installPath & "Scripts\Cleanup.vbs"

        If fso.FileExists(scriptPath) Then
            shell.Run "cscript.exe """ & scriptPath & """ //NoLogo", 0, True
        End If
      ]]>
    </CustomAction>

    <!-- Close Office Applications Before Uninstall -->
    <CustomAction Id="CloseOfficeApps"
                  Script="vbscript"
                  Execute="immediate"
                  Return="ignore">
      <![CDATA[
        ' Close Office applications gracefully
        Dim shell
        Set shell = CreateObject("WScript.Shell")

        On Error Resume Next
        shell.Run "taskkill /IM WINWORD.EXE /T", 0, True
        shell.Run "taskkill /IM POWERPNT.EXE /T", 0, True
        shell.Run "taskkill /IM OUTLOOK.EXE /T", 0, True
        shell.Run "taskkill /IM EXCEL.EXE /T", 0, True
        On Error GoTo 0
      ]]>
    </CustomAction>

    <!-- Installation Sequence -->
    <InstallExecuteSequence>
      <Custom Action="CheckPrerequisites" Before="LaunchConditions">NOT Installed</Custom>
      <Custom Action="RegisterOfficeAddin" After="InstallFiles">NOT Installed</Custom>
      <Custom Action="SetupAutoUpdater" After="RegisterOfficeAddin">NOT Installed AND AUTO_UPDATE_ENABLED="1"</Custom>
      <Custom Action="CloseOfficeApps" Before="RemoveFiles">REMOVE="ALL"</Custom>
      <Custom Action="RemoveAutoUpdater" Before="UnregisterOfficeAddin">REMOVE="ALL"</Custom>
      <Custom Action="UnregisterOfficeAddin" After="RemoveFiles">REMOVE="ALL"</Custom>
      <Custom Action="CompleteCleanup" After="UnregisterOfficeAddin">REMOVE="ALL"</Custom>
    </InstallExecuteSequence>

    <!-- Launch Conditions -->
    <Condition Message="!(loc.OSVersionError)">
      <![CDATA[Installed OR (VersionNT >= 601)]]>
    </Condition>

    <Condition Message="!(loc.OfficeVersionError)">
      <![CDATA[Installed OR OFFICEVERSION]]>
    </Condition>

    <Condition Message="!(loc.NetFrameworkError)">
      <![CDATA[Installed OR NETFRAMEWORK48]]>
    </Condition>

    <!-- Directory Structure -->
    <Directory Id="TARGETDIR" Name="SourceDir">
      <Directory Id="LocalAppDataFolder">
        <Directory Id="INSTALLFOLDER" Name="Al-Bayan Connect">
          <Directory Id="WebAssetsFolder" Name="WebAssets" />
          <Directory Id="ScriptsFolder" Name="Scripts" />
          <Directory Id="LogsFolder" Name="Logs" />
        </Directory>
      </Directory>
      
      <!-- Start Menu -->
      <Directory Id="ProgramMenuFolder">
        <Directory Id="ApplicationProgramsFolder" Name="!(loc.ProductName)" />
      </Directory>
    </Directory>

    <!-- Features -->
    <Feature Id="MainFeature" 
             Title="!(loc.MainFeatureTitle)" 
             Description="!(loc.MainFeatureDescription)"
             Level="1"
             ConfigurableDirectory="INSTALLFOLDER">
      <ComponentRef Id="MainExecutable" />
      <ComponentRef Id="OfficeAddinManifest" />
      <ComponentRef Id="WebAssetsComponent" />
      <ComponentRef Id="StartMenuShortcuts" />
    </Feature>

    <Feature Id="DesktopShortcut" 
             Title="!(loc.DesktopShortcutTitle)" 
             Description="!(loc.DesktopShortcutDescription)"
             Level="1000">
      <ComponentRef Id="DesktopShortcutComponent" />
    </Feature>

    <!-- Icons -->
    <Icon Id="ProductIcon" SourceFile="Resources\icon.ico" />

    <!-- UI Reference -->
    <UIRef Id="AlBayanConnectUI" />

  </Product>
</Wix>
