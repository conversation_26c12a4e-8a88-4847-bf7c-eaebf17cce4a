<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://wixtoolset.org/schemas/v4/wxs"
     xmlns:util="http://wixtoolset.org/schemas/v4/wxs/util"
     xmlns:netfx="http://wixtoolset.org/schemas/v4/wxs/netfx">

  <Fragment>
    
    <!-- Prerequisite Properties -->
    <PropertyGroup>
      
      <!-- Operating System Version Detection -->
      <Property Id="OSVERSION" Secure="yes">
        <DirectorySearch Id="SystemFolderSearch" Path="[SystemFolder]">
          <FileSearch Id="KernelSearch" Name="kernel32.dll" />
        </DirectorySearch>
      </Property>

      <!-- Office Version Detection -->
      <Property Id="OFFICE2016INSTALLED" Secure="yes">
        <RegistrySearch Id="Office2016Search"
                        Root="HKLM"
                        Key="SOFTWARE\Microsoft\Office\16.0\Common\InstallRoot"
                        Name="Path"
                        Type="raw" />
      </Property>

      <Property Id="OFFICE2019INSTALLED" Secure="yes">
        <RegistrySearch Id="Office2019Search"
                        Root="HKLM"
                        Key="SOFTWARE\Microsoft\Office\16.0\Common\InstallRoot"
                        Name="Path"
                        Type="raw" />
      </Property>

      <Property Id="OFFICE365INSTALLED" Secure="yes">
        <RegistrySearch Id="Office365Search"
                        Root="HKLM"
                        Key="SOFTWARE\Microsoft\Office\ClickToRun\Configuration"
                        Name="ProductReleaseIds"
                        Type="raw" />
      </Property>

      <Property Id="OFFICE2013INSTALLED" Secure="yes">
        <RegistrySearch Id="Office2013Search"
                        Root="HKLM"
                        Key="SOFTWARE\Microsoft\Office\15.0\Common\InstallRoot"
                        Name="Path"
                        Type="raw" />
      </Property>

      <!-- .NET Framework Detection -->
      <PropertyRef Id="NETFRAMEWORK48" />
      <PropertyRef Id="NETFRAMEWORK472" />
      <PropertyRef Id="NETFRAMEWORK471" />

      <!-- Windows Version Detection -->
      <Property Id="WINDOWSVERSION" Secure="yes">
        <RegistrySearch Id="WindowsVersionSearch"
                        Root="HKLM"
                        Key="SOFTWARE\Microsoft\Windows NT\CurrentVersion"
                        Name="CurrentVersion"
                        Type="raw" />
      </Property>

      <!-- Memory Detection -->
      <Property Id="PHYSICALMEMORY" Secure="yes">
        <RegistrySearch Id="MemorySearch"
                        Root="HKLM"
                        Key="HARDWARE\RESOURCEMAP\System Resources\Physical Memory"
                        Name=".Translated"
                        Type="raw" />
      </Property>

      <!-- Disk Space Detection -->
      <Property Id="DISKSPACE" Secure="yes" />

      <!-- Browser Detection for Office Online -->
      <Property Id="CHROMEINSTALLED" Secure="yes">
        <RegistrySearch Id="ChromeSearch"
                        Root="HKLM"
                        Key="SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\chrome.exe"
                        Name="Path"
                        Type="raw" />
      </Property>

      <Property Id="EDGEINSTALLED" Secure="yes">
        <RegistrySearch Id="EdgeSearch"
                        Root="HKLM"
                        Key="SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\msedge.exe"
                        Name="Path"
                        Type="raw" />
      </Property>

      <!-- Audio Device Detection -->
      <Property Id="AUDIODEVICES" Secure="yes" />

    </PropertyGroup>

    <!-- Custom Actions for Prerequisite Checking -->
    <Binary Id="PrerequisiteChecker" SourceFile="$(var.SolutionDir)PrerequisiteChecker\bin\Release\PrerequisiteChecker.dll" />

    <CustomAction Id="CheckSystemRequirements"
                  BinaryKey="PrerequisiteChecker"
                  DllEntry="CheckSystemRequirements"
                  Execute="immediate"
                  Return="check" />

    <CustomAction Id="CheckOfficeVersions"
                  BinaryKey="PrerequisiteChecker"
                  DllEntry="CheckOfficeVersions"
                  Execute="immediate"
                  Return="check" />

    <CustomAction Id="CheckNetFramework"
                  BinaryKey="PrerequisiteChecker"
                  DllEntry="CheckNetFramework"
                  Execute="immediate"
                  Return="check" />

    <CustomAction Id="CheckAudioDevices"
                  BinaryKey="PrerequisiteChecker"
                  DllEntry="CheckAudioDevices"
                  Execute="immediate"
                  Return="check" />

    <CustomAction Id="CheckDiskSpace"
                  BinaryKey="PrerequisiteChecker"
                  DllEntry="CheckDiskSpace"
                  Execute="immediate"
                  Return="check" />

    <!-- VBScript Custom Action for Additional Checks -->
    <CustomAction Id="CheckBrowserSupport"
                  Script="vbscript"
                  Execute="immediate"
                  Return="check">
      <![CDATA[
        ' Check for supported browsers for Office Online
        Dim shell, browserFound
        Set shell = CreateObject("WScript.Shell")
        browserFound = False
        
        ' Check for Chrome
        On Error Resume Next
        shell.RegRead "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\chrome.exe\"
        If Err.Number = 0 Then
            browserFound = True
            Session.Property("CHROME_SUPPORTED") = "1"
        End If
        Err.Clear
        
        ' Check for Edge
        shell.RegRead "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\msedge.exe\"
        If Err.Number = 0 Then
            browserFound = True
            Session.Property("EDGE_SUPPORTED") = "1"
        End If
        Err.Clear
        
        ' Check for Firefox
        shell.RegRead "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\firefox.exe\"
        If Err.Number = 0 Then
            browserFound = True
            Session.Property("FIREFOX_SUPPORTED") = "1"
        End If
        Err.Clear
        
        If browserFound Then
            Session.Property("BROWSER_SUPPORTED") = "1"
        Else
            Session.Property("BROWSER_SUPPORTED") = "0"
        End If
      ]]>
    </CustomAction>

    <!-- Launch Conditions -->
    <Condition Message="!(loc.OSVersionError)">
      <![CDATA[Installed OR (VersionNT >= 601)]]>
    </Condition>

    <Condition Message="!(loc.OfficeVersionError)">
      <![CDATA[Installed OR OFFICE2016INSTALLED OR OFFICE2019INSTALLED OR OFFICE365INSTALLED OR OFFICE2013INSTALLED]]>
    </Condition>

    <Condition Message="!(loc.NetFrameworkError)">
      <![CDATA[Installed OR NETFRAMEWORK48 OR NETFRAMEWORK472 OR NETFRAMEWORK471]]>
    </Condition>

    <Condition Message="!(loc.MemoryError)">
      <![CDATA[Installed OR (PhysicalMemory >= 2048)]]>
    </Condition>

    <Condition Message="!(loc.DiskSpaceError)">
      <![CDATA[Installed OR (DISKSPACE >= 100)]]>
    </Condition>

    <!-- Warning Conditions (non-blocking) -->
    <Condition Message="!(loc.BrowserWarning)" Level="200">
      <![CDATA[Installed OR BROWSER_SUPPORTED = "1"]]>
    </Condition>

    <Condition Message="!(loc.AudioWarning)" Level="200">
      <![CDATA[Installed OR AUDIODEVICES <> ""]]>
    </Condition>

  </Fragment>

  <!-- Prerequisite Installation Sequences -->
  <Fragment>
    
    <InstallExecuteSequence>
      <!-- System Requirements Check -->
      <Custom Action="CheckSystemRequirements" Before="LaunchConditions">NOT Installed</Custom>
      <Custom Action="CheckOfficeVersions" After="CheckSystemRequirements">NOT Installed</Custom>
      <Custom Action="CheckNetFramework" After="CheckOfficeVersions">NOT Installed</Custom>
      <Custom Action="CheckAudioDevices" After="CheckNetFramework">NOT Installed</Custom>
      <Custom Action="CheckDiskSpace" After="CheckAudioDevices">NOT Installed</Custom>
      <Custom Action="CheckBrowserSupport" After="CheckDiskSpace">NOT Installed</Custom>
    </InstallExecuteSequence>

    <InstallUISequence>
      <!-- UI Sequence for Prerequisites -->
      <Custom Action="CheckSystemRequirements" Before="LaunchConditions">NOT Installed</Custom>
      <Custom Action="CheckOfficeVersions" After="CheckSystemRequirements">NOT Installed</Custom>
      <Custom Action="CheckNetFramework" After="CheckOfficeVersions">NOT Installed</Custom>
      <Custom Action="CheckAudioDevices" After="CheckNetFramework">NOT Installed</Custom>
      <Custom Action="CheckDiskSpace" After="CheckAudioDevices">NOT Installed</Custom>
      <Custom Action="CheckBrowserSupport" After="CheckDiskSpace">NOT Installed</Custom>
    </InstallUISequence>

  </Fragment>

  <!-- Prerequisite Download and Installation -->
  <Fragment>
    
    <!-- .NET Framework 4.8 Bootstrapper -->
    <util:RegistrySearch Root="HKLM"
                         Key="SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full"
                         Name="Release"
                         Variable="NetFrameworkVersion" />

    <PackageGroup Id="NetFx48">
      <ExePackage Id="NetFx48Exe"
                  Cache="no"
                  Compressed="no"
                  PerMachine="yes"
                  Permanent="yes"
                  Vital="yes"
                  SourceFile="$(var.ResourcesDir)NDP48-x86-x64-AllOS-ENU.exe"
                  DownloadUrl="https://download.microsoft.com/download/6/E/4/6E48E8AB-DC00-419E-9704-06DD46E5F81D/NDP48-x86-x64-AllOS-ENU.exe"
                  InstallCommand="/quiet"
                  DetectCondition="NetFrameworkVersion &gt;= 528040" />
    </PackageGroup>

    <!-- Visual C++ Redistributable -->
    <PackageGroup Id="VCRedist">
      <ExePackage Id="VCRedist2019x64"
                  Cache="no"
                  Compressed="no"
                  PerMachine="yes"
                  Permanent="yes"
                  Vital="yes"
                  SourceFile="$(var.ResourcesDir)VC_redist.x64.exe"
                  DownloadUrl="https://aka.ms/vs/16/release/vc_redist.x64.exe"
                  InstallCommand="/quiet"
                  DetectCondition="VCRedist2019x64Installed" />
    </PackageGroup>

  </Fragment>

  <!-- Prerequisite Validation Component -->
  <Fragment>
    
    <Component Id="PrerequisiteValidation" Directory="INSTALLFOLDER" Guid="12345678-1234-1234-1234-123456789010">
      
      <!-- Store prerequisite check results -->
      <RegistryKey Root="HKCU" Key="Software\Al-Bayan\Connect\Prerequisites">
        <RegistryValue Name="OSVersion" Value="[WINDOWSVERSION]" Type="string" />
        <RegistryValue Name="OfficeInstalled" Value="[OFFICE2016INSTALLED][OFFICE2019INSTALLED][OFFICE365INSTALLED]" Type="string" />
        <RegistryValue Name="NetFramework" Value="[NETFRAMEWORK48]" Type="string" />
        <RegistryValue Name="BrowserSupport" Value="[BROWSER_SUPPORTED]" Type="string" />
        <RegistryValue Name="AudioDevices" Value="[AUDIODEVICES]" Type="string" />
        <RegistryValue Name="CheckDate" Value="[Date]" Type="string" />
      </RegistryKey>

      <!-- Create validation log -->
      <File Id="PrerequisiteLog" 
            Source="$(var.TempDir)prerequisite_check.log" 
            KeyPath="yes" />

    </Component>

  </Fragment>

</Wix>
