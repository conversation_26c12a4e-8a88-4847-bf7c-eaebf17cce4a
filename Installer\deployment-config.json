{"deploymentConfiguration": {"version": "1.0.0", "description": "Al-Bayan Connect Enterprise Deployment Configuration", "lastModified": "2025-01-05"}, "installationSettings": {"installDirectory": "C:\\Program Files\\Al-Bayan Connect", "language": "en", "features": "ALL", "createDesktopShortcut": true, "createStartMenu": true, "enableAutoUpdate": true, "forceInstallation": false}, "enterpriseSettings": {"enterpriseMode": true, "telemetryEnabled": false, "centralManagement": true, "customBranding": false, "restrictedFeatures": []}, "securitySettings": {"requireAdminRights": true, "allowLocalProcessing": true, "allowCloudProcessing": true, "encryptUserData": true, "auditLogging": true}, "networkSettings": {"proxyConfiguration": {"useSystemProxy": true, "customProxy": "", "proxyAuthentication": false}, "allowedDomains": ["*.al-bayan.ai", "*.microsoft.com", "*.office.com"], "blockedDomains": []}, "customProperties": {"ENTERPRISE_MODE": "1", "TELEMETRY_ENABLED": "0", "CENTRAL_MANAGEMENT": "1", "AUDIT_LOGGING": "1", "ALLOW_LOCAL_PROCESSING": "1", "ALLOW_CLOUD_PROCESSING": "1", "ENCRYPT_USER_DATA": "1", "CUSTOM_BRANDING": "0"}, "groupPolicySettings": {"disableUserSettings": false, "lockLanguageSelection": false, "enforceSecurityPolicies": true, "restrictCustomCommands": false, "mandatoryFeatures": ["MainFeature"], "optionalFeatures": ["DesktopShortcut"]}, "updateSettings": {"automaticUpdates": true, "updateChannel": "stable", "updateServer": "https://updates.al-bayan.ai", "checkInterval": "daily", "allowBetaUpdates": false}, "loggingSettings": {"enableDetailedLogging": true, "logLevel": "INFO", "logRetentionDays": 30, "centralLogServer": "", "logCategories": ["Installation", "Usage", "Errors", "Security", "Performance"]}, "supportSettings": {"helpDeskContact": "<EMAIL>", "documentationUrl": "https://docs.al-bayan.ai", "supportTicketUrl": "https://support.al-bayan.ai", "communityForumUrl": "https://community.al-bayan.ai"}, "complianceSettings": {"gdprCompliance": true, "hipaaCompliance": false, "soc2Compliance": true, "dataResidency": "auto", "retentionPolicies": {"userDataRetentionDays": 365, "logRetentionDays": 90, "analyticsRetentionDays": 180}}, "featureFlags": {"enableAnalytics": true, "enableCustomCommands": true, "enableVoiceTraining": true, "enableOfflineMode": true, "enableMultiLanguage": true, "enableAdvancedSecurity": true}, "deploymentProfiles": {"standard": {"description": "Standard deployment for general users", "features": "ALL", "restrictions": [], "customProperties": {"PROFILE_TYPE": "STANDARD"}}, "restricted": {"description": "Restricted deployment for high-security environments", "features": "MainFeature", "restrictions": ["NoCustomCommands", "NoCloudProcessing"], "customProperties": {"PROFILE_TYPE": "RESTRICTED", "ALLOW_CLOUD_PROCESSING": "0", "ALLOW_CUSTOM_COMMANDS": "0"}}, "developer": {"description": "Developer deployment with all features and debugging", "features": "ALL", "restrictions": [], "customProperties": {"PROFILE_TYPE": "DEVELOPER", "DEBUG_MODE": "1", "VERBOSE_LOGGING": "1"}}}, "validationRules": {"requiredWindowsVersion": "10.0", "requiredOfficeVersion": "16.0", "requiredNetFrameworkVersion": "4.7.2", "minimumDiskSpaceMB": 500, "minimumMemoryMB": 2048, "requiredPermissions": ["HKCU_Write", "LocalAppData_Write"]}, "rollbackSettings": {"enableRollback": true, "createRestorePoint": true, "backupUserData": true, "rollbackTimeoutMinutes": 30}}