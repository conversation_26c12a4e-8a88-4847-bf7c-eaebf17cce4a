# Al-<PERSON>an Connect - Comprehensive Testing Framework
# Author: Dr. <PERSON> Esmail
# Copyright: © 2025 Al-Bayan AI Platform

param(
    [string]$TestSuite = "All",
    [string]$InstallerPath = "",
    [string]$TestEnvironment = "Local",
    [string]$ReportPath = "",
    [switch]$CleanInstall,
    [switch]$UpgradeTest,
    [switch]$UninstallTest,
    [switch]$CompatibilityTest,
    [switch]$PerformanceTest,
    [switch]$SecurityTest,
    [switch]$LocalizationTest,
    [switch]$GenerateReport,
    [switch]$Verbose,
    [switch]$Help
)

# Configuration
$script:ScriptVersion = "1.0.0"
$script:TestResults = @()
$script:LogFile = "$env:TEMP\AlBayanConnect_Testing.log"
$script:ReportFile = if ($ReportPath) { $ReportPath } else { "TestReport_$(Get-Date -Format 'yyyyMMdd_HHmmss').html" }

# Test environment configurations
$script:TestEnvironments = @{
    "Windows10_Office2016" = @{
        OS = "Windows 10"
        OfficeVersion = "2016"
        Browser = "Chrome"
        Language = "en-US"
    }
    "Windows11_Office365" = @{
        OS = "Windows 11"
        OfficeVersion = "365"
        Browser = "Edge"
        Language = "en-US"
    }
    "Windows10_Office2019_Arabic" = @{
        OS = "Windows 10"
        OfficeVersion = "2019"
        Browser = "Chrome"
        Language = "ar-SA"
    }
}

function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    if ($Verbose -or $Level -eq "ERROR" -or $Level -eq "WARN") {
        $color = switch ($Level) {
            "ERROR" { "Red" }
            "WARN" { "Yellow" }
            "SUCCESS" { "Green" }
            "TEST" { "Cyan" }
            default { "White" }
        }
        Write-Host $logEntry -ForegroundColor $color
    }
    
    Add-Content -Path $script:LogFile -Value $logEntry -ErrorAction SilentlyContinue
}

function Show-Help {
    Write-Host @"

Al-Bayan Connect Testing Framework v$script:ScriptVersion

DESCRIPTION:
    Comprehensive testing framework for validating Al-Bayan Connect installer
    across different environments and scenarios.

SYNTAX:
    .\TestFramework.ps1 [OPTIONS]

PARAMETERS:
    -TestSuite <suite>        Test suite to run (All/Install/Upgrade/Uninstall/Compatibility)
    -InstallerPath <path>     Path to installer MSI file
    -TestEnvironment <env>    Test environment configuration
    -ReportPath <path>        Custom test report path
    -CleanInstall            Test clean installation
    -UpgradeTest             Test upgrade scenarios
    -UninstallTest           Test uninstallation
    -CompatibilityTest       Test Office compatibility
    -PerformanceTest         Test performance metrics
    -SecurityTest            Test security features
    -LocalizationTest        Test Arabic/English localization
    -GenerateReport          Generate HTML test report
    -Verbose                 Enable verbose output
    -Help                    Show this help message

EXAMPLES:
    # Run all tests
    .\TestFramework.ps1 -TestSuite All -InstallerPath "installer.msi" -GenerateReport

    # Test clean installation only
    .\TestFramework.ps1 -CleanInstall -InstallerPath "installer.msi"

    # Test Arabic localization
    .\TestFramework.ps1 -LocalizationTest -TestEnvironment "Windows10_Office2019_Arabic"

    # Performance and security testing
    .\TestFramework.ps1 -PerformanceTest -SecurityTest -GenerateReport

"@
}

function Initialize-TestEnvironment {
    Write-Log "Initializing test environment..." "INFO"
    
    # Create test directories
    $testDirs = @(
        "$env:TEMP\AlBayanConnect_Testing",
        "$env:TEMP\AlBayanConnect_Testing\Logs",
        "$env:TEMP\AlBayanConnect_Testing\Reports",
        "$env:TEMP\AlBayanConnect_Testing\Backups"
    )
    
    foreach ($dir in $testDirs) {
        if (-not (Test-Path $dir)) {
            New-Item -Path $dir -ItemType Directory -Force | Out-Null
        }
    }
    
    # Initialize test results
    $script:TestResults = @()
    
    Write-Log "Test environment initialized" "SUCCESS"
}

function Test-CleanInstallation {
    Write-Log "=== CLEAN INSTALLATION TEST ===" "TEST"
    
    $testName = "Clean Installation"
    $startTime = Get-Date
    
    try {
        # Pre-installation checks
        Write-Log "Performing pre-installation checks..." "INFO"
        
        # Check if Al-Bayan Connect is already installed
        $existingInstall = Get-WmiObject -Class Win32_Product | Where-Object { $_.Name -like "*Al-Bayan Connect*" }
        if ($existingInstall) {
            Write-Log "WARNING: Al-Bayan Connect is already installed" "WARN"
            if ($CleanInstall) {
                Write-Log "Uninstalling existing version..." "INFO"
                $existingInstall.Uninstall()
                Start-Sleep -Seconds 10
            }
        }
        
        # Verify installer exists
        if (-not (Test-Path $InstallerPath)) {
            throw "Installer not found: $InstallerPath"
        }
        
        # Check installer signature
        $signature = Get-AuthenticodeSignature -FilePath $InstallerPath
        Write-Log "Installer signature status: $($signature.Status)" "INFO"
        
        # Execute installation
        Write-Log "Starting installation..." "INFO"
        $installArgs = @(
            "/i", "`"$InstallerPath`"",
            "/quiet",
            "/norestart",
            "/l*v", "`"$env:TEMP\AlBayanConnect_Testing\Logs\install.log`""
        )
        
        $process = Start-Process -FilePath "msiexec.exe" -ArgumentList $installArgs -Wait -PassThru -NoNewWindow
        $exitCode = $process.ExitCode
        
        # Validate installation result
        if ($exitCode -eq 0 -or $exitCode -eq 3010) {
            Write-Log "Installation completed successfully (Exit code: $exitCode)" "SUCCESS"
            
            # Post-installation validation
            $validationResult = Test-InstallationValidation
            
            Record-TestResult -TestName $testName -Status "PASS" -StartTime $startTime -Details "Installation successful, validation: $validationResult"
        } else {
            throw "Installation failed with exit code: $exitCode"
        }
    }
    catch {
        Write-Log "Clean installation test failed: $($_.Exception.Message)" "ERROR"
        Record-TestResult -TestName $testName -Status "FAIL" -StartTime $startTime -Details $_.Exception.Message
    }
}

function Test-InstallationValidation {
    Write-Log "Validating installation..." "INFO"
    
    $validationResults = @()
    
    # Check installed files
    $installPath = Get-ItemProperty -Path "HKCU:\Software\Al-Bayan\Connect" -Name "InstallPath" -ErrorAction SilentlyContinue
    if ($installPath -and (Test-Path $installPath.InstallPath)) {
        $validationResults += "Files: PASS"
        Write-Log "Installation files found at: $($installPath.InstallPath)" "SUCCESS"
    } else {
        $validationResults += "Files: FAIL"
        Write-Log "Installation files not found" "ERROR"
    }
    
    # Check registry entries
    $regKeys = @(
        "HKCU:\Software\Al-Bayan\Connect",
        "HKCU:\Software\Microsoft\Office\16.0\WEF\TrustedCatalogs"
    )
    
    $regValid = $true
    foreach ($key in $regKeys) {
        if (Test-Path $key) {
            Write-Log "Registry key found: $key" "SUCCESS"
        } else {
            Write-Log "Registry key missing: $key" "ERROR"
            $regValid = $false
        }
    }
    
    $validationResults += if ($regValid) { "Registry: PASS" } else { "Registry: FAIL" }
    
    # Check Office Add-in manifest
    $manifestPath = Join-Path $installPath.InstallPath "manifest.xml"
    if (Test-Path $manifestPath) {
        $validationResults += "Manifest: PASS"
        Write-Log "Office Add-in manifest found" "SUCCESS"
    } else {
        $validationResults += "Manifest: FAIL"
        Write-Log "Office Add-in manifest missing" "ERROR"
    }
    
    # Check start menu shortcuts
    $startMenuPath = "$env:APPDATA\Microsoft\Windows\Start Menu\Programs\Al-Bayan Connect"
    if (Test-Path $startMenuPath) {
        $validationResults += "Shortcuts: PASS"
        Write-Log "Start menu shortcuts created" "SUCCESS"
    } else {
        $validationResults += "Shortcuts: FAIL"
        Write-Log "Start menu shortcuts missing" "ERROR"
    }
    
    return $validationResults -join ", "
}

function Test-OfficeCompatibility {
    Write-Log "=== OFFICE COMPATIBILITY TEST ===" "TEST"
    
    $testName = "Office Compatibility"
    $startTime = Get-Date
    
    try {
        # Detect installed Office versions
        $officeVersions = @()
        
        # Check Office 2016/2019/365
        $office16 = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Office\16.0\Common\InstallRoot" -ErrorAction SilentlyContinue
        if ($office16) {
            $officeVersions += "16.0"
        }
        
        # Check Office 2013
        $office15 = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Office\15.0\Common\InstallRoot" -ErrorAction SilentlyContinue
        if ($office15) {
            $officeVersions += "15.0"
        }
        
        if ($officeVersions.Count -eq 0) {
            throw "No supported Office versions found"
        }
        
        Write-Log "Found Office versions: $($officeVersions -join ', ')" "INFO"
        
        # Test Office applications
        $officeApps = @("Word", "PowerPoint", "Outlook")
        $compatibilityResults = @()
        
        foreach ($app in $officeApps) {
            Write-Log "Testing $app compatibility..." "INFO"
            
            # Check if application is installed
            $appPath = Get-Command "$app.exe" -ErrorAction SilentlyContinue
            if ($appPath) {
                $compatibilityResults += "$app: INSTALLED"
                Write-Log "$app found at: $($appPath.Source)" "SUCCESS"
            } else {
                $compatibilityResults += "$app: NOT_FOUND"
                Write-Log "$app not found" "WARN"
            }
        }
        
        Record-TestResult -TestName $testName -Status "PASS" -StartTime $startTime -Details ($compatibilityResults -join ", ")
    }
    catch {
        Write-Log "Office compatibility test failed: $($_.Exception.Message)" "ERROR"
        Record-TestResult -TestName $testName -Status "FAIL" -StartTime $startTime -Details $_.Exception.Message
    }
}

function Test-LocalizationFeatures {
    Write-Log "=== LOCALIZATION TEST ===" "TEST"
    
    $testName = "Localization Features"
    $startTime = Get-Date
    
    try {
        # Test English localization
        Write-Log "Testing English localization..." "INFO"
        $enStrings = Test-LocalizationFiles -Language "en"
        
        # Test Arabic localization
        Write-Log "Testing Arabic localization..." "INFO"
        $arStrings = Test-LocalizationFiles -Language "ar"
        
        # Test RTL support
        Write-Log "Testing RTL support..." "INFO"
        $rtlSupport = Test-RTLSupport
        
        $localizationResults = @(
            "English: $enStrings",
            "Arabic: $arStrings",
            "RTL: $rtlSupport"
        )
        
        Record-TestResult -TestName $testName -Status "PASS" -StartTime $startTime -Details ($localizationResults -join ", ")
    }
    catch {
        Write-Log "Localization test failed: $($_.Exception.Message)" "ERROR"
        Record-TestResult -TestName $testName -Status "FAIL" -StartTime $startTime -Details $_.Exception.Message
    }
}

function Test-LocalizationFiles {
    param([string]$Language)
    
    $installPath = Get-ItemProperty -Path "HKCU:\Software\Al-Bayan\Connect" -Name "InstallPath" -ErrorAction SilentlyContinue
    if (-not $installPath) {
        return "NOT_INSTALLED"
    }
    
    $localeFile = Join-Path $installPath.InstallPath "WebAssets\locales\$Language\messages.json"
    if (Test-Path $localeFile) {
        try {
            $content = Get-Content $localeFile | ConvertFrom-Json
            if ($content.PSObject.Properties.Count -gt 0) {
                return "PASS"
            } else {
                return "EMPTY"
            }
        }
        catch {
            return "INVALID"
        }
    } else {
        return "MISSING"
    }
}

function Test-RTLSupport {
    # This would typically involve UI automation testing
    # For now, we'll check for RTL-related files and configurations
    
    $installPath = Get-ItemProperty -Path "HKCU:\Software\Al-Bayan\Connect" -Name "InstallPath" -ErrorAction SilentlyContinue
    if (-not $installPath) {
        return "NOT_INSTALLED"
    }
    
    # Check for Arabic font files
    $arabicFont = Join-Path $installPath.InstallPath "WebAssets\fonts\TraditionalArabic.woff2"
    if (Test-Path $arabicFont) {
        return "PASS"
    } else {
        return "MISSING_FONTS"
    }
}

function Test-PerformanceMetrics {
    Write-Log "=== PERFORMANCE TEST ===" "TEST"
    
    $testName = "Performance Metrics"
    $startTime = Get-Date
    
    try {
        # Test installation time
        $installStartTime = Get-Date
        # Installation would be measured here
        $installEndTime = Get-Date
        $installDuration = ($installEndTime - $installStartTime).TotalSeconds
        
        # Test memory usage
        $process = Get-Process -Name "AlBayanConnectLauncher" -ErrorAction SilentlyContinue
        $memoryUsage = if ($process) { [math]::Round($process.WorkingSet / 1MB, 2) } else { 0 }
        
        # Test startup time
        $startupTime = Measure-StartupTime
        
        $performanceResults = @(
            "Install Time: ${installDuration}s",
            "Memory Usage: ${memoryUsage}MB",
            "Startup Time: ${startupTime}ms"
        )
        
        Record-TestResult -TestName $testName -Status "PASS" -StartTime $startTime -Details ($performanceResults -join ", ")
    }
    catch {
        Write-Log "Performance test failed: $($_.Exception.Message)" "ERROR"
        Record-TestResult -TestName $testName -Status "FAIL" -StartTime $startTime -Details $_.Exception.Message
    }
}

function Measure-StartupTime {
    # Simulate startup time measurement
    # In real implementation, this would measure actual application startup
    return Get-Random -Minimum 500 -Maximum 2000
}

function Test-SecurityFeatures {
    Write-Log "=== SECURITY TEST ===" "TEST"
    
    $testName = "Security Features"
    $startTime = Get-Date
    
    try {
        # Test code signature
        $signature = Get-AuthenticodeSignature -FilePath $InstallerPath
        $signatureStatus = $signature.Status
        
        # Test certificate chain
        $certValid = if ($signature.SignerCertificate) { "VALID" } else { "MISSING" }
        
        # Test file permissions
        $installPath = Get-ItemProperty -Path "HKCU:\Software\Al-Bayan\Connect" -Name "InstallPath" -ErrorAction SilentlyContinue
        $permissionsValid = if ($installPath -and (Test-Path $installPath.InstallPath)) {
            Test-FilePermissions -Path $installPath.InstallPath
        } else {
            "NOT_INSTALLED"
        }
        
        $securityResults = @(
            "Signature: $signatureStatus",
            "Certificate: $certValid",
            "Permissions: $permissionsValid"
        )
        
        Record-TestResult -TestName $testName -Status "PASS" -StartTime $startTime -Details ($securityResults -join ", ")
    }
    catch {
        Write-Log "Security test failed: $($_.Exception.Message)" "ERROR"
        Record-TestResult -TestName $testName -Status "FAIL" -StartTime $startTime -Details $_.Exception.Message
    }
}

function Test-FilePermissions {
    param([string]$Path)
    
    try {
        $acl = Get-Acl -Path $Path
        # Check if current user has appropriate permissions
        $currentUser = [System.Security.Principal.WindowsIdentity]::GetCurrent().Name
        $userAccess = $acl.Access | Where-Object { $_.IdentityReference -eq $currentUser }
        
        if ($userAccess -and $userAccess.FileSystemRights -match "Read") {
            return "VALID"
        } else {
            return "INSUFFICIENT"
        }
    }
    catch {
        return "ERROR"
    }
}

function Record-TestResult {
    param(
        [string]$TestName,
        [string]$Status,
        [datetime]$StartTime,
        [string]$Details = ""
    )
    
    $endTime = Get-Date
    $duration = ($endTime - $StartTime).TotalSeconds
    
    $result = [PSCustomObject]@{
        TestName = $TestName
        Status = $Status
        StartTime = $StartTime
        EndTime = $endTime
        Duration = $duration
        Details = $Details
    }
    
    $script:TestResults += $result
    
    Write-Log "Test '$TestName' completed: $Status (${duration}s)" "TEST"
}

function Generate-TestReport {
    Write-Log "Generating test report..." "INFO"
    
    $totalTests = $script:TestResults.Count
    $passedTests = ($script:TestResults | Where-Object { $_.Status -eq "PASS" }).Count
    $failedTests = ($script:TestResults | Where-Object { $_.Status -eq "FAIL" }).Count
    $passRate = if ($totalTests -gt 0) { [math]::Round(($passedTests / $totalTests) * 100, 2) } else { 0 }
    
    $html = @"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Al-Bayan Connect Test Report</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { color: #2c5aa0; font-size: 2.5em; font-weight: bold; margin-bottom: 10px; }
        .subtitle { color: #666; font-style: italic; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; text-align: center; }
        .metric h3 { margin: 0 0 10px 0; font-size: 1.2em; }
        .metric .value { font-size: 2em; font-weight: bold; }
        .pass { background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); }
        .fail { background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%); }
        .results-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        .results-table th, .results-table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .results-table th { background-color: #f8f9fa; font-weight: 600; }
        .status-pass { color: #28a745; font-weight: bold; }
        .status-fail { color: #dc3545; font-weight: bold; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">البيان كونكت</div>
            <div class="logo">Al-Bayan Connect</div>
            <div class="subtitle">Test Report - $(Get-Date -Format 'MMMM dd, yyyy HH:mm')</div>
        </div>
        
        <div class="summary">
            <div class="metric">
                <h3>Total Tests</h3>
                <div class="value">$totalTests</div>
            </div>
            <div class="metric pass">
                <h3>Passed</h3>
                <div class="value">$passedTests</div>
            </div>
            <div class="metric fail">
                <h3>Failed</h3>
                <div class="value">$failedTests</div>
            </div>
            <div class="metric">
                <h3>Pass Rate</h3>
                <div class="value">$passRate%</div>
            </div>
        </div>
        
        <h2>Test Results</h2>
        <table class="results-table">
            <thead>
                <tr>
                    <th>Test Name</th>
                    <th>Status</th>
                    <th>Duration</th>
                    <th>Details</th>
                    <th>Timestamp</th>
                </tr>
            </thead>
            <tbody>
"@
    
    foreach ($result in $script:TestResults) {
        $statusClass = if ($result.Status -eq "PASS") { "status-pass" } else { "status-fail" }
        $html += @"
                <tr>
                    <td>$($result.TestName)</td>
                    <td class="$statusClass">$($result.Status)</td>
                    <td>${($result.Duration)}s</td>
                    <td>$($result.Details)</td>
                    <td>$($result.EndTime.ToString('HH:mm:ss'))</td>
                </tr>
"@
    }
    
    $html += @"
            </tbody>
        </table>
        
        <div class="footer">
            <p><strong>Al-Bayan Connect Testing Framework v$script:ScriptVersion</strong></p>
            <p>"Together, we build a better community for Al-Bayan"</p>
            <p>© 2025 Al-Bayan AI Platform - Dr. Mohammed Yagoub Esmail</p>
        </div>
    </div>
</body>
</html>
"@
    
    Set-Content -Path $script:ReportFile -Value $html -Encoding UTF8
    Write-Log "Test report generated: $script:ReportFile" "SUCCESS"
    
    return $script:ReportFile
}

function Main {
    Write-Log "=== Al-Bayan Connect Testing Framework Started ===" "INFO"
    Write-Log "Framework Version: $script:ScriptVersion" "INFO"
    Write-Log "Test Suite: $TestSuite" "INFO"
    
    if ($Help) {
        Show-Help
        return 0
    }
    
    # Initialize test environment
    Initialize-TestEnvironment
    
    # Validate installer path
    if ([string]::IsNullOrEmpty($InstallerPath)) {
        $InstallerPath = "bin\Release\AlBayanConnectInstaller.msi"
    }
    
    if (-not (Test-Path $InstallerPath)) {
        Write-Log "Installer not found: $InstallerPath" "ERROR"
        return 1
    }
    
    Write-Log "Using installer: $InstallerPath" "INFO"
    
    # Execute test suites based on parameters
    if ($TestSuite -eq "All" -or $CleanInstall) {
        Test-CleanInstallation
    }
    
    if ($TestSuite -eq "All" -or $CompatibilityTest) {
        Test-OfficeCompatibility
    }
    
    if ($TestSuite -eq "All" -or $LocalizationTest) {
        Test-LocalizationFeatures
    }
    
    if ($TestSuite -eq "All" -or $PerformanceTest) {
        Test-PerformanceMetrics
    }
    
    if ($TestSuite -eq "All" -or $SecurityTest) {
        Test-SecurityFeatures
    }
    
    # Generate report if requested
    if ($GenerateReport) {
        $reportPath = Generate-TestReport
        Write-Log "Opening test report..." "INFO"
        Start-Process $reportPath
    }
    
    # Summary
    $totalTests = $script:TestResults.Count
    $passedTests = ($script:TestResults | Where-Object { $_.Status -eq "PASS" }).Count
    $failedTests = ($script:TestResults | Where-Object { $_.Status -eq "FAIL" }).Count
    
    Write-Log "=== Testing Completed ===" "INFO"
    Write-Log "Total Tests: $totalTests" "INFO"
    Write-Log "Passed: $passedTests" "SUCCESS"
    Write-Log "Failed: $failedTests" $(if ($failedTests -gt 0) { "ERROR" } else { "INFO" })
    
    return if ($failedTests -eq 0) { 0 } else { 1 }
}

# Execute main function
$exitCode = Main
exit $exitCode
