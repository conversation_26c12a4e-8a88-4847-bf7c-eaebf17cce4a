// Al-Bayan Connect - Analytics Dashboard Component
// Author: Dr. <PERSON>
// Copyright: © 2025 Al-Bayan AI Platform

import React, { useState } from 'react';
import {
  Card,
  CardHeader,
  CardPreview,
  Text,
  Button,
  Dropdown,
  Option
} from '@fluentui/react-components';
import {
  Calendar24Regular,
  DataUsage24Regular,
  Timer24Regular,
  Trophy24Regular
} from '@fluentui/react-icons';

import { AnalyticsData, Language } from '@types/index';

interface AnalyticsDashboardProps {
  data: AnalyticsData;
  dateRange: { start: Date; end: Date };
  onDateRangeChange: (range: { start: Date; end: Date }) => void;
}

export const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({
  data,
  dateRange,
  onDateRangeChange
}) => {
  const [selectedPeriod, setSelectedPeriod] = useState<string>('30');

  const handlePeriodChange = (period: string) => {
    setSelectedPeriod(period);
    const days = parseInt(period);
    const end = new Date();
    const start = new Date(end.getTime() - days * 24 * 60 * 60 * 1000);
    onDateRangeChange({ start, end });
  };

  const formatDuration = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const mins = Math.round(minutes % 60);
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const getLanguageUsagePercentage = (language: Language): number => {
    const total = Object.values(data.languageUsage).reduce((sum, count) => sum + count, 0);
    return total > 0 ? Math.round((data.languageUsage[language] / total) * 100) : 0;
  };

  return (
    <div style={{ padding: '16px', display: 'flex', flexDirection: 'column', gap: '16px' }}>
      {/* Header with Date Range Selector */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '16px'
      }}>
        <Text size={500} weight="semibold">Analytics Overview</Text>
        
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <Calendar24Regular />
          <Text size={300}>Period:</Text>
          <Dropdown
            value={selectedPeriod}
            onOptionSelect={(_, data) => handlePeriodChange(data.optionValue as string)}
          >
            <Option value="7">Last 7 days</Option>
            <Option value="30">Last 30 days</Option>
            <Option value="90">Last 90 days</Option>
            <Option value="365">Last year</Option>
          </Dropdown>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
        gap: '16px' 
      }}>
        {/* Total Sessions */}
        <Card>
          <CardHeader
            image={<DataUsage24Regular style={{ color: '#0078d4' }} />}
            header={<Text weight="semibold">Total Sessions</Text>}
            description={<Text size={600} weight="bold">{data.totalSessions}</Text>}
          />
        </Card>

        {/* Total Words */}
        <Card>
          <CardHeader
            image={<Timer24Regular style={{ color: '#107c10' }} />}
            header={<Text weight="semibold">Words Dictated</Text>}
            description={<Text size={600} weight="bold">{data.totalWords.toLocaleString()}</Text>}
          />
        </Card>

        {/* Average Accuracy */}
        <Card>
          <CardHeader
            image={<Trophy24Regular style={{ color: '#ff8c00' }} />}
            header={<Text weight="semibold">Average Accuracy</Text>}
            description={
              <Text size={600} weight="bold">
                {Math.round(data.averageAccuracy * 100)}%
              </Text>
            }
          />
        </Card>

        {/* Time Saved */}
        <Card>
          <CardHeader
            image={<Timer24Regular style={{ color: '#d13438' }} />}
            header={<Text weight="semibold">Time Saved</Text>}
            description={
              <Text size={600} weight="bold">
                {formatDuration(data.productivityMetrics.timeSaved)}
              </Text>
            }
          />
        </Card>
      </div>

      {/* Language Usage */}
      <Card>
        <CardHeader
          header={<Text weight="semibold">Language Usage</Text>}
          description={<Text size={200}>Distribution of dictation by language</Text>}
        />
        <CardPreview>
          <div style={{ padding: '16px' }}>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              {/* Arabic Usage */}
              <div>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '4px' }}>
                  <Text>العربية (Arabic)</Text>
                  <Text weight="semibold">{getLanguageUsagePercentage(Language.ARABIC)}%</Text>
                </div>
                <div style={{ 
                  width: '100%', 
                  height: '8px', 
                  backgroundColor: '#f3f2f1', 
                  borderRadius: '4px',
                  overflow: 'hidden'
                }}>
                  <div style={{ 
                    width: `${getLanguageUsagePercentage(Language.ARABIC)}%`, 
                    height: '100%', 
                    backgroundColor: '#107c10',
                    transition: 'width 0.3s ease'
                  }} />
                </div>
              </div>

              {/* English Usage */}
              <div>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '4px' }}>
                  <Text>English</Text>
                  <Text weight="semibold">{getLanguageUsagePercentage(Language.ENGLISH)}%</Text>
                </div>
                <div style={{ 
                  width: '100%', 
                  height: '8px', 
                  backgroundColor: '#f3f2f1', 
                  borderRadius: '4px',
                  overflow: 'hidden'
                }}>
                  <div style={{ 
                    width: `${getLanguageUsagePercentage(Language.ENGLISH)}%`, 
                    height: '100%', 
                    backgroundColor: '#0078d4',
                    transition: 'width 0.3s ease'
                  }} />
                </div>
              </div>
            </div>
          </div>
        </CardPreview>
      </Card>

      {/* Productivity Metrics */}
      <Card>
        <CardHeader
          header={<Text weight="semibold">Productivity Insights</Text>}
          description={<Text size={200}>Your dictation performance metrics</Text>}
        />
        <CardPreview>
          <div style={{ padding: '16px', display: 'flex', flexDirection: 'column', gap: '16px' }}>
            <div style={{ 
              display: 'grid', 
              gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', 
              gap: '16px' 
            }}>
              <div style={{ textAlign: 'center' }}>
                <Text size={400} weight="bold" style={{ display: 'block' }}>
                  {Math.round(data.productivityMetrics.wordsPerMinute)}
                </Text>
                <Text size={200}>Words per minute</Text>
              </div>
              
              <div style={{ textAlign: 'center' }}>
                <Text size={400} weight="bold" style={{ display: 'block' }}>
                  {formatDuration(data.productivityMetrics.timeSaved)}
                </Text>
                <Text size={200}>Time saved vs typing</Text>
              </div>
            </div>

            {data.productivityMetrics.accuracyTrend.length > 0 && (
              <div>
                <Text weight="semibold" style={{ marginBottom: '8px', display: 'block' }}>
                  Recent Accuracy Trend
                </Text>
                <div style={{ 
                  display: 'flex', 
                  alignItems: 'end', 
                  gap: '2px', 
                  height: '60px',
                  padding: '8px',
                  backgroundColor: '#f8f9fa',
                  borderRadius: '4px'
                }}>
                  {data.productivityMetrics.accuracyTrend.slice(-14).map((point, index) => (
                    <div
                      key={index}
                      style={{
                        flex: 1,
                        height: `${point.accuracy * 60}px`,
                        backgroundColor: point.accuracy > 0.8 ? '#107c10' : 
                                       point.accuracy > 0.6 ? '#ff8c00' : '#d13438',
                        borderRadius: '2px',
                        minWidth: '4px'
                      }}
                      title={`${point.date.toLocaleDateString()}: ${Math.round(point.accuracy * 100)}%`}
                    />
                  ))}
                </div>
                <Text size={100} style={{ marginTop: '4px', color: '#605e5c' }}>
                  Last 14 days accuracy trend
                </Text>
              </div>
            )}
          </div>
        </CardPreview>
      </Card>

      {/* Top Commands */}
      {Object.keys(data.commandUsage).length > 0 && (
        <Card>
          <CardHeader
            header={<Text weight="semibold">Most Used Commands</Text>}
            description={<Text size={200}>Your frequently used voice commands</Text>}
          />
          <CardPreview>
            <div style={{ padding: '16px' }}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                {Object.entries(data.commandUsage)
                  .sort(([, a], [, b]) => b - a)
                  .slice(0, 5)
                  .map(([command, usage], index) => (
                    <div key={command} style={{ 
                      display: 'flex', 
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      padding: '8px',
                      backgroundColor: index % 2 === 0 ? '#f8f9fa' : 'transparent',
                      borderRadius: '4px'
                    }}>
                      <Text>{command}</Text>
                      <Text weight="semibold">{usage} times</Text>
                    </div>
                  ))}
              </div>
            </div>
          </CardPreview>
        </Card>
      )}

      {/* No Data Message */}
      {data.totalSessions === 0 && (
        <Card>
          <CardPreview>
            <div style={{ 
              padding: '40px', 
              textAlign: 'center',
              color: '#605e5c'
            }}>
              <Text size={400}>No data available for the selected period</Text>
              <Text size={200} style={{ display: 'block', marginTop: '8px' }}>
                Start using Al-Bayan Connect to see your analytics here!
              </Text>
            </div>
          </CardPreview>
        </Card>
      )}
    </div>
  );
};
