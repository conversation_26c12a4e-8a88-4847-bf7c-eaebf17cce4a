// Al-Bayan Connect - Custom Command Builder Component
// Author: Dr. <PERSON>il
// Copyright: © 2025 Al-Bayan AI Platform

import React, { useState } from 'react';
import {
  Button,
  Text,
  Input,
  Textarea,
  Dropdown,
  Option,
  Switch,
  Card,
  CardHeader,
  CardPreview,
  Dialog,
  DialogTrigger,
  DialogSurface,
  DialogTitle,
  DialogContent,
  DialogBody,
  DialogActions
} from '@fluentui/react-components';
import {
  Add24Regular,
  Edit24Regular,
  Delete24Regular,
  Mic24Regular,
  Save24Regular,
  Dismiss24Regular
} from '@fluentui/react-icons';

import { CustomCommand, Language, CommandCategory, CommandAction } from '@types/index';

interface CustomCommandBuilderProps {
  commands: CustomCommand[];
  onCommandCreate: (command: Omit<CustomCommand, 'id' | 'createdAt' | 'usageCount'>) => void;
  onCommandUpdate: (id: string, command: Partial<CustomCommand>) => void;
  onCommandDelete: (id: string) => void;
}

export const CustomCommandBuilder: React.FC<CustomCommandBuilderProps> = ({
  commands,
  onCommandCreate,
  onCommandUpdate,
  onCommandDelete
}) => {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingCommand, setEditingCommand] = useState<CustomCommand | null>(null);
  const [newCommand, setNewCommand] = useState<Partial<CustomCommand>>({
    phrase: '',
    description: '',
    language: Language.ENGLISH,
    category: CommandCategory.CUSTOM,
    action: CommandAction.INSERT_TEXT,
    isActive: true,
    userId: 'current-user',
    parameters: { action: '' }
  });

  const handleCreateCommand = () => {
    if (!newCommand.phrase || !newCommand.description) return;

    onCommandCreate({
      phrase: newCommand.phrase,
      description: newCommand.description,
      language: newCommand.language || Language.ENGLISH,
      category: newCommand.category || CommandCategory.CUSTOM,
      action: newCommand.action || CommandAction.INSERT_TEXT,
      isActive: newCommand.isActive !== false,
      userId: newCommand.userId || 'current-user',
      parameters: newCommand.parameters || { action: newCommand.phrase },
      isCustom: true
    });

    // Reset form
    setNewCommand({
      phrase: '',
      description: '',
      language: Language.ENGLISH,
      category: CommandCategory.CUSTOM,
      action: CommandAction.INSERT_TEXT,
      isActive: true,
      userId: 'current-user',
      parameters: { action: '' }
    });
    setIsCreateDialogOpen(false);
  };

  const handleEditCommand = (command: CustomCommand) => {
    setEditingCommand(command);
    setNewCommand({ ...command });
  };

  const handleUpdateCommand = () => {
    if (!editingCommand || !newCommand.phrase || !newCommand.description) return;

    onCommandUpdate(editingCommand.id, {
      phrase: newCommand.phrase,
      description: newCommand.description,
      language: newCommand.language,
      category: newCommand.category,
      action: newCommand.action,
      isActive: newCommand.isActive,
      parameters: newCommand.parameters
    });

    setEditingCommand(null);
    setNewCommand({
      phrase: '',
      description: '',
      language: Language.ENGLISH,
      category: CommandCategory.CUSTOM,
      action: CommandAction.INSERT_TEXT,
      isActive: true,
      userId: 'current-user',
      parameters: { action: '' }
    });
  };

  const handleDeleteCommand = (id: string) => {
    if (confirm('Are you sure you want to delete this command?')) {
      onCommandDelete(id);
    }
  };

  const formatUsageCount = (count: number): string => {
    if (count === 0) return 'Never used';
    if (count === 1) return 'Used once';
    return `Used ${count} times`;
  };

  return (
    <div style={{ padding: '16px', display: 'flex', flexDirection: 'column', gap: '16px' }}>
      {/* Header */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center' 
      }}>
        <div>
          <Text size={500} weight="semibold">Custom Voice Commands</Text>
          <Text size={200} style={{ display: 'block', marginTop: '4px', color: '#605e5c' }}>
            Create personalized voice shortcuts for frequently used text and actions
          </Text>
        </div>
        
        <Dialog open={isCreateDialogOpen} onOpenChange={(_, data) => setIsCreateDialogOpen(data.open)}>
          <DialogTrigger disableButtonEnhancement>
            <Button icon={<Add24Regular />} appearance="primary">
              Create Command
            </Button>
          </DialogTrigger>
          <DialogSurface>
            <DialogTitle>Create New Voice Command</DialogTitle>
            <DialogContent>
              <DialogBody>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                  <div>
                    <Text weight="semibold" style={{ marginBottom: '4px', display: 'block' }}>
                      Voice Phrase
                    </Text>
                    <Input
                      value={newCommand.phrase || ''}
                      onChange={(_, data) => setNewCommand(prev => ({ ...prev, phrase: data.value }))}
                      placeholder="e.g., 'insert my signature'"
                    />
                    <Text size={100} style={{ color: '#605e5c', marginTop: '2px' }}>
                      The phrase you'll say to trigger this command
                    </Text>
                  </div>

                  <div>
                    <Text weight="semibold" style={{ marginBottom: '4px', display: 'block' }}>
                      Description
                    </Text>
                    <Input
                      value={newCommand.description || ''}
                      onChange={(_, data) => setNewCommand(prev => ({ ...prev, description: data.value }))}
                      placeholder="Brief description of what this command does"
                    />
                  </div>

                  <div>
                    <Text weight="semibold" style={{ marginBottom: '4px', display: 'block' }}>
                      Action Text
                    </Text>
                    <Textarea
                      value={newCommand.parameters?.action || ''}
                      onChange={(_, data) => setNewCommand(prev => ({ 
                        ...prev, 
                        parameters: { ...prev.parameters, action: data.value }
                      }))}
                      placeholder="The text that will be inserted when you say the phrase"
                      rows={3}
                    />
                  </div>

                  <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px' }}>
                    <div>
                      <Text weight="semibold" style={{ marginBottom: '4px', display: 'block' }}>
                        Language
                      </Text>
                      <Dropdown
                        value={newCommand.language}
                        onOptionSelect={(_, data) => setNewCommand(prev => ({ 
                          ...prev, 
                          language: data.optionValue as Language 
                        }))}
                      >
                        <Option value={Language.ENGLISH}>English</Option>
                        <Option value={Language.ARABIC}>العربية (Arabic)</Option>
                        <Option value={Language.AUTO_DETECT}>Any Language</Option>
                      </Dropdown>
                    </div>

                    <div>
                      <Text weight="semibold" style={{ marginBottom: '4px', display: 'block' }}>
                        Category
                      </Text>
                      <Dropdown
                        value={newCommand.category}
                        onOptionSelect={(_, data) => setNewCommand(prev => ({ 
                          ...prev, 
                          category: data.optionValue as CommandCategory 
                        }))}
                      >
                        <Option value={CommandCategory.CUSTOM}>Custom</Option>
                        <Option value={CommandCategory.FORMATTING}>Formatting</Option>
                        <Option value={CommandCategory.NAVIGATION}>Navigation</Option>
                        <Option value={CommandCategory.EDITING}>Editing</Option>
                      </Dropdown>
                    </div>
                  </div>

                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <Switch
                      checked={newCommand.isActive !== false}
                      onChange={(_, data) => setNewCommand(prev => ({ ...prev, isActive: data.checked }))}
                    />
                    <Text>Enable this command</Text>
                  </div>
                </div>
              </DialogBody>
            </DialogContent>
            <DialogActions>
              <Button 
                appearance="secondary" 
                onClick={() => setIsCreateDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button 
                appearance="primary" 
                icon={<Save24Regular />}
                onClick={handleCreateCommand}
                disabled={!newCommand.phrase || !newCommand.description}
              >
                Create Command
              </Button>
            </DialogActions>
          </DialogSurface>
        </Dialog>
      </div>

      {/* Commands List */}
      <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
        {commands.length === 0 ? (
          <Card>
            <CardPreview>
              <div style={{ 
                padding: '40px', 
                textAlign: 'center',
                color: '#605e5c'
              }}>
                <Mic24Regular style={{ fontSize: '48px', marginBottom: '16px' }} />
                <Text size={400} style={{ display: 'block', marginBottom: '8px' }}>
                  No custom commands yet
                </Text>
                <Text size={200}>
                  Create your first voice command to get started!
                </Text>
              </div>
            </CardPreview>
          </Card>
        ) : (
          commands.map((command) => (
            <Card key={command.id}>
              <CardHeader
                image={<Mic24Regular style={{ color: command.isActive ? '#107c10' : '#605e5c' }} />}
                header={
                  <div>
                    <Text weight="semibold">"{command.phrase}"</Text>
                    <Text size={200} style={{ display: 'block', color: '#605e5c' }}>
                      {command.description}
                    </Text>
                  </div>
                }
                action={
                  <div style={{ display: 'flex', gap: '4px' }}>
                    <Button
                      icon={<Edit24Regular />}
                      size="small"
                      appearance="subtle"
                      onClick={() => handleEditCommand(command)}
                    />
                    <Button
                      icon={<Delete24Regular />}
                      size="small"
                      appearance="subtle"
                      onClick={() => handleDeleteCommand(command.id)}
                    />
                  </div>
                }
              />
              <CardPreview>
                <div style={{ padding: '12px', backgroundColor: '#f8f9fa' }}>
                  <Text size={200} style={{ 
                    fontFamily: 'monospace',
                    display: 'block',
                    marginBottom: '8px',
                    padding: '8px',
                    backgroundColor: 'white',
                    borderRadius: '4px',
                    border: '1px solid #d2d0ce'
                  }}>
                    {command.parameters?.action || command.phrase}
                  </Text>
                  
                  <div style={{ 
                    display: 'flex', 
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    fontSize: '12px',
                    color: '#605e5c'
                  }}>
                    <div>
                      <span>{command.language === Language.ARABIC ? 'عربي' : 
                             command.language === Language.ENGLISH ? 'English' : 'Any'}</span>
                      <span style={{ margin: '0 8px' }}>•</span>
                      <span>{command.category}</span>
                    </div>
                    
                    <div>
                      <span>{formatUsageCount(command.usageCount)}</span>
                      {!command.isActive && (
                        <>
                          <span style={{ margin: '0 8px' }}>•</span>
                          <span style={{ color: '#d13438' }}>Disabled</span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </CardPreview>
            </Card>
          ))
        )}
      </div>

      {/* Edit Dialog */}
      {editingCommand && (
        <Dialog open={true} onOpenChange={() => setEditingCommand(null)}>
          <DialogSurface>
            <DialogTitle>Edit Voice Command</DialogTitle>
            <DialogContent>
              <DialogBody>
                {/* Same form fields as create dialog */}
                <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                  <div>
                    <Text weight="semibold" style={{ marginBottom: '4px', display: 'block' }}>
                      Voice Phrase
                    </Text>
                    <Input
                      value={newCommand.phrase || ''}
                      onChange={(_, data) => setNewCommand(prev => ({ ...prev, phrase: data.value }))}
                    />
                  </div>

                  <div>
                    <Text weight="semibold" style={{ marginBottom: '4px', display: 'block' }}>
                      Description
                    </Text>
                    <Input
                      value={newCommand.description || ''}
                      onChange={(_, data) => setNewCommand(prev => ({ ...prev, description: data.value }))}
                    />
                  </div>

                  <div>
                    <Text weight="semibold" style={{ marginBottom: '4px', display: 'block' }}>
                      Action Text
                    </Text>
                    <Textarea
                      value={newCommand.parameters?.action || ''}
                      onChange={(_, data) => setNewCommand(prev => ({ 
                        ...prev, 
                        parameters: { ...prev.parameters, action: data.value }
                      }))}
                      rows={3}
                    />
                  </div>

                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <Switch
                      checked={newCommand.isActive !== false}
                      onChange={(_, data) => setNewCommand(prev => ({ ...prev, isActive: data.checked }))}
                    />
                    <Text>Enable this command</Text>
                  </div>
                </div>
              </DialogBody>
            </DialogContent>
            <DialogActions>
              <Button 
                appearance="secondary" 
                onClick={() => setEditingCommand(null)}
              >
                Cancel
              </Button>
              <Button 
                appearance="primary" 
                icon={<Save24Regular />}
                onClick={handleUpdateCommand}
              >
                Save Changes
              </Button>
            </DialogActions>
          </DialogSurface>
        </Dialog>
      )}
    </div>
  );
};
