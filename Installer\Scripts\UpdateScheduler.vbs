' Al-Bayan Connect - Update Scheduler Script
' Author: Dr. <PERSON>
' Copyright: © 2025 Al-Bayan AI Platform

Option Explicit

Dim shell, fso, logFile
Dim taskScheduler, taskFolder, taskDefinition, task
Dim updateSettings, installPath

' Initialize objects
Set shell = CreateObject("WScript.Shell")
Set fso = CreateObject("Scripting.FileSystemObject")

' Configuration
logFile = shell.ExpandEnvironmentStrings("%TEMP%") & "\AlBayanConnect_UpdateScheduler.log"

' Main function to setup update scheduling
Function SetupUpdateScheduler()
    WriteLog "=== Al-Bayan Connect Update Scheduler Setup Started ==="
    WriteLog "Date: " & Now()
    WriteLog "Computer: " & shell.ExpandEnvironmentStrings("%COMPUTERNAME%")
    WriteLog "User: " & shell.ExpandEnvironmentStrings("%USERNAME%")
    WriteLog ""
    
    ' Get installation path and settings
    If Not GetInstallationSettings() Then
        WriteLog "Failed to get installation settings"
        SetupUpdateScheduler = 1
        Exit Function
    End If
    
    ' Create or update scheduled task
    If CreateUpdateTask() Then
        WriteLog "Update scheduler setup completed successfully"
        SetupUpdateScheduler = 0
    Else
        WriteLog "Failed to setup update scheduler"
        SetupUpdateScheduler = 1
    End If
End Function

' Get installation settings
Function GetInstallationSettings()
    On Error Resume Next
    
    ' Get installation path from registry
    installPath = shell.RegRead("HKCU\Software\Al-Bayan\Connect\InstallPath")
    
    If Err.Number <> 0 Or installPath = "" Then
        ' Try alternative registry location
        installPath = shell.RegRead("HKLM\Software\Al-Bayan\Connect\InstallPath")
        
        If Err.Number <> 0 Or installPath = "" Then
            ' Fallback to default location
            installPath = shell.ExpandEnvironmentStrings("%LOCALAPPDATA%") & "\Al-Bayan Connect\"
        End If
    End If
    
    WriteLog "Installation path: " & installPath
    
    ' Get update settings
    Set updateSettings = CreateObject("Scripting.Dictionary")
    
    ' Default settings
    updateSettings.Add "Enabled", True
    updateSettings.Add "Channel", "stable"
    updateSettings.Add "CheckInterval", "daily"
    updateSettings.Add "AutoInstall", True
    updateSettings.Add "NotifyUser", True
    updateSettings.Add "UpdateTime", "02:00"
    
    ' Try to read custom settings from registry
    On Error Resume Next
    Dim enabled, channel, interval, autoInstall, notifyUser, updateTime
    
    enabled = shell.RegRead("HKCU\Software\Al-Bayan\Connect\Updates\Enabled")
    If Err.Number = 0 Then updateSettings("Enabled") = CBool(enabled)
    Err.Clear
    
    channel = shell.RegRead("HKCU\Software\Al-Bayan\Connect\Updates\Channel")
    If Err.Number = 0 Then updateSettings("Channel") = channel
    Err.Clear
    
    interval = shell.RegRead("HKCU\Software\Al-Bayan\Connect\Updates\CheckInterval")
    If Err.Number = 0 Then updateSettings("CheckInterval") = interval
    Err.Clear
    
    autoInstall = shell.RegRead("HKCU\Software\Al-Bayan\Connect\Updates\AutoInstall")
    If Err.Number = 0 Then updateSettings("AutoInstall") = CBool(autoInstall)
    Err.Clear
    
    notifyUser = shell.RegRead("HKCU\Software\Al-Bayan\Connect\Updates\NotifyUser")
    If Err.Number = 0 Then updateSettings("NotifyUser") = CBool(notifyUser)
    Err.Clear
    
    updateTime = shell.RegRead("HKCU\Software\Al-Bayan\Connect\Updates\UpdateTime")
    If Err.Number = 0 Then updateSettings("UpdateTime") = updateTime
    Err.Clear
    
    WriteLog "Update settings loaded:"
    WriteLog "  Enabled: " & updateSettings("Enabled")
    WriteLog "  Channel: " & updateSettings("Channel")
    WriteLog "  Check Interval: " & updateSettings("CheckInterval")
    WriteLog "  Auto Install: " & updateSettings("AutoInstall")
    WriteLog "  Notify User: " & updateSettings("NotifyUser")
    WriteLog "  Update Time: " & updateSettings("UpdateTime")
    
    GetInstallationSettings = True
End Function

' Create or update the scheduled task
Function CreateUpdateTask()
    On Error Resume Next
    
    WriteLog "Creating scheduled task for automatic updates..."
    
    ' Connect to Task Scheduler
    Set taskScheduler = CreateObject("Schedule.Service")
    taskScheduler.Connect()
    
    If Err.Number <> 0 Then
        WriteLog "Failed to connect to Task Scheduler: " & Err.Description
        CreateUpdateTask = False
        Exit Function
    End If
    
    ' Get the root task folder
    Set taskFolder = taskScheduler.GetFolder("\")
    
    ' Delete existing task if it exists
    On Error Resume Next
    taskFolder.DeleteTask "Al-Bayan Connect Auto-Updater", 0
    Err.Clear
    
    ' Create new task definition
    Set taskDefinition = taskScheduler.NewTask(0)
    
    ' Set task properties
    With taskDefinition.RegistrationInfo
        .Description = "Automatically checks for and installs Al-Bayan Connect updates"
        .Author = "Al-Bayan AI Platform"
        .Version = "1.0"
        .Documentation = "https://github.com/al-bayan-ai/al-bayan-connect"
    End With
    
    ' Set principal (user context)
    With taskDefinition.Principal
        .LogonType = 3 ' TASK_LOGON_INTERACTIVE_TOKEN
        .RunLevel = 0  ' TASK_RUNLEVEL_LUA (standard user)
    End With
    
    ' Set task settings
    With taskDefinition.Settings
        .Enabled = updateSettings("Enabled")
        .Hidden = False
        .AllowDemandStart = True
        .AllowHardTerminate = True
        .MultipleInstances = 2 ' TASK_INSTANCES_IGNORE_NEW
        .ExecutionTimeLimit = "PT1H" ' 1 hour timeout
        .Priority = 6 ' Normal priority
        .RestartCount = 3
        .RestartInterval = "PT5M" ' 5 minutes
        .RunOnlyIfNetworkAvailable = True
        .WakeToRun = False
    End With
    
    ' Create trigger based on check interval
    Dim trigger
    Select Case LCase(updateSettings("CheckInterval"))
        Case "daily"
            Set trigger = taskDefinition.Triggers.Create(2) ' TASK_TRIGGER_DAILY
            trigger.DaysInterval = 1
        Case "weekly"
            Set trigger = taskDefinition.Triggers.Create(3) ' TASK_TRIGGER_WEEKLY
            trigger.WeeksInterval = 1
            trigger.DaysOfWeek = 2 ' Monday
        Case "monthly"
            Set trigger = taskDefinition.Triggers.Create(4) ' TASK_TRIGGER_MONTHLY
            trigger.MonthsOfYear = 4095 ' All months
            trigger.DaysOfMonth = 1 ' First day of month
        Case Else
            ' Default to daily
            Set trigger = taskDefinition.Triggers.Create(2)
            trigger.DaysInterval = 1
    End Select
    
    ' Set trigger start time
    Dim startTime
    startTime = Date() & " " & updateSettings("UpdateTime") & ":00"
    trigger.StartBoundary = FormatDateTime(CDate(startTime), 3) & "T" & updateSettings("UpdateTime") & ":00"
    trigger.Enabled = True
    
    ' Create action to run PowerShell script
    Dim action
    Set action = taskDefinition.Actions.Create(0) ' TASK_ACTION_EXEC
    action.Path = "powershell.exe"
    
    Dim arguments
    arguments = "-ExecutionPolicy Bypass -WindowStyle Hidden -File """ & installPath & "Scripts\AutoUpdater.ps1"""
    
    If Not updateSettings("AutoInstall") Then
        arguments = arguments & " -CheckOnly"
    End If
    
    arguments = arguments & " -Channel " & updateSettings("Channel")
    arguments = arguments & " -Silent"
    
    action.Arguments = arguments
    action.WorkingDirectory = installPath & "Scripts"
    
    ' Register the task
    On Error Resume Next
    Set task = taskFolder.RegisterTaskDefinition( _
        "Al-Bayan Connect Auto-Updater", _
        taskDefinition, _
        6, _
        , _
        , _
        3 _
    )
    
    If Err.Number <> 0 Then
        WriteLog "Failed to register scheduled task: " & Err.Description
        CreateUpdateTask = False
        Exit Function
    End If
    
    WriteLog "Scheduled task created successfully"
    WriteLog "Task name: Al-Bayan Connect Auto-Updater"
    WriteLog "Next run time: " & task.NextRunTime
    
    ' Create additional task for startup check (if enabled)
    If updateSettings("NotifyUser") Then
        CreateStartupCheckTask()
    End If
    
    CreateUpdateTask = True
End Function

' Create startup check task
Sub CreateStartupCheckTask()
    On Error Resume Next
    
    WriteLog "Creating startup check task..."
    
    ' Delete existing startup task if it exists
    taskFolder.DeleteTask "Al-Bayan Connect Startup Check", 0
    Err.Clear
    
    ' Create new task definition for startup check
    Dim startupTaskDef
    Set startupTaskDef = taskScheduler.NewTask(0)
    
    ' Set task properties
    With startupTaskDef.RegistrationInfo
        .Description = "Checks for Al-Bayan Connect updates at user logon"
        .Author = "Al-Bayan AI Platform"
        .Version = "1.0"
    End With
    
    ' Set principal
    With startupTaskDef.Principal
        .LogonType = 3 ' TASK_LOGON_INTERACTIVE_TOKEN
        .RunLevel = 0  ' TASK_RUNLEVEL_LUA
    End With
    
    ' Set settings
    With startupTaskDef.Settings
        .Enabled = True
        .Hidden = True
        .AllowDemandStart = True
        .MultipleInstances = 2 ' TASK_INSTANCES_IGNORE_NEW
        .ExecutionTimeLimit = "PT10M" ' 10 minutes timeout
        .Priority = 6
        .RunOnlyIfNetworkAvailable = True
        .WakeToRun = False
    End With
    
    ' Create logon trigger
    Dim logonTrigger
    Set logonTrigger = startupTaskDef.Triggers.Create(9) ' TASK_TRIGGER_LOGON
    logonTrigger.Enabled = True
    logonTrigger.Delay = "PT2M" ' Wait 2 minutes after logon
    
    ' Create action for startup check
    Dim startupAction
    Set startupAction = startupTaskDef.Actions.Create(0)
    startupAction.Path = "powershell.exe"
    startupAction.Arguments = "-ExecutionPolicy Bypass -WindowStyle Hidden -File """ & installPath & "Scripts\AutoUpdater.ps1"" -CheckOnly -Channel " & updateSettings("Channel")
    startupAction.WorkingDirectory = installPath & "Scripts"
    
    ' Register startup task
    taskFolder.RegisterTaskDefinition( _
        "Al-Bayan Connect Startup Check", _
        startupTaskDef, _
        6, _
        , _
        , _
        3 _
    )
    
    If Err.Number = 0 Then
        WriteLog "Startup check task created successfully"
    Else
        WriteLog "Failed to create startup check task: " & Err.Description
    End If
End Sub

' Remove update scheduler
Function RemoveUpdateScheduler()
    WriteLog "Removing update scheduler..."
    
    On Error Resume Next
    
    ' Connect to Task Scheduler
    Set taskScheduler = CreateObject("Schedule.Service")
    taskScheduler.Connect()
    
    If Err.Number <> 0 Then
        WriteLog "Failed to connect to Task Scheduler: " & Err.Description
        RemoveUpdateScheduler = False
        Exit Function
    End If
    
    ' Get the root task folder
    Set taskFolder = taskScheduler.GetFolder("\")
    
    ' Delete tasks
    taskFolder.DeleteTask "Al-Bayan Connect Auto-Updater", 0
    If Err.Number = 0 Then
        WriteLog "Removed main update task"
    Else
        WriteLog "Failed to remove main update task: " & Err.Description
    End If
    Err.Clear
    
    taskFolder.DeleteTask "Al-Bayan Connect Startup Check", 0
    If Err.Number = 0 Then
        WriteLog "Removed startup check task"
    Else
        WriteLog "Main update task not found or already removed"
    End If
    
    WriteLog "Update scheduler removal completed"
    RemoveUpdateScheduler = True
End Function

' Utility function to write log
Sub WriteLog(message)
    Dim logFileHandle
    
    On Error Resume Next
    Set logFileHandle = fso.OpenTextFile(logFile, 8, True)
    
    If Err.Number = 0 Then
        logFileHandle.WriteLine Now() & " - " & message
        logFileHandle.Close
    End If
    
    WScript.Echo message
End Sub

' Main execution
Dim result, action

' Check command line arguments
If WScript.Arguments.Count > 0 Then
    action = LCase(WScript.Arguments(0))
Else
    action = "setup"
End If

Select Case action
    Case "setup", "install", "create"
        result = SetupUpdateScheduler()
    Case "remove", "uninstall", "delete"
        result = RemoveUpdateScheduler()
    Case Else
        WScript.Echo "Usage: UpdateScheduler.vbs [setup|remove]"
        WScript.Echo "  setup  - Create or update the scheduled task for automatic updates"
        WScript.Echo "  remove - Remove the scheduled task"
        result = 1
End Select

WScript.Echo ""
WScript.Echo "Update scheduler operation completed. Log file: " & logFile
WScript.Quit result
