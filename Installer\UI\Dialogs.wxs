<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://wixtoolset.org/schemas/v4/wxs">
  
  <Fragment>
    
    <!-- Language Selection Dialog -->
    <Dialog Id="LanguageSelectionDlg" Width="370" Height="270" Title="!(loc.LanguageSelectionTitle)">
      
      <!-- Background -->
      <Control Id="BannerBitmap" Type="Bitmap" X="0" Y="0" Width="370" Height="44" TabSkip="no" Text="!(loc.WixUIBannerBmp)" />
      <Control Id="BannerLine" Type="Line" X="0" Y="44" Width="370" Height="0" />
      <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="370" Height="0" />
      
      <!-- Title and Description -->
      <Control Id="Title" Type="Text" X="15" Y="6" Width="200" Height="15" Transparent="yes" NoPrefix="yes" Text="!(loc.LanguageSelectionTitle)" />
      <Control Id="Description" Type="Text" X="25" Y="23" Width="280" Height="15" Transparent="yes" NoPrefix="yes" Text="!(loc.LanguageSelectionDescription)" />
      
      <!-- Language Selection -->
      <Control Id="LanguageLabel" Type="Text" X="25" Y="60" Width="320" Height="15" NoPrefix="yes" Text="!(loc.SelectLanguageLabel)" />
      
      <!-- English Option -->
      <Control Id="EnglishRadio" Type="RadioButtonGroup" X="25" Y="80" Width="320" Height="40" Property="LANGUAGE_SELECTION" Text="!(loc.LanguageOptions)">
        <RadioButtonGroup Property="LANGUAGE_SELECTION">
          <RadioButton Value="en" X="0" Y="0" Width="150" Height="15" Text="English" />
          <RadioButton Value="ar" X="0" Y="20" Width="150" Height="15" Text="العربية (Arabic)" />
        </RadioButtonGroup>
      </Control>
      
      <!-- Language Description -->
      <Control Id="LanguageDescription" Type="Text" X="25" Y="130" Width="320" Height="40" NoPrefix="yes" Text="!(loc.LanguageSelectionHelp)" />
      
      <!-- Buttons -->
      <Control Id="Next" Type="PushButton" X="236" Y="243" Width="56" Height="17" Default="yes" Text="!(loc.WixUINext)" />
      <Control Id="Cancel" Type="PushButton" X="304" Y="243" Width="56" Height="17" Cancel="yes" Text="!(loc.WixUICancel)">
        <Publish Event="SpawnDialog" Value="CancelDlg">1</Publish>
      </Control>
      
    </Dialog>

    <!-- Custom Welcome Dialog -->
    <Dialog Id="WelcomeDlg" Width="370" Height="270" Title="!(loc.WelcomeTitle)">
      
      <!-- Background -->
      <Control Id="Bitmap" Type="Bitmap" X="0" Y="0" Width="370" Height="234" TabSkip="no" Text="!(loc.WixUIDialogBmp)" />
      
      <!-- Welcome Content -->
      <Control Id="Title" Type="Text" X="135" Y="20" Width="220" Height="60" Transparent="yes" NoPrefix="yes" Text="!(loc.WelcomeDlgTitle)" />
      <Control Id="Description" Type="Text" X="135" Y="70" Width="220" Height="120" Transparent="yes" NoPrefix="yes" Text="!(loc.WelcomeDlgDescription)" />
      
      <!-- Version Information -->
      <Control Id="Version" Type="Text" X="135" Y="190" Width="220" Height="15" Transparent="yes" NoPrefix="yes" Text="!(loc.VersionInfo)" />
      
      <!-- Buttons -->
      <Control Id="Next" Type="PushButton" X="236" Y="243" Width="56" Height="17" Default="yes" Text="!(loc.WixUINext)" />
      <Control Id="Cancel" Type="PushButton" X="304" Y="243" Width="56" Height="17" Cancel="yes" Text="!(loc.WixUICancel)">
        <Publish Event="SpawnDialog" Value="CancelDlg">1</Publish>
      </Control>
      
    </Dialog>

    <!-- License Agreement Dialog -->
    <Dialog Id="LicenseAgreementDlg" Width="370" Height="270" Title="!(loc.LicenseAgreementTitle)">
      
      <!-- Background -->
      <Control Id="BannerBitmap" Type="Bitmap" X="0" Y="0" Width="370" Height="44" TabSkip="no" Text="!(loc.WixUIBannerBmp)" />
      <Control Id="BannerLine" Type="Line" X="0" Y="44" Width="370" Height="0" />
      <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="370" Height="0" />
      
      <!-- Title -->
      <Control Id="Title" Type="Text" X="15" Y="6" Width="200" Height="15" Transparent="yes" NoPrefix="yes" Text="!(loc.LicenseAgreementTitle)" />
      <Control Id="Description" Type="Text" X="25" Y="23" Width="280" Height="15" Transparent="yes" NoPrefix="yes" Text="!(loc.LicenseAgreementDescription)" />
      
      <!-- License Text -->
      <Control Id="LicenseText" Type="ScrollableText" X="20" Y="60" Width="330" Height="140" Sunken="yes" TabSkip="no">
        <Text SourceFile="Resources\license.rtf" />
      </Control>
      
      <!-- Agreement Checkbox -->
      <Control Id="LicenseAcceptedCheckBox" Type="CheckBox" X="20" Y="207" Width="330" Height="18" CheckBoxValue="1" Property="LicenseAccepted" Text="!(loc.LicenseAcceptedText)" />
      
      <!-- Buttons -->
      <Control Id="Back" Type="PushButton" X="180" Y="243" Width="56" Height="17" Text="!(loc.WixUIBack)" />
      <Control Id="Next" Type="PushButton" X="236" Y="243" Width="56" Height="17" Default="yes" Text="!(loc.WixUINext)">
        <Condition Action="disable"><![CDATA[LicenseAccepted <> "1"]]></Condition>
        <Condition Action="enable">LicenseAccepted = "1"</Condition>
      </Control>
      <Control Id="Cancel" Type="PushButton" X="304" Y="243" Width="56" Height="17" Cancel="yes" Text="!(loc.WixUICancel)">
        <Publish Event="SpawnDialog" Value="CancelDlg">1</Publish>
      </Control>
      
    </Dialog>

    <!-- Customize Dialog -->
    <Dialog Id="CustomizeDlg" Width="370" Height="270" Title="!(loc.CustomizeTitle)">
      
      <!-- Background -->
      <Control Id="BannerBitmap" Type="Bitmap" X="0" Y="0" Width="370" Height="44" TabSkip="no" Text="!(loc.WixUIBannerBmp)" />
      <Control Id="BannerLine" Type="Line" X="0" Y="44" Width="370" Height="0" />
      <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="370" Height="0" />
      
      <!-- Title -->
      <Control Id="Title" Type="Text" X="15" Y="6" Width="200" Height="15" Transparent="yes" NoPrefix="yes" Text="!(loc.CustomizeTitle)" />
      <Control Id="Description" Type="Text" X="25" Y="23" Width="280" Height="15" Transparent="yes" NoPrefix="yes" Text="!(loc.CustomizeDescription)" />
      
      <!-- Feature Tree -->
      <Control Id="Tree" Type="SelectionTree" X="25" Y="55" Width="175" Height="95" Property="_BrowseProperty" Sunken="yes" TabSkip="no" Text="Tree of selections" />
      
      <!-- Feature Description -->
      <Control Id="ItemDescription" Type="Text" X="215" Y="55" Width="120" Height="95" Sunken="yes" Transparent="yes" />
      
      <!-- Installation Path -->
      <Control Id="Location" Type="Text" X="25" Y="165" Width="320" Height="20" Text="!(loc.InstallLocationLabel)" />
      <Control Id="Folder" Type="PathEdit" X="25" Y="180" Width="320" Height="18" Property="WIXUI_INSTALLDIR" Indirect="yes" />
      <Control Id="Browse" Type="PushButton" X="304" Y="200" Width="56" Height="17" Text="!(loc.WixUIBrowse)">
        <Publish Event="SpawnDialog" Value="BrowseDlg">1</Publish>
      </Control>
      
      <!-- Disk Cost -->
      <Control Id="DiskCost" Type="PushButton" X="111" Y="243" Width="56" Height="17" Text="!(loc.WixUIDiskCost)">
        <Publish Event="SpawnDialog" Value="DiskCostDlg">1</Publish>
      </Control>
      
      <!-- Buttons -->
      <Control Id="Back" Type="PushButton" X="180" Y="243" Width="56" Height="17" Text="!(loc.WixUIBack)" />
      <Control Id="Next" Type="PushButton" X="236" Y="243" Width="56" Height="17" Default="yes" Text="!(loc.WixUINext)" />
      <Control Id="Cancel" Type="PushButton" X="304" Y="243" Width="56" Height="17" Cancel="yes" Text="!(loc.WixUICancel)">
        <Publish Event="SpawnDialog" Value="CancelDlg">1</Publish>
      </Control>
      
    </Dialog>

    <!-- Verify Ready Dialog -->
    <Dialog Id="VerifyReadyDlg" Width="370" Height="270" Title="!(loc.VerifyReadyTitle)">
      
      <!-- Background -->
      <Control Id="BannerBitmap" Type="Bitmap" X="0" Y="0" Width="370" Height="44" TabSkip="no" Text="!(loc.WixUIBannerBmp)" />
      <Control Id="BannerLine" Type="Line" X="0" Y="44" Width="370" Height="0" />
      <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="370" Height="0" />
      
      <!-- Title -->
      <Control Id="Title" Type="Text" X="15" Y="6" Width="200" Height="15" Transparent="yes" NoPrefix="yes" Text="!(loc.VerifyReadyTitle)" />
      <Control Id="Description" Type="Text" X="25" Y="23" Width="280" Height="15" Transparent="yes" NoPrefix="yes" Text="!(loc.VerifyReadyDescription)" />
      
      <!-- Installation Summary -->
      <Control Id="Text" Type="Text" X="25" Y="70" Width="320" Height="120" Transparent="yes" NoPrefix="yes" Text="!(loc.VerifyReadyText)" />
      
      <!-- Buttons -->
      <Control Id="Back" Type="PushButton" X="180" Y="243" Width="56" Height="17" Text="!(loc.WixUIBack)" />
      <Control Id="Install" Type="PushButton" X="236" Y="243" Width="56" Height="17" Default="yes" Text="!(loc.WixUIInstall)" />
      <Control Id="Cancel" Type="PushButton" X="304" Y="243" Width="56" Height="17" Cancel="yes" Text="!(loc.WixUICancel)">
        <Publish Event="SpawnDialog" Value="CancelDlg">1</Publish>
      </Control>
      
    </Dialog>

  </Fragment>
</Wix>
