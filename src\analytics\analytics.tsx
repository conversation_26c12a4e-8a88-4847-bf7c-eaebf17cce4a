// Al-Bayan Connect - Analytics Dashboard
// Author: Dr. <PERSON>
// Copyright: © 2025 Al-Bayan AI Platform

import React, { useState, useEffect } from 'react';
import ReactDOM from 'react-dom/client';
import { FluentProvider, webLightTheme } from '@fluentui/react-components';
import { AnalyticsDashboard } from '../components/AnalyticsDashboard';
import { ErrorBoundary } from '../components/ErrorBoundary';
import { AnalyticsService } from '../services/AnalyticsService';
import { AnalyticsData } from '@types/index';

const AnalyticsApp: React.FC = () => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [dateRange, setDateRange] = useState<{ start: Date; end: Date }>({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
    end: new Date()
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadAnalyticsData = async () => {
      try {
        setIsLoading(true);
        const analyticsService = new AnalyticsService();
        const data = analyticsService.getAnalyticsData(dateRange);
        setAnalyticsData(data);
      } catch (error) {
        console.error('Failed to load analytics data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadAnalyticsData();
  }, [dateRange]);

  const handleDateRangeChange = (newRange: { start: Date; end: Date }) => {
    setDateRange(newRange);
  };

  if (isLoading || !analyticsData) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        flexDirection: 'column',
        gap: '16px'
      }}>
        <div className="loading-spinner" />
        <div>Loading analytics data...</div>
      </div>
    );
  }

  return (
    <FluentProvider theme={webLightTheme}>
      <ErrorBoundary>
        <AnalyticsDashboard
          data={analyticsData}
          dateRange={dateRange}
          onDateRangeChange={handleDateRangeChange}
        />
      </ErrorBoundary>
    </FluentProvider>
  );
};

// Initialize the React app when Office is ready
Office.onReady(() => {
  const container = document.getElementById('analytics-root');
  if (container) {
    const root = ReactDOM.createRoot(container);
    root.render(<AnalyticsApp />);
  }
});
