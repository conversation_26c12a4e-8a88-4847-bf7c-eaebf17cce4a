# Al-Bayan Connect Installer

*"Together, we build a better community for Al-Bayan"*

This directory contains the complete installer solution for Al-Bayan Connect, a revolutionary bilingual dictation add-in for Microsoft Office.

## 🚀 Quick Start

### Prerequisites
- Windows 10 or later
- Visual Studio 2019/2022 or Build Tools
- WiX Toolset v4.0
- .NET Framework 4.8 or later
- PowerShell 5.0 or later

### Basic Build
```bash
# Simple build
build.bat

# Release build with signing and packaging
build.bat /config Release /sign /package
```

## 📁 Directory Structure

```
Al-Bayan Connect/Installer/
├── AlBayanConnectInstaller.wixproj    # Main WiX project file
├── Product.wxs                        # Product definition and main installer logic
├── build.bat                          # Master build script
├── build-signed.bat                   # Signed build script
├── version.txt                        # Version information
├── deployment-config.json             # Enterprise deployment configuration
├── Components/                        # WiX component definitions
│   ├── OfficeAddin.wxs               # Office Add-in registration
│   ├── WebAssets.wxs                 # Web application assets
│   └── Prerequisites.wxs             # System requirements
├── UI/                               # Installer user interface
│   ├── InstallerUI.wxs              # Main UI definition
│   └── Dialogs.wxs                  # Custom dialogs
├── Localization/                     # Multi-language support
│   ├── Strings_en.wxl               # English strings
│   └── Strings_ar.wxl               # Arabic strings
├── Scripts/                         # Automation and deployment scripts
│   ├── BuildInstaller.ps1           # PowerShell build script
│   ├── CodeSigning.ps1              # Code signing automation
│   ├── CertificateValidation.vbs    # Certificate validation
│   ├── AutoUpdater.ps1              # Auto-update system
│   ├── UpdateScheduler.vbs          # Update scheduling
│   ├── EnterpriseDeployment.ps1     # Enterprise deployment
│   ├── SilentInstall.bat            # Silent installation
│   ├── OfficeRegistration.vbs       # Office Add-in registration
│   ├── PrerequisiteCheck.vbs        # System requirements check
│   └── Cleanup.vbs                  # Uninstallation cleanup
└── Resources/                       # Static resources
    ├── banner.bmp                   # Installer banner
    ├── dialog.bmp                   # Dialog background
    ├── icon.ico                     # Application icon
    └── license.rtf                  # License agreement
```

## 🔧 Build Options

### Configuration Options
- **Debug**: Development build with debugging symbols
- **Release**: Production build optimized for distribution

### Platform Options
- **x86**: 32-bit (recommended for maximum compatibility)
- **x64**: 64-bit
- **AnyCPU**: Platform-agnostic

### Build Features
- **Code Signing**: Digital signature for security and trust
- **Validation**: Comprehensive installer validation
- **Packaging**: Distribution package creation
- **Clean Build**: Remove previous build artifacts

## 🔐 Code Signing

### Certificate Configuration
Set environment variables for automatic signing:

```bash
# For certificate store
set CODE_SIGNING_CERT_THUMBPRINT=1234567890ABCDEF...

# For certificate file
set CODE_SIGNING_CERT_PATH=path\to\certificate.pfx
set CODE_SIGNING_CERT_PASSWORD=your_password
```

### Manual Signing
```powershell
# Sign with certificate store
Scripts\CodeSigning.ps1 -FilePath "installer.msi" -CertificateThumbprint "1234567890ABCDEF"

# Sign with certificate file
Scripts\CodeSigning.ps1 -FilePath "installer.msi" -CertificatePath "cert.pfx" -CertificatePassword "password"

# Verify signature
Scripts\CodeSigning.ps1 -FilePath "installer.msi" -Verify
```

## 🏢 Enterprise Deployment

### Silent Installation
```bash
# Basic silent install
Scripts\SilentInstall.bat

# Custom installation directory
Scripts\SilentInstall.bat /INSTALLDIR "C:\Program Files\Al-Bayan Connect"

# Arabic language with no desktop shortcut
Scripts\SilentInstall.bat /LANGUAGE ar /NODESKTOP
```

### PowerShell Deployment
```powershell
# Basic deployment
Scripts\EnterpriseDeployment.ps1

# Custom configuration
Scripts\EnterpriseDeployment.ps1 -ConfigFile "deployment-config.json"

# Test deployment (WhatIf mode)
Scripts\EnterpriseDeployment.ps1 -WhatIf -Verbose
```

### Group Policy Deployment
1. Copy the MSI to a network share
2. Create a Group Policy Object (GPO)
3. Navigate to Computer Configuration > Software Settings > Software Installation
4. Add the MSI package
5. Configure deployment options

## 🔄 Auto-Updates

### Setup Auto-Updates
```vbscript
' Enable automatic updates
cscript Scripts\UpdateScheduler.vbs setup

' Remove automatic updates
cscript Scripts\UpdateScheduler.vbs remove
```

### Manual Update Check
```powershell
# Check for updates only
Scripts\AutoUpdater.ps1 -CheckOnly

# Update from beta channel
Scripts\AutoUpdater.ps1 -Channel beta

# Force update
Scripts\AutoUpdater.ps1 -Force
```

## 🧪 Testing and Validation

### Prerequisites Check
```vbscript
' Check system requirements
cscript Scripts\PrerequisiteCheck.vbs
```

### Certificate Validation
```vbscript
' Validate installer certificates
cscript Scripts\CertificateValidation.vbs installer.msi
```

### Installation Testing
1. **Clean System Testing**: Test on fresh Windows installations
2. **Upgrade Testing**: Test upgrades from previous versions
3. **Uninstall Testing**: Verify complete removal
4. **Office Compatibility**: Test with different Office versions
5. **Multi-Language Testing**: Test Arabic and English interfaces

## 🌐 Localization

### Supported Languages
- **English (en-US)**: Primary language
- **Arabic (ar-SA)**: Full RTL support

### Adding New Languages
1. Create new `.wxl` file in `Localization/` directory
2. Translate all string resources
3. Update `Product.wxs` to reference new localization
4. Test installer in new language

## 📋 Build Logs

Build logs are automatically generated:
- `%TEMP%\AlBayanConnect_Build.log` - Main build log
- `%TEMP%\AlBayanConnect_Build_msbuild.log` - MSBuild detailed log
- `%TEMP%\AlBayanConnect_PrerequisiteCheck.log` - Prerequisites check
- `%TEMP%\AlBayanConnect_CertValidation.log` - Certificate validation

## 🚨 Troubleshooting

### Common Build Issues

**WiX Toolset not found**
```bash
# Install WiX Toolset v4.0
# Add to PATH: C:\Program Files (x86)\WiX Toolset v4.0\bin
```

**MSBuild not found**
```bash
# Install Visual Studio Build Tools
# Or add to PATH: C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin
```

**Code signing fails**
```bash
# Check certificate validity
Scripts\CodeSigning.ps1 -FilePath "test.exe" -Verify

# Validate certificate configuration
Scripts\CertificateValidation.vbs
```

**Office registration fails**
```bash
# Check Office installation
Scripts\PrerequisiteCheck.vbs

# Manual registration
cscript Scripts\OfficeRegistration.vbs register
```

### Support Resources
- **GitHub Issues**: https://github.com/al-bayan-ai/al-bayan-connect/issues
- **Documentation**: https://docs.al-bayan.ai
- **Community Forum**: https://community.al-bayan.ai
- **Email Support**: <EMAIL>

## 📄 License

Copyright © 2025 Al-Bayan AI Platform - Dr. Mohammed Yagoub Esmail

This installer is part of the Al-Bayan Connect project. See the main LICENSE file for details.

## 🤝 Contributing

We welcome contributions to improve the installer:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

### Development Guidelines
- Follow WiX best practices
- Maintain bilingual support
- Test on multiple Windows versions
- Document all changes
- Update version numbers appropriately

---

**"Together, we build a better community for Al-Bayan"**

For the latest updates and documentation, visit: https://al-bayan.ai
