<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://wixtoolset.org/schemas/v4/wxs">
  
  <Fragment>
    <!-- Custom UI Definition -->
    <UI Id="AlBayanConnectUI">
      
      <!-- Text Style Definitions -->
      <TextStyle Id="WixUI_Font_Normal" FaceName="Tahoma" Size="8" />
      <TextStyle Id="WixUI_Font_Bigger" FaceName="Tahoma" Size="12" />
      <TextStyle Id="WixUI_Font_Title" FaceName="Tahoma" Size="9" Bold="yes" />
      <TextStyle Id="WixUI_Font_Arabic" FaceName="Traditional Arabic" Size="10" />
      <TextStyle Id="WixUI_Font_Arabic_Title" FaceName="Traditional Arabic" Size="12" Bold="yes" />

      <!-- Property Definitions -->
      <Property Id="DefaultUIFont" Value="WixUI_Font_Normal" />
      <Property Id="WixUI_Mode" Value="FeatureTree" />
      <Property Id="ARPNOMODIFY" Value="1" />
      <Property Id="LANGUAGE_SELECTION" Value="en" />

      <!-- Dialog References -->
      <DialogRef Id="BrowseDlg" />
      <DialogRef Id="DiskCostDlg" />
      <DialogRef Id="ErrorDlg" />
      <DialogRef Id="FatalError" />
      <DialogRef Id="FilesInUse" />
      <DialogRef Id="MsiRMFilesInUse" />
      <DialogRef Id="PrepareDlg" />
      <DialogRef Id="ProgressDlg" />
      <DialogRef Id="ResumeDlg" />
      <DialogRef Id="UserExit" />
      
      <!-- Custom Dialogs -->
      <DialogRef Id="LanguageSelectionDlg" />
      <DialogRef Id="WelcomeDlg" />
      <DialogRef Id="LicenseAgreementDlg" />
      <DialogRef Id="CustomizeDlg" />
      <DialogRef Id="VerifyReadyDlg" />
      <DialogRef Id="MaintenanceWelcomeDlg" />
      <DialogRef Id="MaintenanceTypeDlg" />

      <!-- UI Flow -->
      <Publish Dialog="BrowseDlg" Control="OK" Event="DoAction" Value="WixUIValidatePath" Order="3">1</Publish>
      <Publish Dialog="BrowseDlg" Control="OK" Event="SpawnDialog" Value="InvalidDirDlg" Order="4"><![CDATA[WIXUI_INSTALLDIR_VALID<>"1"]]></Publish>

      <Publish Dialog="ExitDialog" Control="Finish" Event="EndDialog" Value="Return" Order="999">1</Publish>

      <!-- Language Selection Flow -->
      <Publish Dialog="LanguageSelectionDlg" Control="Next" Event="NewDialog" Value="WelcomeDlg">1</Publish>
      <Publish Dialog="LanguageSelectionDlg" Control="Cancel" Event="SpawnDialog" Value="CancelDlg">1</Publish>

      <!-- Welcome Dialog Flow -->
      <Publish Dialog="WelcomeDlg" Control="Next" Event="NewDialog" Value="LicenseAgreementDlg">NOT Installed</Publish>
      <Publish Dialog="WelcomeDlg" Control="Next" Event="NewDialog" Value="VerifyReadyDlg">Installed AND PATCH</Publish>

      <!-- License Agreement Flow -->
      <Publish Dialog="LicenseAgreementDlg" Control="Back" Event="NewDialog" Value="WelcomeDlg">1</Publish>
      <Publish Dialog="LicenseAgreementDlg" Control="Next" Event="NewDialog" Value="CustomizeDlg">LicenseAccepted = "1"</Publish>

      <!-- Customize Dialog Flow -->
      <Publish Dialog="CustomizeDlg" Control="Back" Event="NewDialog" Value="MaintenanceTypeDlg" Order="1">Installed</Publish>
      <Publish Dialog="CustomizeDlg" Control="Back" Event="NewDialog" Value="LicenseAgreementDlg" Order="2">NOT Installed</Publish>
      <Publish Dialog="CustomizeDlg" Control="Next" Event="NewDialog" Value="VerifyReadyDlg">1</Publish>

      <!-- Verify Ready Dialog Flow -->
      <Publish Dialog="VerifyReadyDlg" Control="Back" Event="NewDialog" Value="CustomizeDlg" Order="1">NOT Installed OR WixUI_InstallMode = "Change"</Publish>
      <Publish Dialog="VerifyReadyDlg" Control="Back" Event="NewDialog" Value="MaintenanceTypeDlg" Order="2">Installed AND NOT PATCH</Publish>
      <Publish Dialog="VerifyReadyDlg" Control="Back" Event="NewDialog" Value="WelcomeDlg" Order="3">Installed AND PATCH</Publish>

      <!-- Maintenance Dialogs -->
      <Publish Dialog="MaintenanceWelcomeDlg" Control="Next" Event="NewDialog" Value="MaintenanceTypeDlg">1</Publish>
      <Publish Dialog="MaintenanceTypeDlg" Control="RepairButton" Event="NewDialog" Value="VerifyReadyDlg">1</Publish>
      <Publish Dialog="MaintenanceTypeDlg" Control="RemoveButton" Event="NewDialog" Value="VerifyReadyDlg">1</Publish>
      <Publish Dialog="MaintenanceTypeDlg" Control="Back" Event="NewDialog" Value="MaintenanceWelcomeDlg">1</Publish>

      <!-- Error Handling -->
      <Property Id="WIXUI_EXITDIALOGOPTIONALTEXT" Value="!(loc.ExitDialogOptionalText)" />
      <Property Id="WIXUI_EXITDIALOGOPTIONALCHECKBOXTEXT" Value="!(loc.LaunchApplication)" />
      <Property Id="WIXUI_EXITDIALOGOPTIONALCHECKBOX" Value="1" />

      <!-- Custom Actions for UI -->
      <CustomAction Id="LaunchApplication" 
                    BinaryKey="WixCA" 
                    DllEntry="WixShellExec" 
                    Execute="immediate" 
                    Return="ignore" />

      <!-- UI Sequence -->
      <InstallUISequence>
        <Show Dialog="LanguageSelectionDlg" Before="WelcomeDlg">NOT Installed</Show>
        <Show Dialog="WelcomeDlg" Before="ProgressDlg">NOT Installed</Show>
        <Show Dialog="ProgressDlg" After="WelcomeDlg" />
        <Show Dialog="ExitDialog" After="ProgressDlg" />
        <Show Dialog="MaintenanceWelcomeDlg" Before="ProgressDlg">Installed</Show>
      </InstallUISequence>

      <!-- Admin UI Sequence -->
      <AdminUISequence>
        <Show Dialog="WelcomeDlg" Before="ProgressDlg" />
        <Show Dialog="ProgressDlg" After="WelcomeDlg" />
        <Show Dialog="ExitDialog" After="ProgressDlg" />
      </AdminUISequence>

    </UI>

    <!-- UI Extension -->
    <UIRef Id="WixUI_Common" />

  </Fragment>
</Wix>
