// Al-<PERSON>an Connect - Analytics Service
// Author: Dr. <PERSON>
// Copyright: © 2025 Al-Bayan AI Platform

import {
  DictationSession,
  SpeechRecognitionResult,
  Language,
  AnalyticsData,
  ProductivityMetrics,
  UserSettings,
  VoiceCommand
} from '@types/index';

interface SessionData {
  startTime: Date;
  endTime: Date;
  results: SpeechRecognitionResult[];
  language: Language;
  commands?: VoiceCommand[];
}

export class AnalyticsService {
  private sessions: DictationSession[] = [];
  private settings: UserSettings | null = null;
  private currentSessionId: string | null = null;

  constructor() {
    this.loadStoredData();
  }

  public recordSession(sessionData: SessionData): void {
    if (!this.settings?.analytics.enabled) {
      return;
    }

    const session: DictationSession = {
      id: this.generateSessionId(),
      startTime: sessionData.startTime,
      endTime: sessionData.endTime,
      duration: sessionData.endTime.getTime() - sessionData.startTime.getTime(),
      wordsCount: this.calculateWordsCount(sessionData.results),
      accuracy: this.calculateAccuracy(sessionData.results),
      language: sessionData.language,
      commands: sessionData.commands || [],
      errors: []
    };

    this.sessions.push(session);
    this.saveData();
    this.cleanupOldSessions();
  }

  public getAnalyticsData(dateRange?: { start: Date; end: Date }): AnalyticsData {
    const filteredSessions = this.filterSessionsByDate(dateRange);
    
    return {
      totalSessions: filteredSessions.length,
      totalWords: this.calculateTotalWords(filteredSessions),
      averageAccuracy: this.calculateAverageAccuracy(filteredSessions),
      languageUsage: this.calculateLanguageUsage(filteredSessions),
      commandUsage: this.calculateCommandUsage(filteredSessions),
      productivityMetrics: this.calculateProductivityMetrics(filteredSessions)
    };
  }

  public getProductivityInsights(): {
    insights: string[];
    recommendations: string[];
  } {
    const data = this.getAnalyticsData();
    const insights: string[] = [];
    const recommendations: string[] = [];

    // Accuracy insights
    if (data.averageAccuracy > 0.9) {
      insights.push(`Excellent accuracy rate of ${(data.averageAccuracy * 100).toFixed(1)}%`);
    } else if (data.averageAccuracy < 0.8) {
      insights.push(`Accuracy could be improved (${(data.averageAccuracy * 100).toFixed(1)}%)`);
      recommendations.push('Consider speaking more clearly or adjusting microphone settings');
    }

    // Usage patterns
    const arabicUsage = data.languageUsage[Language.ARABIC] || 0;
    const englishUsage = data.languageUsage[Language.ENGLISH] || 0;
    const totalUsage = arabicUsage + englishUsage;

    if (totalUsage > 0) {
      const arabicPercentage = (arabicUsage / totalUsage) * 100;
      const englishPercentage = (englishUsage / totalUsage) * 100;
      
      insights.push(`Language usage: ${arabicPercentage.toFixed(1)}% Arabic, ${englishPercentage.toFixed(1)}% English`);
    }

    // Productivity insights
    if (data.productivityMetrics.wordsPerMinute > 50) {
      insights.push(`High productivity: ${data.productivityMetrics.wordsPerMinute.toFixed(0)} words per minute`);
    } else if (data.productivityMetrics.wordsPerMinute < 30) {
      recommendations.push('Practice with voice commands to increase dictation speed');
    }

    // Time saved calculation
    const timeSavedHours = data.productivityMetrics.timeSaved / 60;
    if (timeSavedHours > 1) {
      insights.push(`You've saved approximately ${timeSavedHours.toFixed(1)} hours through voice dictation`);
    }

    return { insights, recommendations };
  }

  private calculateWordsCount(results: SpeechRecognitionResult[]): number {
    return results
      .filter(result => result.isFinal)
      .reduce((total, result) => {
        const words = result.transcript.trim().split(/\s+/).filter(word => word.length > 0);
        return total + words.length;
      }, 0);
  }

  private calculateAccuracy(results: SpeechRecognitionResult[]): number {
    const finalResults = results.filter(result => result.isFinal);
    if (finalResults.length === 0) return 0;

    const totalConfidence = finalResults.reduce((sum, result) => sum + result.confidence, 0);
    return totalConfidence / finalResults.length;
  }

  private calculateTotalWords(sessions: DictationSession[]): number {
    return sessions.reduce((total, session) => total + session.wordsCount, 0);
  }

  private calculateAverageAccuracy(sessions: DictationSession[]): number {
    if (sessions.length === 0) return 0;
    
    const totalAccuracy = sessions.reduce((sum, session) => sum + session.accuracy, 0);
    return totalAccuracy / sessions.length;
  }

  private calculateLanguageUsage(sessions: DictationSession[]): Record<Language, number> {
    const usage: Record<Language, number> = {
      [Language.ARABIC]: 0,
      [Language.ENGLISH]: 0,
      [Language.AUTO_DETECT]: 0
    };

    sessions.forEach(session => {
      usage[session.language] += session.wordsCount;
    });

    return usage;
  }

  private calculateCommandUsage(sessions: DictationSession[]): Record<string, number> {
    const usage: Record<string, number> = {};

    sessions.forEach(session => {
      session.commands.forEach(command => {
        usage[command.phrase] = (usage[command.phrase] || 0) + 1;
      });
    });

    return usage;
  }

  private calculateProductivityMetrics(sessions: DictationSession[]): ProductivityMetrics {
    const totalWords = this.calculateTotalWords(sessions);
    const totalMinutes = sessions.reduce((sum, session) => sum + (session.duration / 60000), 0);
    
    const wordsPerMinute = totalMinutes > 0 ? totalWords / totalMinutes : 0;
    
    // Estimate time saved compared to typing (assuming 40 WPM typing speed)
    const typingTimeMinutes = totalWords / 40;
    const dictationTimeMinutes = totalMinutes;
    const timeSaved = Math.max(0, typingTimeMinutes - dictationTimeMinutes);

    // Calculate accuracy trend (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const recentSessions = sessions.filter(session => session.startTime >= thirtyDaysAgo);
    const accuracyTrend = this.calculateAccuracyTrend(recentSessions);

    // Calculate usage pattern (by hour of day)
    const usagePattern = this.calculateUsagePattern(sessions);

    return {
      wordsPerMinute,
      timeSaved,
      accuracyTrend,
      usagePattern
    };
  }

  private calculateAccuracyTrend(sessions: DictationSession[]): Array<{ date: Date; accuracy: number }> {
    const dailyAccuracy: Record<string, { total: number; count: number }> = {};

    sessions.forEach(session => {
      const dateKey = session.startTime.toISOString().split('T')[0];
      if (!dailyAccuracy[dateKey]) {
        dailyAccuracy[dateKey] = { total: 0, count: 0 };
      }
      dailyAccuracy[dateKey].total += session.accuracy;
      dailyAccuracy[dateKey].count += 1;
    });

    return Object.entries(dailyAccuracy)
      .map(([dateStr, data]) => ({
        date: new Date(dateStr),
        accuracy: data.total / data.count
      }))
      .sort((a, b) => a.date.getTime() - b.date.getTime());
  }

  private calculateUsagePattern(sessions: DictationSession[]): Array<{ hour: number; sessions: number }> {
    const hourlyUsage: Record<number, number> = {};

    // Initialize all hours
    for (let hour = 0; hour < 24; hour++) {
      hourlyUsage[hour] = 0;
    }

    sessions.forEach(session => {
      const hour = session.startTime.getHours();
      hourlyUsage[hour] += 1;
    });

    return Object.entries(hourlyUsage).map(([hour, sessions]) => ({
      hour: parseInt(hour),
      sessions
    }));
  }

  private filterSessionsByDate(dateRange?: { start: Date; end: Date }): DictationSession[] {
    if (!dateRange) {
      return this.sessions;
    }

    return this.sessions.filter(session => 
      session.startTime >= dateRange.start && session.startTime <= dateRange.end
    );
  }

  private generateSessionId(): string {
    return `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private loadStoredData(): void {
    try {
      const stored = localStorage.getItem('al-bayan-analytics-sessions');
      if (stored) {
        const parsed = JSON.parse(stored);
        this.sessions = parsed.map((session: any) => ({
          ...session,
          startTime: new Date(session.startTime),
          endTime: new Date(session.endTime)
        }));
      }
    } catch (error) {
      console.error('Failed to load analytics data:', error);
      this.sessions = [];
    }
  }

  private saveData(): void {
    if (!this.settings?.analytics.enabled) {
      return;
    }

    try {
      localStorage.setItem('al-bayan-analytics-sessions', JSON.stringify(this.sessions));
    } catch (error) {
      console.error('Failed to save analytics data:', error);
    }
  }

  private cleanupOldSessions(): void {
    if (!this.settings?.analytics.retentionDays) {
      return;
    }

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - this.settings.analytics.retentionDays);

    this.sessions = this.sessions.filter(session => session.startTime >= cutoffDate);
    this.saveData();
  }

  public updateSettings(settings: UserSettings): void {
    this.settings = settings;
    
    if (!settings.analytics.enabled) {
      // Clear data if analytics is disabled
      this.sessions = [];
      localStorage.removeItem('al-bayan-analytics-sessions');
    }
  }

  public exportData(): string {
    return JSON.stringify({
      sessions: this.sessions,
      exportDate: new Date().toISOString(),
      version: '1.0'
    }, null, 2);
  }

  public clearAllData(): void {
    this.sessions = [];
    localStorage.removeItem('al-bayan-analytics-sessions');
  }

  public getSessionCount(): number {
    return this.sessions.length;
  }

  public getLastSessionDate(): Date | null {
    if (this.sessions.length === 0) return null;
    
    return this.sessions.reduce((latest, session) => 
      session.startTime > latest ? session.startTime : latest, 
      this.sessions[0].startTime
    );
  }

  public getDailyStats(date: Date): {
    sessions: number;
    words: number;
    accuracy: number;
    duration: number;
  } {
    const dayStart = new Date(date);
    dayStart.setHours(0, 0, 0, 0);
    
    const dayEnd = new Date(date);
    dayEnd.setHours(23, 59, 59, 999);

    const daySessions = this.sessions.filter(session => 
      session.startTime >= dayStart && session.startTime <= dayEnd
    );

    return {
      sessions: daySessions.length,
      words: this.calculateTotalWords(daySessions),
      accuracy: this.calculateAverageAccuracy(daySessions),
      duration: daySessions.reduce((total, session) => total + session.duration, 0)
    };
  }

  public getTopCommands(limit: number = 10): Array<{ command: string; usage: number }> {
    const commandUsage = this.calculateCommandUsage(this.sessions);
    
    return Object.entries(commandUsage)
      .sort(([, a], [, b]) => b - a)
      .slice(0, limit)
      .map(([command, usage]) => ({ command, usage }));
  }
}
