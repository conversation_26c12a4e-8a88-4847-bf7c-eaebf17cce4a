<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<OfficeApp xmlns="http://schemas.microsoft.com/office/appforoffice/1.1" 
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
           xmlns:bt="http://schemas.microsoft.com/office/officeappbasictypes/1.0" 
           xmlns:ov="http://schemas.microsoft.com/office/taskpaneappversionoverrides" 
           xsi:type="TaskPaneApp">

  <!-- Begin Basic Settings: Add-in metadata, used for all versions of Office unless override provided. -->
  <Id>al-bayan-connect-2025-v1</Id>
  <Version>*******</Version>
  <ProviderName>Al-Bayan AI Platform - Dr. <PERSON>il</ProviderName>
  <DefaultLocale>en-US</DefaultLocale>
  
  <!-- The display name of your add-in. Used on the store and various places of the Office UI such as the add-ins dialog. -->
  <DisplayName DefaultValue="Al-Bayan Connect" />
  <Description DefaultValue="Revolutionary bilingual dictation add-in for seamless Arabic-English voice-to-text with intelligent language detection and advanced voice commands." />
  
  <!-- Icon for your add-in. Used on installation screens and the add-ins dialog. -->
  <IconUrl DefaultValue="https://localhost:3000/assets/icon-32.png" />
  <HighResolutionIconUrl DefaultValue="https://localhost:3000/assets/icon-64.png" />
  
  <!-- Domains that will be allowed when navigating to them from the add-in. -->
  <AppDomains>
    <AppDomain>https://localhost:3000</AppDomain>
    <AppDomain>https://al-bayan-connect.azurewebsites.net</AppDomain>
  </AppDomains>
  
  <!-- End Basic Settings. -->

  <!-- Begin TaskPane Mode integration. This section is used if there are no VersionOverrides or if the Office client version does not support add-in commands. -->
  <Hosts>
    <Host Name="Document" />
    <Host Name="Presentation" />
    <Host Name="Mailbox" />
  </Hosts>
  
  <DefaultSettings>
    <SourceLocation DefaultValue="https://localhost:3000/taskpane.html" />
  </DefaultSettings>
  
  <!-- End TaskPane Mode integration. -->

  <Permissions>ReadWriteDocument</Permissions>

  <!-- Begin Add-in Commands Mode integration. -->
  <VersionOverrides xmlns="http://schemas.microsoft.com/office/taskpaneappversionoverrides" xsi:type="VersionOverridesV1_0">

    <!-- The Hosts node is required. -->
    <Hosts>
      <!-- Word Integration -->
      <Host xsi:type="Document">
        <DesktopFormFactor>
          <FunctionFile resid="Commands.Url" />

          <ExtensionPoint xsi:type="PrimaryCommandSurface">
            <CustomTab id="AlBayanConnectTab">
              <Label resid="AlBayanConnect.Tab.Label" />
              
              <!-- Main Dictation Group -->
              <Group id="DictationGroup">
                <Label resid="AlBayanConnect.DictationGroup.Label" />
                
                <!-- Main Dictation Panel Button -->
                <Control xsi:type="Button" id="DictationPanelButton">
                  <Label resid="AlBayanConnect.DictationPanel.Label" />
                  <Supertip>
                    <Title resid="AlBayanConnect.DictationPanel.Label" />
                    <Description resid="AlBayanConnect.DictationPanel.Tooltip" />
                  </Supertip>
                  <Icon>
                    <bt:Image size="16" resid="Icon.16x16" />
                    <bt:Image size="32" resid="Icon.32x32" />
                    <bt:Image size="80" resid="Icon.80x80" />
                  </Icon>
                  <Action xsi:type="ShowTaskpane">
                    <TaskpaneId>DictationPanel</TaskpaneId>
                    <SourceLocation resid="Taskpane.Url" />
                  </Action>
                </Control>
                
                <!-- Quick Dictate Button -->
                <Control xsi:type="Button" id="QuickDictateButton">
                  <Label resid="AlBayanConnect.QuickDictate.Label" />
                  <Supertip>
                    <Title resid="AlBayanConnect.QuickDictate.Label" />
                    <Description resid="AlBayanConnect.QuickDictate.Tooltip" />
                  </Supertip>
                  <Icon>
                    <bt:Image size="16" resid="Microphone.16x16" />
                    <bt:Image size="32" resid="Microphone.32x32" />
                    <bt:Image size="80" resid="Microphone.80x80" />
                  </Icon>
                  <Action xsi:type="ExecuteFunction">
                    <FunctionName>toggleQuickDictation</FunctionName>
                  </Action>
                </Control>

                <!-- Language Switch Button -->
                <Control xsi:type="Button" id="LanguageSwitchButton">
                  <Label resid="AlBayanConnect.LanguageSwitch.Label" />
                  <Supertip>
                    <Title resid="AlBayanConnect.LanguageSwitch.Label" />
                    <Description resid="AlBayanConnect.LanguageSwitch.Tooltip" />
                  </Supertip>
                  <Icon>
                    <bt:Image size="16" resid="Language.16x16" />
                    <bt:Image size="32" resid="Language.32x32" />
                    <bt:Image size="80" resid="Language.80x80" />
                  </Icon>
                  <Action xsi:type="ExecuteFunction">
                    <FunctionName>switchLanguage</FunctionName>
                  </Action>
                </Control>
              </Group>

              <!-- Smart Features Group -->
              <Group id="SmartFeaturesGroup">
                <Label resid="AlBayanConnect.SmartFeaturesGroup.Label" />
                
                <!-- Custom Commands Button -->
                <Control xsi:type="Button" id="CustomCommandsButton">
                  <Label resid="AlBayanConnect.CustomCommands.Label" />
                  <Supertip>
                    <Title resid="AlBayanConnect.CustomCommands.Label" />
                    <Description resid="AlBayanConnect.CustomCommands.Tooltip" />
                  </Supertip>
                  <Icon>
                    <bt:Image size="16" resid="Commands.16x16" />
                    <bt:Image size="32" resid="Commands.32x32" />
                    <bt:Image size="80" resid="Commands.80x80" />
                  </Icon>
                  <Action xsi:type="ShowTaskpane">
                    <TaskpaneId>CustomCommands</TaskpaneId>
                    <SourceLocation resid="CustomCommands.Url" />
                  </Action>
                </Control>

                <!-- Analytics Button -->
                <Control xsi:type="Button" id="AnalyticsButton">
                  <Label resid="AlBayanConnect.Analytics.Label" />
                  <Supertip>
                    <Title resid="AlBayanConnect.Analytics.Label" />
                    <Description resid="AlBayanConnect.Analytics.Tooltip" />
                  </Supertip>
                  <Icon>
                    <bt:Image size="16" resid="Analytics.16x16" />
                    <bt:Image size="32" resid="Analytics.32x32" />
                    <bt:Image size="80" resid="Analytics.80x80" />
                  </Icon>
                  <Action xsi:type="ShowTaskpane">
                    <TaskpaneId>Analytics</TaskpaneId>
                    <SourceLocation resid="Analytics.Url" />
                  </Action>
                </Control>
              </Group>
            </CustomTab>
          </ExtensionPoint>
        </DesktopFormFactor>
      </Host>
    </Hosts>

    <!-- Resources -->
    <Resources>
      <bt:Images>
        <bt:Image id="Icon.16x16" DefaultValue="https://localhost:3000/assets/icon-16.png" />
        <bt:Image id="Icon.32x32" DefaultValue="https://localhost:3000/assets/icon-32.png" />
        <bt:Image id="Icon.80x80" DefaultValue="https://localhost:3000/assets/icon-80.png" />
        <bt:Image id="Microphone.16x16" DefaultValue="https://localhost:3000/assets/microphone-16.png" />
        <bt:Image id="Microphone.32x32" DefaultValue="https://localhost:3000/assets/microphone-32.png" />
        <bt:Image id="Microphone.80x80" DefaultValue="https://localhost:3000/assets/microphone-80.png" />
        <bt:Image id="Language.16x16" DefaultValue="https://localhost:3000/assets/language-16.png" />
        <bt:Image id="Language.32x32" DefaultValue="https://localhost:3000/assets/language-32.png" />
        <bt:Image id="Language.80x80" DefaultValue="https://localhost:3000/assets/language-80.png" />
        <bt:Image id="Commands.16x16" DefaultValue="https://localhost:3000/assets/commands-16.png" />
        <bt:Image id="Commands.32x32" DefaultValue="https://localhost:3000/assets/commands-32.png" />
        <bt:Image id="Commands.80x80" DefaultValue="https://localhost:3000/assets/commands-80.png" />
        <bt:Image id="Analytics.16x16" DefaultValue="https://localhost:3000/assets/analytics-16.png" />
        <bt:Image id="Analytics.32x32" DefaultValue="https://localhost:3000/assets/analytics-32.png" />
        <bt:Image id="Analytics.80x80" DefaultValue="https://localhost:3000/assets/analytics-80.png" />
      </bt:Images>
      <bt:Urls>
        <bt:Url id="Commands.Url" DefaultValue="https://localhost:3000/commands.html" />
        <bt:Url id="Taskpane.Url" DefaultValue="https://localhost:3000/taskpane.html" />
        <bt:Url id="CustomCommands.Url" DefaultValue="https://localhost:3000/custom-commands.html" />
        <bt:Url id="Analytics.Url" DefaultValue="https://localhost:3000/analytics.html" />
      </bt:Urls>
      <!-- ShortStrings max characters==125. -->
      <bt:ShortStrings>
        <bt:String id="AlBayanConnect.Tab.Label" DefaultValue="Al-Bayan Connect" />
        <bt:String id="AlBayanConnect.DictationGroup.Label" DefaultValue="Voice Dictation" />
        <bt:String id="AlBayanConnect.SmartFeaturesGroup.Label" DefaultValue="Smart Features" />
        <bt:String id="AlBayanConnect.DictationPanel.Label" DefaultValue="Dictation Panel" />
        <bt:String id="AlBayanConnect.QuickDictate.Label" DefaultValue="Quick Dictate" />
        <bt:String id="AlBayanConnect.LanguageSwitch.Label" DefaultValue="Switch Language" />
        <bt:String id="AlBayanConnect.CustomCommands.Label" DefaultValue="Custom Commands" />
        <bt:String id="AlBayanConnect.Analytics.Label" DefaultValue="Analytics" />
      </bt:ShortStrings>
      <!-- LongStrings max characters==250. -->
      <bt:LongStrings>
        <bt:String id="AlBayanConnect.DictationPanel.Tooltip" DefaultValue="Open the Al-Bayan Connect dictation panel with bilingual support and advanced features" />
        <bt:String id="AlBayanConnect.QuickDictate.Tooltip" DefaultValue="Start/stop dictation instantly with intelligent Arabic-English language detection" />
        <bt:String id="AlBayanConnect.LanguageSwitch.Tooltip" DefaultValue="Toggle between Arabic and English languages or enable auto-detection" />
        <bt:String id="AlBayanConnect.CustomCommands.Tooltip" DefaultValue="Create and manage custom voice commands for frequently used text and actions" />
        <bt:String id="AlBayanConnect.Analytics.Tooltip" DefaultValue="View usage statistics and productivity insights for your dictation sessions" />
      </bt:LongStrings>
    </Resources>
  </VersionOverrides>
  <!-- End Add-in Commands Mode integration. -->

</OfficeApp>
