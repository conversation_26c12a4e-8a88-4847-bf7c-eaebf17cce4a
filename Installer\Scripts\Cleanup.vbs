' Al-Bayan Connect - Complete Cleanup Script
' Author: Dr. <PERSON>
' Copyright: © 2025 Al-Bayan AI Platform

Option Explicit

Dim shell, fso, logFile, installPath
Dim cleanupResults, errorCount, successCount

' Initialize objects
Set shell = CreateObject("WScript.Shell")
Set fso = CreateObject("Scripting.FileSystemObject")
Set cleanupResults = CreateObject("Scripting.Dictionary")

errorCount = 0
successCount = 0

' Configuration
logFile = shell.ExpandEnvironmentStrings("%TEMP%") & "\AlBayanConnect_Cleanup.log"

' Main cleanup function
Function PerformCompleteCleanup()
    WriteLog "=== Al-Bayan Connect Complete Cleanup Started ==="
    WriteLog "Date: " & Now()
    WriteLog "Computer: " & shell.ExpandEnvironmentStrings("%COMPUTERNAME%")
    WriteLog "User: " & shell.ExpandEnvironmentStrings("%USERNAME%")
    WriteLog ""
    
    ' Get installation path
    installPath = GetInstallationPath()
    WriteLog "Installation Path: " & installPath
    WriteLog ""
    
    ' Perform cleanup operations
    CloseOfficeApplications()
    RemoveOfficeAddinRegistration()
    RemoveApplicationRegistryEntries()
    RemoveFileAssociations()
    RemoveTrustedLocations()
    RemoveStartMenuEntries()
    RemoveDesktopShortcuts()
    RemoveInstallationFiles()
    RemoveUserData()
    RemoveTemporaryFiles()
    RemoveCertificates()
    
    ' Generate cleanup summary
    GenerateCleanupSummary()
    
    ' Return result
    If errorCount = 0 Then
        PerformCompleteCleanup = 0 ' Success
    Else
        PerformCompleteCleanup = 1 ' Some errors occurred
    End If
End Function

' Close Office Applications
Sub CloseOfficeApplications()
    Dim processes, process, processName
    
    WriteLog "Closing Office Applications..."
    
    processes = Array("WINWORD.EXE", "POWERPNT.EXE", "OUTLOOK.EXE", "EXCEL.EXE")
    
    For Each processName In processes
        On Error Resume Next
        
        ' Try graceful shutdown first
        shell.Run "taskkill /IM " & processName & " /T", 0, True
        WScript.Sleep 2000
        
        ' Force kill if still running
        shell.Run "taskkill /F /IM " & processName & " /T", 0, True
        
        If Err.Number = 0 Then
            WriteLog "✓ Closed: " & processName
            RecordSuccess "CloseProcess_" & processName
        Else
            WriteLog "⚠ Process not running or already closed: " & processName
        End If
        Err.Clear
    Next
    
    WriteLog ""
End Sub

' Remove Office Add-in Registration
Sub RemoveOfficeAddinRegistration()
    Dim officeVersions, version, regPath, catalogKeys, key
    
    WriteLog "Removing Office Add-in Registration..."
    
    officeVersions = Array("16.0", "15.0", "14.0")
    
    For Each version In officeVersions
        WriteLog "Cleaning Office " & version & " registration..."
        
        ' Remove trusted catalogs
        regPath = "HKCU\Software\Microsoft\Office\" & version & "\WEF\TrustedCatalogs\"
        
        On Error Resume Next
        catalogKeys = GetRegistrySubKeys(regPath)
        
        If IsArray(catalogKeys) Then
            For Each key In catalogKeys
                If InStr(LCase(key), "al-bayan") > 0 Or InStr(LCase(key), "albayan") > 0 Then
                    shell.RegDelete regPath & key & "\"
                    If Err.Number = 0 Then
                        WriteLog "✓ Removed catalog: " & key
                        RecordSuccess "RemoveCatalog_" & version & "_" & key
                    Else
                        WriteLog "✗ Failed to remove catalog: " & key
                        RecordError "RemoveCatalog_" & version & "_" & key
                    End If
                    Err.Clear
                End If
            Next
        End If
        
        ' Remove trusted locations
        regPath = "HKCU\Software\Microsoft\Office\" & version & "\Common\Security\Trusted Locations\"
        
        Dim locations, location, locationPath, description
        locations = GetRegistrySubKeys(regPath)
        
        If IsArray(locations) Then
            For Each location In locations
                On Error Resume Next
                description = shell.RegRead(regPath & location & "\Description")
                If Err.Number = 0 And InStr(LCase(description), "al-bayan") > 0 Then
                    shell.RegDelete regPath & location & "\"
                    If Err.Number = 0 Then
                        WriteLog "✓ Removed trusted location: " & location
                        RecordSuccess "RemoveTrustedLocation_" & version & "_" & location
                    Else
                        WriteLog "✗ Failed to remove trusted location: " & location
                        RecordError "RemoveTrustedLocation_" & version & "_" & location
                    End If
                End If
                Err.Clear
            Next
        End If
    Next
    
    WriteLog ""
End Sub

' Remove Application Registry Entries
Sub RemoveApplicationRegistryEntries()
    Dim registryPaths, regPath
    
    WriteLog "Removing Application Registry Entries..."
    
    registryPaths = Array( _
        "HKCU\Software\Al-Bayan\", _
        "HKCU\Software\Microsoft\Windows\CurrentVersion\Uninstall\{ProductCode}\", _
        "HKLM\Software\Al-Bayan\", _
        "HKCU\Software\Classes\AlBayanConnect.CommandFile\" _
    )
    
    For Each regPath In registryPaths
        On Error Resume Next
        shell.RegDelete regPath
        
        If Err.Number = 0 Then
            WriteLog "✓ Removed registry path: " & regPath
            RecordSuccess "RemoveRegistry_" & Replace(regPath, "\", "_")
        ElseIf Err.Number = -2147024894 Then ' Key not found
            WriteLog "⚠ Registry path not found: " & regPath
        Else
            WriteLog "✗ Failed to remove registry path: " & regPath & " (Error: " & Err.Number & ")"
            RecordError "RemoveRegistry_" & Replace(regPath, "\", "_")
        End If
        Err.Clear
    Next
    
    WriteLog ""
End Sub

' Remove File Associations
Sub RemoveFileAssociations()
    WriteLog "Removing File Associations..."
    
    On Error Resume Next
    
    ' Remove .albayan file association
    shell.RegDelete "HKCU\Software\Classes\.albayan\"
    If Err.Number = 0 Then
        WriteLog "✓ Removed .albayan file association"
        RecordSuccess "RemoveFileAssociation_albayan"
    Else
        WriteLog "⚠ .albayan file association not found or already removed"
    End If
    Err.Clear
    
    ' Remove AlBayanConnect.CommandFile class
    shell.RegDelete "HKCU\Software\Classes\AlBayanConnect.CommandFile\"
    If Err.Number = 0 Then
        WriteLog "✓ Removed AlBayanConnect.CommandFile class"
        RecordSuccess "RemoveFileClass_CommandFile"
    Else
        WriteLog "⚠ AlBayanConnect.CommandFile class not found or already removed"
    End If
    Err.Clear
    
    WriteLog ""
End Sub

' Remove Trusted Locations
Sub RemoveTrustedLocations()
    WriteLog "Removing Additional Trusted Locations..."
    
    ' This is handled in RemoveOfficeAddinRegistration, but we double-check here
    WriteLog "✓ Trusted locations cleanup completed in Office registration removal"
    WriteLog ""
End Sub

' Remove Start Menu Entries
Sub RemoveStartMenuEntries()
    Dim startMenuPath, programsPath
    
    WriteLog "Removing Start Menu Entries..."
    
    ' Get Start Menu paths
    startMenuPath = shell.SpecialFolders("StartMenu") & "\Programs\Al-Bayan Connect"
    programsPath = shell.SpecialFolders("Programs") & "\Al-Bayan Connect"
    
    ' Remove Start Menu folder and shortcuts
    If fso.FolderExists(startMenuPath) Then
        On Error Resume Next
        fso.DeleteFolder startMenuPath, True
        If Err.Number = 0 Then
            WriteLog "✓ Removed Start Menu folder: " & startMenuPath
            RecordSuccess "RemoveStartMenuFolder"
        Else
            WriteLog "✗ Failed to remove Start Menu folder: " & startMenuPath
            RecordError "RemoveStartMenuFolder"
        End If
        Err.Clear
    Else
        WriteLog "⚠ Start Menu folder not found: " & startMenuPath
    End If
    
    ' Remove Programs folder
    If fso.FolderExists(programsPath) Then
        On Error Resume Next
        fso.DeleteFolder programsPath, True
        If Err.Number = 0 Then
            WriteLog "✓ Removed Programs folder: " & programsPath
            RecordSuccess "RemoveProgramsFolder"
        Else
            WriteLog "✗ Failed to remove Programs folder: " & programsPath
            RecordError "RemoveProgramsFolder"
        End If
        Err.Clear
    Else
        WriteLog "⚠ Programs folder not found: " & programsPath
    End If
    
    WriteLog ""
End Sub

' Remove Desktop Shortcuts
Sub RemoveDesktopShortcuts()
    Dim desktopPath, shortcutPath
    
    WriteLog "Removing Desktop Shortcuts..."
    
    desktopPath = shell.SpecialFolders("Desktop")
    shortcutPath = desktopPath & "\Al-Bayan Connect.lnk"
    
    If fso.FileExists(shortcutPath) Then
        On Error Resume Next
        fso.DeleteFile shortcutPath
        If Err.Number = 0 Then
            WriteLog "✓ Removed desktop shortcut: " & shortcutPath
            RecordSuccess "RemoveDesktopShortcut"
        Else
            WriteLog "✗ Failed to remove desktop shortcut: " & shortcutPath
            RecordError "RemoveDesktopShortcut"
        End If
        Err.Clear
    Else
        WriteLog "⚠ Desktop shortcut not found: " & shortcutPath
    End If
    
    WriteLog ""
End Sub

' Remove Installation Files
Sub RemoveInstallationFiles()
    WriteLog "Removing Installation Files..."
    
    If installPath <> "" And fso.FolderExists(installPath) Then
        On Error Resume Next
        
        ' First, remove read-only attributes
        shell.Run "attrib -R """ & installPath & "*.*"" /S", 0, True
        
        ' Delete the installation folder
        fso.DeleteFolder installPath, True
        
        If Err.Number = 0 Then
            WriteLog "✓ Removed installation folder: " & installPath
            RecordSuccess "RemoveInstallationFolder"
        Else
            WriteLog "✗ Failed to remove installation folder: " & installPath & " (Error: " & Err.Number & ")"
            RecordError "RemoveInstallationFolder"
            
            ' Try to remove individual files if folder deletion failed
            RemoveIndividualFiles installPath
        End If
        Err.Clear
    Else
        WriteLog "⚠ Installation folder not found or path empty: " & installPath
    End If
    
    WriteLog ""
End Sub

' Remove User Data
Sub RemoveUserData()
    Dim userDataPaths, dataPath
    
    WriteLog "Removing User Data..."
    
    userDataPaths = Array( _
        shell.ExpandEnvironmentStrings("%APPDATA%") & "\Al-Bayan Connect", _
        shell.ExpandEnvironmentStrings("%LOCALAPPDATA%") & "\Al-Bayan Connect", _
        shell.ExpandEnvironmentStrings("%USERPROFILE%") & "\Documents\Al-Bayan Connect" _
    )
    
    For Each dataPath In userDataPaths
        If fso.FolderExists(dataPath) Then
            On Error Resume Next
            fso.DeleteFolder dataPath, True
            If Err.Number = 0 Then
                WriteLog "✓ Removed user data folder: " & dataPath
                RecordSuccess "RemoveUserData_" & Replace(dataPath, "\", "_")
            Else
                WriteLog "✗ Failed to remove user data folder: " & dataPath
                RecordError "RemoveUserData_" & Replace(dataPath, "\", "_")
            End If
            Err.Clear
        Else
            WriteLog "⚠ User data folder not found: " & dataPath
        End If
    Next
    
    WriteLog ""
End Sub

' Remove Temporary Files
Sub RemoveTemporaryFiles()
    Dim tempPaths, tempPath, tempFiles, tempFile
    
    WriteLog "Removing Temporary Files..."
    
    tempPaths = Array( _
        shell.ExpandEnvironmentStrings("%TEMP%"), _
        shell.ExpandEnvironmentStrings("%TMP%") _
    )
    
    For Each tempPath In tempPaths
        If fso.FolderExists(tempPath) Then
            ' Look for Al-Bayan related temp files
            On Error Resume Next
            For Each tempFile In fso.GetFolder(tempPath).Files
                If InStr(LCase(tempFile.Name), "albayan") > 0 Or InStr(LCase(tempFile.Name), "al-bayan") > 0 Then
                    fso.DeleteFile tempFile.Path
                    If Err.Number = 0 Then
                        WriteLog "✓ Removed temp file: " & tempFile.Name
                        RecordSuccess "RemoveTempFile_" & tempFile.Name
                    End If
                    Err.Clear
                End If
            Next
        End If
    Next
    
    WriteLog ""
End Sub

' Remove Certificates
Sub RemoveCertificates()
    WriteLog "Removing Development Certificates..."
    
    ' Remove any development certificates that might have been installed
    On Error Resume Next
    shell.Run "certlm.msc /s", 0, False
    
    WriteLog "⚠ Manual certificate cleanup may be required"
    WriteLog "  Check: Certificates (Local Computer) > Personal > Certificates"
    WriteLog "  Look for: Al-Bayan Connect development certificates"
    
    WriteLog ""
End Sub

' Utility Functions
Function GetInstallationPath()
    Dim path
    
    ' Try to get from registry
    On Error Resume Next
    path = shell.RegRead("HKCU\Software\Al-Bayan\Connect\InstallPath")
    
    If Err.Number <> 0 Or path = "" Then
        ' Try alternative registry location
        path = shell.RegRead("HKLM\Software\Al-Bayan\Connect\InstallPath")
        
        If Err.Number <> 0 Or path = "" Then
            ' Fallback to default location
            path = shell.ExpandEnvironmentStrings("%LOCALAPPDATA%") & "\Al-Bayan Connect\"
        End If
    End If
    
    GetInstallationPath = path
End Function

Function GetRegistrySubKeys(regPath)
    ' This is a simplified version - in a real implementation,
    ' you would use WMI or other methods to enumerate registry keys
    GetRegistrySubKeys = Array() ' Return empty array for now
End Function

Sub RemoveIndividualFiles(folderPath)
    ' Attempt to remove individual files if folder deletion failed
    On Error Resume Next
    
    If fso.FolderExists(folderPath) Then
        Dim folder, file
        Set folder = fso.GetFolder(folderPath)
        
        For Each file In folder.Files
            fso.DeleteFile file.Path
        Next
        
        ' Try to remove the folder again
        fso.DeleteFolder folderPath
    End If
End Sub

Sub RecordSuccess(operation)
    successCount = successCount + 1
    cleanupResults.Add operation, "SUCCESS"
End Sub

Sub RecordError(operation)
    errorCount = errorCount + 1
    cleanupResults.Add operation, "ERROR"
End Sub

Sub GenerateCleanupSummary()
    Dim key, status
    
    WriteLog "=== CLEANUP SUMMARY ==="
    WriteLog "Total Operations: " & cleanupResults.Count
    WriteLog "Successful: " & successCount
    WriteLog "Errors: " & errorCount
    WriteLog ""
    
    WriteLog "Detailed Results:"
    For Each key In cleanupResults.Keys
        status = cleanupResults(key)
        WriteLog key & ": " & status
    Next
    
    WriteLog ""
    If errorCount = 0 Then
        WriteLog "CLEANUP RESULT: COMPLETE SUCCESS"
    Else
        WriteLog "CLEANUP RESULT: COMPLETED WITH " & errorCount & " ERROR(S)"
    End If
    
    WriteLog "=== END OF CLEANUP ==="
End Sub

Sub WriteLog(message)
    Dim logFileHandle
    
    On Error Resume Next
    Set logFileHandle = fso.OpenTextFile(logFile, 8, True)
    
    If Err.Number = 0 Then
        logFileHandle.WriteLine Now() & " - " & message
        logFileHandle.Close
    End If
    
    WScript.Echo message
End Sub

' Main execution
Dim result
result = PerformCompleteCleanup()

WScript.Echo ""
WScript.Echo "Cleanup completed. Log file: " & logFile
WScript.Echo ""
WScript.Echo "Al-Bayan Connect has been removed from your system."
WScript.Echo "Thank you for using Al-Bayan Connect!"

WScript.Quit result
