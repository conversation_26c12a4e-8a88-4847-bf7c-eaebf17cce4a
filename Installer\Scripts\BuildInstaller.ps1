# Al-Bayan Connect - Installer Build Script
# Author: Dr. <PERSON>il
# Copyright: © 2025 Al-Bayan AI Platform

param(
    [string]$Configuration = "Release",
    [string]$Platform = "x86",
    [string]$Version = "",
    [string]$BuildNumber = "",
    [string]$OutputPath = "",
    [switch]$Sign,
    [string]$CertificateThumbprint = "",
    [string]$CertificatePath = "",
    [string]$CertificatePassword = "",
    [switch]$Validate,
    [switch]$Package,
    [switch]$Clean,
    [switch]$Verbose,
    [switch]$Help
)

# Configuration
$script:ScriptVersion = "1.0.0"
$script:ProductName = "Al-Bayan Connect"
$script:ProjectFile = "AlBayanConnectInstaller.wixproj"
$script:LogFile = "$env:TEMP\AlBayanConnect_Build.log"

function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    if ($Verbose -or $Level -eq "ERROR" -or $Level -eq "WARN") {
        $color = switch ($Level) {
            "ERROR" { "Red" }
            "WARN" { "Yellow" }
            "SUCCESS" { "Green" }
            default { "White" }
        }
        Write-Host $logEntry -ForegroundColor $color
    }
    
    Add-Content -Path $script:LogFile -Value $logEntry -ErrorAction SilentlyContinue
}

function Show-Help {
    Write-Host @"

Al-Bayan Connect Installer Build Script v$script:ScriptVersion

DESCRIPTION:
    Builds the Al-Bayan Connect MSI installer with proper versioning,
    code signing, and validation.

SYNTAX:
    .\BuildInstaller.ps1 [OPTIONS]

PARAMETERS:
    -Configuration <config>       Build configuration (Debug/Release)
    -Platform <platform>          Target platform (x86/x64/AnyCPU)
    -Version <version>            Product version (e.g., *******)
    -BuildNumber <number>         Build number for CI/CD
    -OutputPath <path>            Custom output directory
    -Sign                         Enable code signing
    -CertificateThumbprint <hash> Certificate thumbprint for signing
    -CertificatePath <path>       Certificate file path for signing
    -CertificatePassword <pwd>    Certificate password
    -Validate                     Validate installer after build
    -Package                      Create distribution package
    -Clean                        Clean before build
    -Verbose                      Enable verbose output
    -Help                         Show this help message

EXAMPLES:
    # Basic build
    .\BuildInstaller.ps1

    # Release build with signing
    .\BuildInstaller.ps1 -Configuration Release -Sign -CertificateThumbprint "1234567890ABCDEF"

    # Build with custom version
    .\BuildInstaller.ps1 -Version "1.2.3.4" -BuildNumber "456"

    # Full build with packaging
    .\BuildInstaller.ps1 -Configuration Release -Sign -Validate -Package

ENVIRONMENT VARIABLES:
    BUILD_VERSION              Product version
    BUILD_NUMBER               Build number
    CODE_SIGNING_CERT_THUMBPRINT  Certificate thumbprint
    CODE_SIGNING_CERT_PATH     Certificate file path
    CODE_SIGNING_CERT_PASSWORD Certificate password

"@
}

function Get-ProductVersion {
    if (-not [string]::IsNullOrEmpty($Version)) {
        return $Version
    }
    
    # Try environment variable
    if ($env:BUILD_VERSION) {
        return $env:BUILD_VERSION
    }
    
    # Try to read from version file
    $versionFile = Join-Path $PSScriptRoot "..\version.txt"
    if (Test-Path $versionFile) {
        $fileVersion = Get-Content $versionFile -First 1
        if ($fileVersion) {
            return $fileVersion.Trim()
        }
    }
    
    # Default version
    return "*******"
}

function Get-BuildNumber {
    if (-not [string]::IsNullOrEmpty($BuildNumber)) {
        return $BuildNumber
    }
    
    # Try environment variable
    if ($env:BUILD_NUMBER) {
        return $env:BUILD_NUMBER
    }
    
    # Generate based on date/time
    $now = Get-Date
    return $now.ToString("yyyyMMdd") + $now.ToString("HHmm")
}

function Test-BuildEnvironment {
    Write-Log "Checking build environment..." "INFO"
    
    $issues = @()
    
    # Check MSBuild
    $msbuild = Get-Command "msbuild.exe" -ErrorAction SilentlyContinue
    if (-not $msbuild) {
        $issues += "MSBuild not found in PATH"
    } else {
        Write-Log "MSBuild found: $($msbuild.Source)" "INFO"
    }
    
    # Check WiX Toolset
    $candle = Get-Command "candle.exe" -ErrorAction SilentlyContinue
    if (-not $candle) {
        $issues += "WiX Toolset not found in PATH"
    } else {
        Write-Log "WiX Toolset found: $($candle.Source)" "INFO"
    }
    
    # Check project file
    if (-not (Test-Path $script:ProjectFile)) {
        $issues += "Project file not found: $script:ProjectFile"
    }
    
    # Check source files
    $sourceFiles = @(
        "Product.wxs",
        "Components\OfficeAddin.wxs",
        "Components\WebAssets.wxs",
        "Components\Prerequisites.wxs",
        "UI\InstallerUI.wxs",
        "UI\Dialogs.wxs"
    )
    
    foreach ($file in $sourceFiles) {
        if (-not (Test-Path $file)) {
            $issues += "Source file not found: $file"
        }
    }
    
    if ($issues.Count -gt 0) {
        Write-Log "Build environment check failed:" "ERROR"
        foreach ($issue in $issues) {
            Write-Log "  - $issue" "ERROR"
        }
        return $false
    }
    
    Write-Log "Build environment check passed" "SUCCESS"
    return $true
}

function Update-VersionInfo {
    param(
        [string]$ProductVersion,
        [string]$BuildNum
    )
    
    Write-Log "Updating version information..." "INFO"
    Write-Log "Product Version: $ProductVersion" "INFO"
    Write-Log "Build Number: $BuildNum" "INFO"
    
    try {
        # Update Product.wxs with version information
        $productFile = "Product.wxs"
        $content = Get-Content $productFile -Raw
        
        # Update version attribute
        $content = $content -replace 'Version="[^"]*"', "Version=`"$ProductVersion`""
        
        # Update build number in comments or properties
        $content = $content -replace 'Build="[^"]*"', "Build=`"$BuildNum`""
        
        Set-Content -Path $productFile -Value $content -Encoding UTF8
        
        Write-Log "Version information updated successfully" "SUCCESS"
        return $true
    }
    catch {
        Write-Log "Failed to update version information: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Invoke-Build {
    param(
        [string]$Config,
        [string]$Plat,
        [string]$Output
    )
    
    Write-Log "Starting build process..." "INFO"
    Write-Log "Configuration: $Config" "INFO"
    Write-Log "Platform: $Plat" "INFO"
    
    try {
        # Prepare MSBuild arguments
        $msbuildArgs = @(
            $script:ProjectFile,
            "/p:Configuration=$Config",
            "/p:Platform=$Plat",
            "/v:normal",
            "/fl",
            "/flp:LogFile=$script:LogFile.Replace('.log', '_msbuild.log')"
        )
        
        if (-not [string]::IsNullOrEmpty($Output)) {
            $msbuildArgs += "/p:OutputPath=$Output"
        }
        
        # Add signing parameters
        if ($Sign) {
            $msbuildArgs += "/p:SignOutput=true"
            
            if (-not [string]::IsNullOrEmpty($CertificateThumbprint)) {
                $msbuildArgs += "/p:CodeSigningCertThumbprint=$CertificateThumbprint"
            } elseif (-not [string]::IsNullOrEmpty($CertificatePath)) {
                $msbuildArgs += "/p:CodeSigningCertPath=$CertificatePath"
                if (-not [string]::IsNullOrEmpty($CertificatePassword)) {
                    $msbuildArgs += "/p:CodeSigningCertPassword=$CertificatePassword"
                }
            }
        }
        
        # Add validation parameters
        if ($Validate) {
            $msbuildArgs += "/p:ValidateCerts=true"
        }
        
        # Clean if requested
        if ($Clean) {
            Write-Log "Cleaning previous build..." "INFO"
            $cleanArgs = $msbuildArgs + @("/t:Clean")
            $cleanResult = Start-Process -FilePath "msbuild.exe" -ArgumentList $cleanArgs -Wait -PassThru -NoNewWindow
            
            if ($cleanResult.ExitCode -ne 0) {
                Write-Log "Clean failed with exit code: $($cleanResult.ExitCode)" "ERROR"
                return $false
            }
            Write-Log "Clean completed successfully" "SUCCESS"
        }
        
        # Execute build
        Write-Log "Executing MSBuild..." "INFO"
        $buildResult = Start-Process -FilePath "msbuild.exe" -ArgumentList $msbuildArgs -Wait -PassThru -NoNewWindow
        
        if ($buildResult.ExitCode -eq 0) {
            Write-Log "Build completed successfully" "SUCCESS"
            return $true
        } else {
            Write-Log "Build failed with exit code: $($buildResult.ExitCode)" "ERROR"
            return $false
        }
    }
    catch {
        Write-Log "Build error: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Test-BuildOutput {
    param([string]$OutputDir)
    
    Write-Log "Validating build output..." "INFO"
    
    $outputFile = Join-Path $OutputDir "AlBayanConnectInstaller.msi"
    
    if (-not (Test-Path $outputFile)) {
        Write-Log "Output file not found: $outputFile" "ERROR"
        return $false
    }
    
    # Get file information
    $fileInfo = Get-Item $outputFile
    Write-Log "Output file: $($fileInfo.FullName)" "INFO"
    Write-Log "File size: $([math]::Round($fileInfo.Length / 1MB, 2)) MB" "INFO"
    
    # Check digital signature if signing was enabled
    if ($Sign) {
        try {
            $signature = Get-AuthenticodeSignature -FilePath $outputFile
            Write-Log "Signature status: $($signature.Status)" "INFO"
            
            if ($signature.Status -eq "Valid") {
                Write-Log "Digital signature validation: PASSED" "SUCCESS"
                if ($signature.SignerCertificate) {
                    Write-Log "Signer: $($signature.SignerCertificate.Subject)" "INFO"
                }
            } else {
                Write-Log "Digital signature validation: FAILED" "ERROR"
                return $false
            }
        }
        catch {
            Write-Log "Signature validation error: $($_.Exception.Message)" "ERROR"
            return $false
        }
    }
    
    # Validate MSI structure
    try {
        # Use Windows Installer API to validate MSI
        $installer = New-Object -ComObject WindowsInstaller.Installer
        $database = $installer.OpenDatabase($outputFile, 0)
        
        if ($database) {
            Write-Log "MSI structure validation: PASSED" "SUCCESS"
            $database = $null
        } else {
            Write-Log "MSI structure validation: FAILED" "ERROR"
            return $false
        }
    }
    catch {
        Write-Log "MSI validation error: $($_.Exception.Message)" "WARN"
    }
    finally {
        if ($installer) {
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($installer) | Out-Null
        }
    }
    
    Write-Log "Build output validation completed" "SUCCESS"
    return $true
}

function New-DistributionPackage {
    param([string]$OutputDir)
    
    Write-Log "Creating distribution package..." "INFO"
    
    try {
        $packageDir = Join-Path $OutputDir "Distribution"
        $packageName = "AlBayanConnect_v$productVersion_Build$buildNumber.zip"
        $packagePath = Join-Path $OutputDir $packageName
        
        # Create package directory
        if (Test-Path $packageDir) {
            Remove-Item -Path $packageDir -Recurse -Force
        }
        New-Item -Path $packageDir -ItemType Directory -Force | Out-Null
        
        # Copy installer
        $installerFile = Join-Path $OutputDir "AlBayanConnectInstaller.msi"
        Copy-Item -Path $installerFile -Destination $packageDir
        
        # Copy deployment scripts
        $scriptsSource = "Scripts"
        $scriptsDestination = Join-Path $packageDir "Scripts"
        Copy-Item -Path $scriptsSource -Destination $scriptsDestination -Recurse
        
        # Copy documentation
        $docsToInclude = @(
            "..\README.md",
            "..\LICENSE",
            "deployment-config.json"
        )
        
        foreach ($doc in $docsToInclude) {
            if (Test-Path $doc) {
                Copy-Item -Path $doc -Destination $packageDir
            }
        }
        
        # Create installation guide
        $installGuide = @"
# Al-Bayan Connect Installation Guide

## Quick Installation
1. Run AlBayanConnectInstaller.msi
2. Follow the installation wizard
3. The add-in will appear in Microsoft Office applications

## Silent Installation
For enterprise deployment, use:
```
Scripts\SilentInstall.bat
```

Or with PowerShell:
```
Scripts\EnterpriseDeployment.ps1
```

## Configuration
Edit deployment-config.json for enterprise settings.

## Support
- GitHub: https://github.com/al-bayan-ai/al-bayan-connect
- Website: https://al-bayan.ai
- Email: <EMAIL>

Version: $productVersion
Build: $buildNumber
Date: $(Get-Date -Format 'yyyy-MM-dd')
"@
        
        Set-Content -Path (Join-Path $packageDir "INSTALL.md") -Value $installGuide
        
        # Create ZIP package
        Add-Type -AssemblyName System.IO.Compression.FileSystem
        [System.IO.Compression.ZipFile]::CreateFromDirectory($packageDir, $packagePath)
        
        # Cleanup temporary directory
        Remove-Item -Path $packageDir -Recurse -Force
        
        Write-Log "Distribution package created: $packagePath" "SUCCESS"
        return $packagePath
    }
    catch {
        Write-Log "Package creation failed: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

function Main {
    Write-Log "=== Al-Bayan Connect Installer Build Started ===" "INFO"
    Write-Log "Script Version: $script:ScriptVersion" "INFO"
    Write-Log "Date: $(Get-Date)" "INFO"
    
    if ($Help) {
        Show-Help
        return 0
    }
    
    # Get version information
    $script:productVersion = Get-ProductVersion
    $script:buildNumber = Get-BuildNumber
    
    # Check build environment
    if (-not (Test-BuildEnvironment)) {
        return 1
    }
    
    # Update version information
    if (-not (Update-VersionInfo -ProductVersion $script:productVersion -BuildNum $script:buildNumber)) {
        return 2
    }
    
    # Determine output path
    $outputDir = if ($OutputPath) { $OutputPath } else { "bin\$Configuration" }
    
    # Execute build
    if (-not (Invoke-Build -Config $Configuration -Plat $Platform -Output $OutputPath)) {
        return 3
    }
    
    # Validate build output
    if (-not (Test-BuildOutput -OutputDir $outputDir)) {
        return 4
    }
    
    # Create distribution package if requested
    if ($Package) {
        $packagePath = New-DistributionPackage -OutputDir $outputDir
        if (-not $packagePath) {
            return 5
        }
    }
    
    Write-Log "=== Build Completed Successfully ===" "SUCCESS"
    Write-Log "Product Version: $script:productVersion" "INFO"
    Write-Log "Build Number: $script:buildNumber" "INFO"
    Write-Log "Output Directory: $outputDir" "INFO"
    
    if ($Package -and $packagePath) {
        Write-Log "Distribution Package: $packagePath" "INFO"
    }
    
    return 0
}

# Execute main function
$exitCode = Main
Write-Log "Build script completed with exit code: $exitCode" "INFO"
exit $exitCode
