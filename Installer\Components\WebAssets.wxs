<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://wixtoolset.org/schemas/v4/wxs"
     xmlns:util="http://wixtoolset.org/schemas/v4/wxs/util">

  <Fragment>
    
    <!-- Web Assets Component Group -->
    <ComponentGroup Id="WebAssetsComponents">
      <ComponentRef Id="WebAssetsComponent" />
      <ComponentRef Id="ManifestComponent" />
      <ComponentRef Id="TaskpaneComponent" />
      <ComponentRef Id="CommandsComponent" />
      <ComponentRef Id="AnalyticsComponent" />
      <ComponentRef Id="CustomCommandsComponent" />
      <ComponentRef Id="StaticAssetsComponent" />
      <ComponentRef Id="LocalizationComponent" />
    </ComponentGroup>

    <!-- Main Web Assets Component -->
    <Component Id="WebAssetsComponent" Directory="WebAssetsFolder" Guid="12345678-1234-1234-1234-123456789020">
      
      <!-- Main HTML Files -->
      <File Id="IndexHtml" Source="..\dist\index.html" KeyPath="yes" />
      <File Id="TaskpaneHtml" Source="..\dist\taskpane.html" />
      <File Id="CommandsHtml" Source="..\dist\commands.html" />
      <File Id="AnalyticsHtml" Source="..\dist\analytics.html" />
      <File Id="CustomCommandsHtml" Source="..\dist\custom-commands.html" />
      
      <!-- Main JavaScript Bundles -->
      <File Id="TaskpaneJs" Source="..\dist\taskpane.js" />
      <File Id="CommandsJs" Source="..\dist\commands.js" />
      <File Id="AnalyticsJs" Source="..\dist\analytics.js" />
      <File Id="CustomCommandsJs" Source="..\dist\custom-commands.js" />
      <File Id="VendorJs" Source="..\dist\vendor.js" />
      
      <!-- CSS Files -->
      <File Id="TaskpaneCss" Source="..\dist\taskpane.css" />
      <File Id="AnalyticsCss" Source="..\dist\analytics.css" />
      <File Id="CustomCommandsCss" Source="..\dist\custom-commands.css" />
      <File Id="CommonCss" Source="..\dist\common.css" />
      
      <!-- Service Worker -->
      <File Id="ServiceWorkerJs" Source="..\dist\sw.js" />
      
      <!-- Web App Manifest -->
      <File Id="WebAppManifest" Source="..\dist\manifest.json" />
      
    </Component>

    <!-- Office Add-in Manifest Component -->
    <Component Id="ManifestComponent" Directory="INSTALLFOLDER" Guid="12345678-1234-1234-1234-123456789021">
      
      <File Id="OfficeManifest" Source="..\manifest.xml" KeyPath="yes" Checksum="yes">
        <!-- Transform manifest to use local URLs -->
        <util:XmlFile Id="UpdateTaskpaneUrl"
                      Action="setValue"
                      File="[#OfficeManifest]"
                      ElementPath="//bt:Url[@id='Taskpane.Url']"
                      Name="DefaultValue"
                      Value="[INSTALLFOLDER]WebAssets\taskpane.html" />
        
        <util:XmlFile Id="UpdateCommandsUrl"
                      Action="setValue"
                      File="[#OfficeManifest]"
                      ElementPath="//bt:Url[@id='Commands.Url']"
                      Name="DefaultValue"
                      Value="[INSTALLFOLDER]WebAssets\commands.html" />
        
        <util:XmlFile Id="UpdateAnalyticsUrl"
                      Action="setValue"
                      File="[#OfficeManifest]"
                      ElementPath="//bt:Url[@id='Analytics.Url']"
                      Name="DefaultValue"
                      Value="[INSTALLFOLDER]WebAssets\analytics.html" />
      </File>
      
    </Component>

    <!-- Taskpane Assets Component -->
    <Component Id="TaskpaneComponent" Directory="WebAssetsFolder" Guid="12345678-1234-1234-1234-123456789022">
      
      <!-- Taskpane specific files -->
      <File Id="TaskpaneBundle" Source="..\dist\static\js\taskpane.bundle.js" />
      <File Id="TaskpaneCssBundle" Source="..\dist\static\css\taskpane.bundle.css" />
      
      <!-- React components -->
      <File Id="DictationPanelJs" Source="..\dist\static\js\components\DictationPanel.js" />
      <File Id="LanguageSelectorJs" Source="..\dist\static\js\components\LanguageSelector.js" />
      <File Id="ResultsDisplayJs" Source="..\dist\static\js\components\ResultsDisplay.js" />
      <File Id="SettingsPanelJs" Source="..\dist\static\js\components\SettingsPanel.js" />
      
      <!-- Services -->
      <File Id="DictationServiceJs" Source="..\dist\static\js\services\DictationService.js" />
      <File Id="LanguageDetectionServiceJs" Source="..\dist\static\js\services\LanguageDetectionService.js" />
      <File Id="OfficeIntegrationServiceJs" Source="..\dist\static\js\services\OfficeIntegrationService.js" />
      
    </Component>

    <!-- Commands Assets Component -->
    <Component Id="CommandsComponent" Directory="WebAssetsFolder" Guid="12345678-1234-1234-1234-123456789023">
      
      <!-- Commands specific files -->
      <File Id="CommandsBundle" Source="..\dist\static\js\commands.bundle.js" />
      <File Id="VoiceCommandProcessorJs" Source="..\dist\static\js\services\VoiceCommandProcessor.js" />
      
    </Component>

    <!-- Analytics Assets Component -->
    <Component Id="AnalyticsComponent" Directory="WebAssetsFolder" Guid="12345678-1234-1234-1234-123456789024">
      
      <!-- Analytics specific files -->
      <File Id="AnalyticsBundle" Source="..\dist\static\js\analytics.bundle.js" />
      <File Id="AnalyticsCssBundle" Source="..\dist\static\css\analytics.bundle.css" />
      <File Id="AnalyticsServiceJs" Source="..\dist\static\js\services\AnalyticsService.js" />
      <File Id="AnalyticsDashboardJs" Source="..\dist\static\js\components\AnalyticsDashboard.js" />
      
    </Component>

    <!-- Custom Commands Assets Component -->
    <Component Id="CustomCommandsComponent" Directory="WebAssetsFolder" Guid="12345678-1234-1234-1234-123456789025">
      
      <!-- Custom Commands specific files -->
      <File Id="CustomCommandsBundle" Source="..\dist\static\js\custom-commands.bundle.js" />
      <File Id="CustomCommandsCssBundle" Source="..\dist\static\css\custom-commands.bundle.css" />
      <File Id="CustomCommandBuilderJs" Source="..\dist\static\js\components\CustomCommandBuilder.js" />
      
    </Component>

    <!-- Static Assets Component -->
    <Component Id="StaticAssetsComponent" Directory="WebAssetsFolder" Guid="12345678-1234-1234-1234-123456789026">
      
      <!-- Images and Icons -->
      <File Id="AppIcon16" Source="..\assets\icons\icon-16.png" />
      <File Id="AppIcon32" Source="..\assets\icons\icon-32.png" />
      <File Id="AppIcon80" Source="..\assets\icons\icon-80.png" />
      <File Id="AppIcon128" Source="..\assets\icons\icon-128.png" />
      <File Id="AppIconSvg" Source="..\assets\icons\icon.svg" />
      
      <!-- Ribbon Icons -->
      <File Id="RibbonIcon16" Source="..\assets\icons\ribbon-16.png" />
      <File Id="RibbonIcon32" Source="..\assets\icons\ribbon-32.png" />
      <File Id="RibbonIcon80" Source="..\assets\icons\ribbon-80.png" />
      
      <!-- UI Images -->
      <File Id="LogoImage" Source="..\assets\images\logo.png" />
      <File Id="BannerImage" Source="..\assets\images\banner.png" />
      <File Id="WelcomeImage" Source="..\assets\images\welcome.png" />
      
      <!-- Audio Files -->
      <File Id="SuccessSound" Source="..\assets\audio\success.mp3" />
      <File Id="ErrorSound" Source="..\assets\audio\error.mp3" />
      <File Id="NotificationSound" Source="..\assets\audio\notification.mp3" />
      
      <!-- Fonts -->
      <File Id="ArabicFont" Source="..\assets\fonts\TraditionalArabic.woff2" />
      <File Id="ArabicFontWoff" Source="..\assets\fonts\TraditionalArabic.woff" />
      
    </Component>

    <!-- Localization Component -->
    <Component Id="LocalizationComponent" Directory="WebAssetsFolder" Guid="12345678-1234-1234-1234-123456789027">
      
      <!-- English Localization -->
      <File Id="LocaleEn" Source="..\dist\locales\en\messages.json" />
      <File Id="LocaleEnCommon" Source="..\dist\locales\en\common.json" />
      <File Id="LocaleEnErrors" Source="..\dist\locales\en\errors.json" />
      
      <!-- Arabic Localization -->
      <File Id="LocaleAr" Source="..\dist\locales\ar\messages.json" />
      <File Id="LocaleArCommon" Source="..\dist\locales\ar\common.json" />
      <File Id="LocaleArErrors" Source="..\dist\locales\ar\errors.json" />
      
    </Component>

  </Fragment>

  <!-- Web Assets Directory Structure -->
  <Fragment>
    <Directory Id="WebAssetsFolder" Name="WebAssets">
      <Directory Id="StaticFolder" Name="static">
        <Directory Id="JsFolder" Name="js">
          <Directory Id="ComponentsFolder" Name="components" />
          <Directory Id="ServicesFolder" Name="services" />
        </Directory>
        <Directory Id="CssFolder" Name="css" />
        <Directory Id="ImagesFolder" Name="images" />
        <Directory Id="FontsFolder" Name="fonts" />
        <Directory Id="AudioFolder" Name="audio" />
      </Directory>
      <Directory Id="LocalesFolder" Name="locales">
        <Directory Id="EnLocaleFolder" Name="en" />
        <Directory Id="ArLocaleFolder" Name="ar" />
      </Directory>
    </Directory>
  </Fragment>

  <!-- Web Assets Custom Actions -->
  <Fragment>
    
    <!-- Custom Action to Update Manifest URLs -->
    <CustomAction Id="UpdateManifestUrls"
                  Script="vbscript"
                  Execute="deferred"
                  Impersonate="yes">
      <![CDATA[
        ' Update manifest.xml with local file URLs
        Dim fso, manifestFile, content, installPath
        Set fso = CreateObject("Scripting.FileSystemObject")
        
        installPath = Session.Property("CustomActionData")
        manifestFile = installPath & "manifest.xml"
        
        If fso.FileExists(manifestFile) Then
            ' Read manifest content
            Set file = fso.OpenTextFile(manifestFile, 1)
            content = file.ReadAll
            file.Close
            
            ' Replace URLs with local paths
            content = Replace(content, "https://localhost:3000/taskpane.html", "file:///" & Replace(installPath & "WebAssets\taskpane.html", "\", "/"))
            content = Replace(content, "https://localhost:3000/commands.html", "file:///" & Replace(installPath & "WebAssets\commands.html", "\", "/"))
            content = Replace(content, "https://localhost:3000/analytics.html", "file:///" & Replace(installPath & "WebAssets\analytics.html", "\", "/"))
            content = Replace(content, "https://localhost:3000/custom-commands.html", "file:///" & Replace(installPath & "WebAssets\custom-commands.html", "\", "/"))
            
            ' Write updated content
            Set file = fso.OpenTextFile(manifestFile, 2)
            file.Write content
            file.Close
        End If
      ]]>
    </CustomAction>

    <!-- Custom Action to Create Web Server Configuration -->
    <CustomAction Id="CreateWebServerConfig"
                  Script="vbscript"
                  Execute="deferred"
                  Impersonate="yes">
      <![CDATA[
        ' Create local web server configuration for development
        Dim fso, configFile, installPath, content
        Set fso = CreateObject("Scripting.FileSystemObject")
        
        installPath = Session.Property("CustomActionData")
        configFile = installPath & "WebAssets\server.config"
        
        content = "{" & vbCrLf
        content = content & "  ""port"": 8443," & vbCrLf
        content = content & "  ""https"": true," & vbCrLf
        content = content & "  ""cert"": ""localhost.crt""," & vbCrLf
        content = content & "  ""key"": ""localhost.key""," & vbCrLf
        content = content & "  ""root"": """ & Replace(installPath & "WebAssets", "\", "\\") & """," & vbCrLf
        content = content & "  ""cors"": true" & vbCrLf
        content = content & "}"
        
        Set file = fso.CreateTextFile(configFile, True)
        file.Write content
        file.Close
      ]]>
    </CustomAction>

    <!-- Custom Action to Set File Permissions -->
    <CustomAction Id="SetWebAssetsPermissions"
                  Script="vbscript"
                  Execute="deferred"
                  Impersonate="yes">
      <![CDATA[
        ' Set appropriate permissions for web assets
        Dim shell, installPath, webAssetsPath
        Set shell = CreateObject("WScript.Shell")
        
        installPath = Session.Property("CustomActionData")
        webAssetsPath = installPath & "WebAssets"
        
        ' Grant read permissions to current user
        shell.Run "icacls """ & webAssetsPath & """ /grant %USERNAME%:R /T", 0, True
      ]]>
    </CustomAction>

  </Fragment>

  <!-- Web Assets Installation Sequence -->
  <Fragment>
    
    <InstallExecuteSequence>
      <Custom Action="UpdateManifestUrls" After="InstallFiles">NOT Installed</Custom>
      <Custom Action="CreateWebServerConfig" After="UpdateManifestUrls">NOT Installed</Custom>
      <Custom Action="SetWebAssetsPermissions" After="CreateWebServerConfig">NOT Installed</Custom>
    </InstallExecuteSequence>

  </Fragment>

</Wix>
