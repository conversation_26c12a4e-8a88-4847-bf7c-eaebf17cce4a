// Al-Bayan Connect - Results Display Component
// Author: Dr. <PERSON>
// Copyright: © 2025 Al-Bayan AI Platform

import React from 'react';
import { Button, Text } from '@fluentui/react-components';
import { Delete24Regular, Copy24Regular } from '@fluentui/react-icons';
import { SpeechRecognitionResult, Language } from '@types/index';

interface ResultsDisplayProps {
  results: SpeechRecognitionResult[];
  currentLanguage: Language;
  onClear: () => void;
}

export const ResultsDisplay: React.FC<ResultsDisplayProps> = ({
  results,
  currentLanguage,
  onClear
}) => {
  const formatConfidence = (confidence: number): string => {
    const percentage = Math.round(confidence * 100);
    return `${percentage}%`;
  };

  const getConfidenceClass = (confidence: number): string => {
    if (confidence >= 0.8) return 'high';
    if (confidence >= 0.6) return 'medium';
    return 'low';
  };

  const formatTimestamp = (timestamp: Date): string => {
    return timestamp.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit', 
      second: '2-digit' 
    });
  };

  const copyAllResults = () => {
    const allText = results
      .map(result => result.transcript)
      .join(' ');
    
    navigator.clipboard.writeText(allText).catch(err => {
      console.error('Failed to copy text:', err);
    });
  };

  const copyResult = (text: string) => {
    navigator.clipboard.writeText(text).catch(err => {
      console.error('Failed to copy text:', err);
    });
  };

  return (
    <div className="results-section">
      <div className="results-header">
        <Text className="results-title">
          {currentLanguage === Language.ARABIC ? 'النتائج' : 'Results'} 
          ({results.length})
        </Text>
        
        <div style={{ display: 'flex', gap: '8px' }}>
          {results.length > 0 && (
            <>
              <Button
                icon={<Copy24Regular />}
                size="small"
                appearance="subtle"
                onClick={copyAllResults}
                title="Copy all results"
              >
                Copy All
              </Button>
              
              <Button
                icon={<Delete24Regular />}
                size="small"
                appearance="subtle"
                onClick={onClear}
                title="Clear all results"
              >
                Clear
              </Button>
            </>
          )}
        </div>
      </div>

      <div className="results-container">
        {results.length === 0 ? (
          <div style={{ 
            textAlign: 'center', 
            padding: '40px 20px',
            color: '#605e5c'
          }}>
            <Text>
              {currentLanguage === Language.ARABIC ? 
                'لا توجد نتائج بعد. ابدأ الإملاء لرؤية النتائج هنا.' :
                'No results yet. Start dictating to see results here.'
              }
            </Text>
          </div>
        ) : (
          results.map((result, index) => (
            <div
              key={index}
              className={`result-item ${result.language === Language.ARABIC ? 'arabic' : 'english'}`}
            >
              <Text className="result-text">
                {result.transcript}
              </Text>
              
              <div className="result-meta">
                <div>
                  <Text size={100}>
                    {formatTimestamp(result.timestamp)}
                  </Text>
                  <Text size={100} style={{ marginLeft: '8px' }}>
                    {result.language === Language.ARABIC ? 'عربي' : 'English'}
                  </Text>
                </div>
                
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <Text 
                    size={100}
                    className={`result-confidence ${getConfidenceClass(result.confidence)}`}
                  >
                    {formatConfidence(result.confidence)}
                  </Text>
                  
                  <Button
                    icon={<Copy24Regular />}
                    size="small"
                    appearance="transparent"
                    onClick={() => copyResult(result.transcript)}
                    title="Copy this result"
                    style={{ minWidth: 'auto', padding: '2px' }}
                  />
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {results.length > 0 && (
        <div style={{ 
          marginTop: '12px', 
          padding: '8px', 
          background: '#f8f9fa', 
          borderRadius: '4px',
          fontSize: '12px',
          color: '#605e5c'
        }}>
          <Text size={100}>
            Total words: {results.reduce((total, result) => 
              total + result.transcript.trim().split(/\s+/).length, 0
            )} | 
            Average confidence: {results.length > 0 ? 
              Math.round((results.reduce((sum, result) => sum + result.confidence, 0) / results.length) * 100) : 0
            }%
          </Text>
        </div>
      )}
    </div>
  );
};
