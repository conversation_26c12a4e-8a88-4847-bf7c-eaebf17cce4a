# Al-<PERSON><PERSON> Connect - Code Signing Script
# Author: Dr. <PERSON>
# Copyright: © 2025 Al-Bayan AI Platform

param(
    [Parameter(Mandatory=$true)]
    [string]$FilePath,
    
    [string]$CertificatePath = "",
    [string]$CertificatePassword = "",
    [string]$CertificateThumbprint = "",
    [string]$TimestampServer = "http://timestamp.digicert.com",
    [string]$HashAlgorithm = "SHA256",
    [switch]$Verify,
    [switch]$Force,
    [switch]$Verbose,
    [switch]$Help
)

# Configuration
$script:ScriptVersion = "1.0.0"
$script:SupportedExtensions = @(".exe", ".dll", ".msi", ".cab", ".ocx")
$script:DefaultTimestampServers = @(
    "http://timestamp.digicert.com",
    "http://timestamp.comodoca.com",
    "http://timestamp.verisign.com/scripts/timstamp.dll",
    "http://tsa.starfieldtech.com"
)

function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    if ($Verbose -or $Level -eq "ERROR" -or $Level -eq "WARN") {
        Write-Host $logEntry -ForegroundColor $(
            switch ($Level) {
                "ERROR" { "Red" }
                "WARN" { "Yellow" }
                "SUCCESS" { "Green" }
                default { "White" }
            }
        )
    }
}

function Show-Help {
    Write-Host @"

Al-Bayan Connect Code Signing Script v$script:ScriptVersion

DESCRIPTION:
    Signs executable files and validates digital signatures for Al-Bayan Connect
    installer and components.

SYNTAX:
    .\CodeSigning.ps1 -FilePath <path> [OPTIONS]

PARAMETERS:
    -FilePath <path>              Path to file to sign or verify
    -CertificatePath <path>       Path to certificate file (.pfx/.p12)
    -CertificatePassword <pwd>    Certificate password
    -CertificateThumbprint <hash> Certificate thumbprint (for store certificates)
    -TimestampServer <url>        Timestamp server URL
    -HashAlgorithm <algorithm>    Hash algorithm (SHA256/SHA1)
    -Verify                       Verify existing signature instead of signing
    -Force                        Force re-signing even if already signed
    -Verbose                      Enable verbose output
    -Help                         Show this help message

EXAMPLES:
    # Sign with certificate file
    .\CodeSigning.ps1 -FilePath "AlBayanConnectInstaller.msi" -CertificatePath "cert.pfx" -CertificatePassword "password"

    # Sign with certificate from store
    .\CodeSigning.ps1 -FilePath "AlBayanConnectInstaller.msi" -CertificateThumbprint "1234567890ABCDEF"

    # Verify signature
    .\CodeSigning.ps1 -FilePath "AlBayanConnectInstaller.msi" -Verify

    # Batch sign multiple files
    Get-ChildItem "*.exe", "*.dll", "*.msi" | ForEach-Object { .\CodeSigning.ps1 -FilePath $_.FullName -CertificateThumbprint "1234567890ABCDEF" }

CERTIFICATE REQUIREMENTS:
    - Code signing certificate from trusted CA
    - Extended Validation (EV) certificate recommended
    - Certificate must be valid and not expired
    - Private key must be available

SUPPORTED FILE TYPES:
    .exe, .dll, .msi, .cab, .ocx

"@
}

function Test-Prerequisites {
    Write-Log "Checking prerequisites..." "INFO"
    
    # Check if signtool.exe is available
    $signtool = Get-Command "signtool.exe" -ErrorAction SilentlyContinue
    if (-not $signtool) {
        # Try to find signtool in Windows SDK
        $sdkPaths = @(
            "${env:ProgramFiles(x86)}\Windows Kits\10\bin\*\x64\signtool.exe",
            "${env:ProgramFiles(x86)}\Windows Kits\8.1\bin\x64\signtool.exe",
            "${env:ProgramFiles(x86)}\Windows Kits\8.0\bin\x64\signtool.exe",
            "${env:ProgramFiles}\Microsoft SDKs\Windows\*\bin\signtool.exe"
        )
        
        foreach ($path in $sdkPaths) {
            $found = Get-ChildItem $path -ErrorAction SilentlyContinue | Select-Object -First 1
            if ($found) {
                $env:PATH += ";$(Split-Path $found.FullName)"
                Write-Log "Found signtool.exe at: $($found.FullName)" "INFO"
                break
            }
        }
        
        # Check again
        $signtool = Get-Command "signtool.exe" -ErrorAction SilentlyContinue
        if (-not $signtool) {
            Write-Log "signtool.exe not found. Please install Windows SDK." "ERROR"
            return $false
        }
    }
    
    Write-Log "Using signtool.exe from: $($signtool.Source)" "INFO"
    return $true
}

function Get-Certificate {
    if (-not [string]::IsNullOrEmpty($CertificateThumbprint)) {
        # Get certificate from store
        $cert = Get-ChildItem -Path "Cert:\CurrentUser\My" | Where-Object { $_.Thumbprint -eq $CertificateThumbprint }
        if (-not $cert) {
            $cert = Get-ChildItem -Path "Cert:\LocalMachine\My" | Where-Object { $_.Thumbprint -eq $CertificateThumbprint }
        }
        
        if ($cert) {
            Write-Log "Found certificate in store: $($cert.Subject)" "INFO"
            return @{
                Type = "Store"
                Thumbprint = $CertificateThumbprint
                Subject = $cert.Subject
                Issuer = $cert.Issuer
                NotAfter = $cert.NotAfter
            }
        } else {
            Write-Log "Certificate with thumbprint $CertificateThumbprint not found in certificate store" "ERROR"
            return $null
        }
    } elseif (-not [string]::IsNullOrEmpty($CertificatePath)) {
        # Validate certificate file
        if (-not (Test-Path $CertificatePath)) {
            Write-Log "Certificate file not found: $CertificatePath" "ERROR"
            return $null
        }
        
        try {
            $cert = New-Object System.Security.Cryptography.X509Certificates.X509Certificate2($CertificatePath, $CertificatePassword)
            Write-Log "Certificate file loaded: $($cert.Subject)" "INFO"
            return @{
                Type = "File"
                Path = $CertificatePath
                Password = $CertificatePassword
                Subject = $cert.Subject
                Issuer = $cert.Issuer
                NotAfter = $cert.NotAfter
            }
        } catch {
            Write-Log "Failed to load certificate file: $($_.Exception.Message)" "ERROR"
            return $null
        }
    } else {
        Write-Log "No certificate specified. Use -CertificatePath or -CertificateThumbprint parameter." "ERROR"
        return $null
    }
}

function Test-FileSignature {
    param([string]$Path)
    
    try {
        $signature = Get-AuthenticodeSignature -FilePath $Path
        
        $result = @{
            IsSigned = $signature.Status -eq "Valid"
            Status = $signature.Status
            SignerCertificate = $signature.SignerCertificate
            TimeStamperCertificate = $signature.TimeStamperCertificate
            StatusMessage = $signature.StatusMessage
        }
        
        if ($result.IsSigned) {
            Write-Log "File is digitally signed" "SUCCESS"
            Write-Log "  Signer: $($signature.SignerCertificate.Subject)" "INFO"
            Write-Log "  Issuer: $($signature.SignerCertificate.Issuer)" "INFO"
            Write-Log "  Valid until: $($signature.SignerCertificate.NotAfter)" "INFO"
            
            if ($signature.TimeStamperCertificate) {
                Write-Log "  Timestamp: $($signature.TimeStamperCertificate.Subject)" "INFO"
            }
        } else {
            Write-Log "File signature status: $($signature.Status)" "WARN"
            if ($signature.StatusMessage) {
                Write-Log "  Message: $($signature.StatusMessage)" "WARN"
            }
        }
        
        return $result
    } catch {
        Write-Log "Failed to check file signature: $($_.Exception.Message)" "ERROR"
        return @{ IsSigned = $false; Status = "Error"; StatusMessage = $_.Exception.Message }
    }
}

function Invoke-FileSigning {
    param(
        [string]$Path,
        [object]$Certificate
    )
    
    Write-Log "Signing file: $Path" "INFO"
    
    # Build signtool command
    $signtoolArgs = @("sign")
    
    if ($Certificate.Type -eq "Store") {
        $signtoolArgs += "/sha1", $Certificate.Thumbprint
    } else {
        $signtoolArgs += "/f", "`"$($Certificate.Path)`""
        if (-not [string]::IsNullOrEmpty($Certificate.Password)) {
            $signtoolArgs += "/p", $Certificate.Password
        }
    }
    
    # Add hash algorithm
    $signtoolArgs += "/fd", $HashAlgorithm
    
    # Add timestamp server
    $timestampSuccess = $false
    foreach ($server in $script:DefaultTimestampServers) {
        try {
            $tempArgs = $signtoolArgs + @("/tr", $server, "/td", $HashAlgorithm, "`"$Path`"")
            Write-Log "Attempting to sign with timestamp server: $server" "INFO"
            
            $result = Start-Process -FilePath "signtool.exe" -ArgumentList $tempArgs -Wait -PassThru -NoNewWindow -RedirectStandardOutput "$env:TEMP\signtool_output.txt" -RedirectStandardError "$env:TEMP\signtool_error.txt"
            
            if ($result.ExitCode -eq 0) {
                Write-Log "File signed successfully with timestamp" "SUCCESS"
                $timestampSuccess = $true
                break
            } else {
                $errorOutput = Get-Content "$env:TEMP\signtool_error.txt" -ErrorAction SilentlyContinue
                Write-Log "Timestamp server failed: $server - $errorOutput" "WARN"
            }
        } catch {
            Write-Log "Error with timestamp server $server : $($_.Exception.Message)" "WARN"
        }
    }
    
    # If timestamp failed, sign without timestamp
    if (-not $timestampSuccess) {
        Write-Log "All timestamp servers failed. Signing without timestamp..." "WARN"
        $finalArgs = $signtoolArgs + @("`"$Path`"")
        
        $result = Start-Process -FilePath "signtool.exe" -ArgumentList $finalArgs -Wait -PassThru -NoNewWindow -RedirectStandardOutput "$env:TEMP\signtool_output.txt" -RedirectStandardError "$env:TEMP\signtool_error.txt"
        
        if ($result.ExitCode -eq 0) {
            Write-Log "File signed successfully (without timestamp)" "SUCCESS"
            return $true
        } else {
            $errorOutput = Get-Content "$env:TEMP\signtool_error.txt" -ErrorAction SilentlyContinue
            Write-Log "Signing failed: $errorOutput" "ERROR"
            return $false
        }
    }
    
    return $timestampSuccess
}

function Main {
    Write-Log "=== Al-Bayan Connect Code Signing Started ===" "INFO"
    Write-Log "Script Version: $script:ScriptVersion" "INFO"
    Write-Log "Date: $(Get-Date)" "INFO"
    
    if ($Help) {
        Show-Help
        return 0
    }
    
    # Validate file path
    if (-not (Test-Path $FilePath)) {
        Write-Log "File not found: $FilePath" "ERROR"
        return 1
    }
    
    $fileInfo = Get-Item $FilePath
    Write-Log "Target file: $($fileInfo.FullName)" "INFO"
    Write-Log "File size: $([math]::Round($fileInfo.Length / 1MB, 2)) MB" "INFO"
    
    # Check if file type is supported
    if ($fileInfo.Extension -notin $script:SupportedExtensions) {
        Write-Log "File type $($fileInfo.Extension) is not supported for code signing" "ERROR"
        return 2
    }
    
    # Check prerequisites
    if (-not (Test-Prerequisites)) {
        return 3
    }
    
    if ($Verify) {
        # Verify existing signature
        Write-Log "Verifying digital signature..." "INFO"
        $signatureResult = Test-FileSignature -Path $FilePath
        
        if ($signatureResult.IsSigned) {
            Write-Log "Signature verification: PASSED" "SUCCESS"
            return 0
        } else {
            Write-Log "Signature verification: FAILED" "ERROR"
            return 4
        }
    } else {
        # Sign the file
        
        # Check if already signed
        $existingSignature = Test-FileSignature -Path $FilePath
        if ($existingSignature.IsSigned -and -not $Force) {
            Write-Log "File is already signed. Use -Force to re-sign." "WARN"
            return 0
        }
        
        # Get certificate
        $certificate = Get-Certificate
        if (-not $certificate) {
            return 5
        }
        
        # Validate certificate expiration
        if ($certificate.NotAfter -lt (Get-Date)) {
            Write-Log "Certificate has expired: $($certificate.NotAfter)" "ERROR"
            return 6
        }
        
        if ($certificate.NotAfter -lt (Get-Date).AddDays(30)) {
            Write-Log "Certificate expires soon: $($certificate.NotAfter)" "WARN"
        }
        
        # Sign the file
        $signingResult = Invoke-FileSigning -Path $FilePath -Certificate $certificate
        
        if ($signingResult) {
            # Verify the signature
            $verificationResult = Test-FileSignature -Path $FilePath
            if ($verificationResult.IsSigned) {
                Write-Log "File signing completed successfully" "SUCCESS"
                return 0
            } else {
                Write-Log "File was signed but verification failed" "ERROR"
                return 7
            }
        } else {
            Write-Log "File signing failed" "ERROR"
            return 8
        }
    }
}

# Execute main function
$exitCode = Main
Write-Log "=== Code Signing Completed with exit code: $exitCode ===" "INFO"
exit $exitCode
