<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" InitialTargets="EnsureWixToolsetInstalled" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x86</Platform>
    <ProductVersion>4.0</ProductVersion>
    <ProjectGuid>12345678-1234-1234-1234-123456789012</ProjectGuid>
    <SchemaVersion>2.0</SchemaVersion>
    <OutputName>AlBayanConnectInstaller</OutputName>
    <OutputType>Package</OutputType>
    <WixTargetsPath Condition=" '$(WixTargetsPath)' == '' AND '$(MSBuildExtensionsPath32)' != '' ">$(MSBuildExtensionsPath32)\Microsoft\WiX\v4.0\Wix.targets</WixTargetsPath>
    <WixTargetsPath Condition=" '$(WixTargetsPath)' == '' ">$(MSBuildExtensionsPath)\Microsoft\WiX\v4.0\Wix.targets</WixTargetsPath>
    <Name>Al-Bayan Connect Installer</Name>
  </PropertyGroup>
  
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
    <OutputPath>bin\$(Configuration)\</OutputPath>
    <IntermediateOutputPath>obj\$(Configuration)\</IntermediateOutputPath>
    <DefineConstants>Debug</DefineConstants>
    <SuppressValidation>false</SuppressValidation>
    <SuppressIces>ICE03;ICE60;ICE82</SuppressIces>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <Verbosity>3</Verbosity>
  </PropertyGroup>
  
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x86' ">
    <OutputPath>bin\$(Configuration)\</OutputPath>
    <IntermediateOutputPath>obj\$(Configuration)\</IntermediateOutputPath>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <Verbosity>2</Verbosity>
    <SuppressValidation>false</SuppressValidation>
    <SuppressIces>ICE03;ICE60;ICE82</SuppressIces>
  </PropertyGroup>
  
  <ItemGroup>
    <Compile Include="Product.wxs" />
    <Compile Include="Components\OfficeAddin.wxs" />
    <Compile Include="Components\WebAssets.wxs" />
    <Compile Include="Components\Prerequisites.wxs" />
    <Compile Include="UI\InstallerUI.wxs" />
    <Compile Include="UI\Dialogs.wxs" />
    <Compile Include="Localization\Strings_en.wxl" />
    <Compile Include="Localization\Strings_ar.wxl" />
  </ItemGroup>
  
  <ItemGroup>
    <Folder Include="Components\" />
    <Folder Include="UI\" />
    <Folder Include="Localization\" />
    <Folder Include="Resources\" />
    <Folder Include="Scripts\" />
    <Folder Include="Assets\" />
  </ItemGroup>
  
  <ItemGroup>
    <Content Include="Resources\banner.bmp" />
    <Content Include="Resources\dialog.bmp" />
    <Content Include="Resources\icon.ico" />
    <Content Include="Resources\license.rtf" />
    <Content Include="Scripts\OfficeRegistration.vbs" />
    <Content Include="Scripts\PrerequisiteCheck.vbs" />
    <Content Include="Scripts\Cleanup.vbs" />
  </ItemGroup>
  
  <ItemGroup>
    <WixExtension Include="WixToolset.UI.wixext" />
    <WixExtension Include="WixToolset.Util.wixext" />
    <WixExtension Include="WixToolset.NetFx.wixext" />
    <WixExtension Include="WixToolset.Bal.wixext" />
  </ItemGroup>
  
  <Import Project="$(WixTargetsPath)" Condition=" '$(WixTargetsPath)' != '' " />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\WiX\v4.0\Wix.targets" Condition=" '$(WixTargetsPath)' == '' AND Exists('$(MSBuildExtensionsPath32)\Microsoft\WiX\v4.0\Wix.targets') " />
  
  <Target Name="EnsureWixToolsetInstalled" Condition=" '$(WixTargetsImported)' != 'true' ">
    <Error Text="The WiX Toolset v4.0 (or newer) build tools must be installed to build this project. To download the WiX Toolset, see http://wixtoolset.org/releases/" />
  </Target>
  
  <!-- Custom build targets -->
  <Target Name="BeforeBuild">
    <Message Text="Building Al-Bayan Connect Installer..." />
    <ItemGroup>
      <WebAssetFiles Include="..\dist\**\*.*" />
    </ItemGroup>
  </Target>

  <Target Name="AfterBuild" DependsOnTargets="SignInstaller">
    <Message Text="Al-Bayan Connect Installer build completed successfully!" />
  </Target>

  <!-- Code Signing Target -->
  <Target Name="SignInstaller" Condition="'$(SignOutput)' == 'true'">
    <Message Text="Signing Al-Bayan Connect Installer..." />

    <!-- Sign the MSI package -->
    <Exec Command="powershell.exe -ExecutionPolicy Bypass -File &quot;$(MSBuildProjectDirectory)\Scripts\CodeSigning.ps1&quot; -FilePath &quot;$(OutputPath)$(OutputName).msi&quot; -CertificateThumbprint &quot;$(CodeSigningCertThumbprint)&quot; -Verbose"
          Condition="'$(CodeSigningCertThumbprint)' != ''"
          ContinueOnError="false" />

    <!-- Alternative: Sign with certificate file -->
    <Exec Command="powershell.exe -ExecutionPolicy Bypass -File &quot;$(MSBuildProjectDirectory)\Scripts\CodeSigning.ps1&quot; -FilePath &quot;$(OutputPath)$(OutputName).msi&quot; -CertificatePath &quot;$(CodeSigningCertPath)&quot; -CertificatePassword &quot;$(CodeSigningCertPassword)&quot; -Verbose"
          Condition="'$(CodeSigningCertPath)' != '' And '$(CodeSigningCertThumbprint)' == ''"
          ContinueOnError="false" />

    <!-- Verify signature after signing -->
    <Exec Command="powershell.exe -ExecutionPolicy Bypass -File &quot;$(MSBuildProjectDirectory)\Scripts\CodeSigning.ps1&quot; -FilePath &quot;$(OutputPath)$(OutputName).msi&quot; -Verify -Verbose"
          ContinueOnError="false" />

    <Message Text="Installer signing completed successfully!" />
  </Target>

  <!-- Certificate Validation Target -->
  <Target Name="ValidateCertificates" BeforeTargets="Build" Condition="'$(ValidateCerts)' == 'true'">
    <Message Text="Validating certificates..." />

    <Exec Command="cscript.exe &quot;$(MSBuildProjectDirectory)\Scripts\CertificateValidation.vbs&quot; &quot;$(OutputPath)$(OutputName).msi&quot; //NoLogo"
          ContinueOnError="false" />

    <Message Text="Certificate validation completed!" />
  </Target>
</Project>
