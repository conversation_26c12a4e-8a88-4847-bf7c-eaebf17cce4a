<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://wixtoolset.org/schemas/v4/wxs"
     xmlns:util="http://wixtoolset.org/schemas/v4/wxs/util">

  <Fragment>
    
    <!-- Office Add-in Components -->
    <ComponentGroup Id="OfficeAddinComponents">
      <ComponentRef Id="OfficeAddinManifest" />
      <ComponentRef Id="OfficeRegistryEntries" />
      <ComponentRef Id="TrustedCatalogEntries" />
      <ComponentRef Id="OfficeAddinCleanup" />
    </ComponentGroup>

    <!-- Main Executable Component -->
    <Component Id="MainExecutable" Directory="INSTALLFOLDER" Guid="12345678-1234-1234-1234-123456789001">
      <File Id="LauncherExe" 
            Source="$(var.SolutionDir)Launcher\bin\Release\AlBayanConnectLauncher.exe" 
            KeyPath="yes" 
            Checksum="yes">
        <Shortcut Id="StartMenuShortcut"
                  Directory="ApplicationProgramsFolder"
                  Name="!(loc.ProductName)"
                  Description="!(loc.ProductDescription)"
                  WorkingDirectory="INSTALLFOLDER"
                  Icon="ProductIcon" />
      </File>
      
      <!-- Configuration file -->
      <File Id="LauncherConfig" 
            Source="$(var.SolutionDir)Launcher\bin\Release\AlBayanConnectLauncher.exe.config" />
    </Component>

    <!-- Office Add-in Manifest Component -->
    <Component Id="OfficeAddinManifest" Directory="INSTALLFOLDER" Guid="12345678-1234-1234-1234-123456789002">
      <File Id="ManifestXml" 
            Source="..\manifest.xml" 
            KeyPath="yes" 
            Checksum="yes" />
      
      <!-- Create registry entry for manifest location -->
      <RegistryValue Root="HKCU" 
                     Key="Software\Al-Bayan\Connect\Installation" 
                     Name="ManifestPath" 
                     Value="[INSTALLFOLDER]manifest.xml" 
                     Type="string" />
    </Component>

    <!-- Office Registry Entries Component -->
    <Component Id="OfficeRegistryEntries" Directory="INSTALLFOLDER" Guid="12345678-1234-1234-1234-123456789003">
      
      <!-- Office 2016/2019/365 Registry Entries -->
      <RegistryKey Root="HKCU" Key="Software\Microsoft\Office\16.0\WEF\TrustedCatalogs\{al-bayan-connect-catalog}">
        <RegistryValue Name="Id" Value="{al-bayan-connect-addin-id}" Type="string" />
        <RegistryValue Name="Url" Value="[INSTALLFOLDER]manifest.xml" Type="string" />
        <RegistryValue Name="Flags" Value="1" Type="integer" />
      </RegistryKey>

      <!-- Office 2013 Support (if needed) -->
      <RegistryKey Root="HKCU" Key="Software\Microsoft\Office\15.0\WEF\TrustedCatalogs\{al-bayan-connect-catalog-15}">
        <RegistryValue Name="Id" Value="{al-bayan-connect-addin-id}" Type="string" />
        <RegistryValue Name="Url" Value="[INSTALLFOLDER]manifest.xml" Type="string" />
        <RegistryValue Name="Flags" Value="1" Type="integer" />
      </RegistryKey>

      <!-- Add-in Installation Registry -->
      <RegistryKey Root="HKCU" Key="Software\Al-Bayan\Connect">
        <RegistryValue Name="InstallPath" Value="[INSTALLFOLDER]" Type="string" />
        <RegistryValue Name="Version" Value="1.0.0.0" Type="string" />
        <RegistryValue Name="InstallDate" Value="[Date]" Type="string" />
        <RegistryValue Name="Language" Value="[LANGUAGE_SELECTION]" Type="string" />
      </RegistryKey>

      <!-- Uninstall Information -->
      <RegistryKey Root="HKCU" Key="Software\Microsoft\Windows\CurrentVersion\Uninstall\{ProductCode}">
        <RegistryValue Name="DisplayName" Value="!(loc.ProductName)" Type="string" />
        <RegistryValue Name="DisplayVersion" Value="1.0.0.0" Type="string" />
        <RegistryValue Name="Publisher" Value="!(loc.Manufacturer)" Type="string" />
        <RegistryValue Name="InstallLocation" Value="[INSTALLFOLDER]" Type="string" />
        <RegistryValue Name="UninstallString" Value="msiexec /x [ProductCode]" Type="string" />
        <RegistryValue Name="NoModify" Value="1" Type="integer" />
        <RegistryValue Name="NoRepair" Value="1" Type="integer" />
      </RegistryKey>

      <!-- File Association for .albayan files -->
      <RegistryKey Root="HKCU" Key="Software\Classes\.albayan">
        <RegistryValue Value="AlBayanConnect.CommandFile" Type="string" />
      </RegistryKey>
      
      <RegistryKey Root="HKCU" Key="Software\Classes\AlBayanConnect.CommandFile">
        <RegistryValue Value="Al-Bayan Connect Command File" Type="string" />
        <RegistryValue Name="DefaultIcon" Value="[INSTALLFOLDER]AlBayanConnectLauncher.exe,0" Type="string" />
      </RegistryKey>
      
      <RegistryKey Root="HKCU" Key="Software\Classes\AlBayanConnect.CommandFile\shell\open\command">
        <RegistryValue Value="&quot;[INSTALLFOLDER]AlBayanConnectLauncher.exe&quot; &quot;%1&quot;" Type="string" />
      </RegistryKey>

    </Component>

    <!-- Trusted Catalog Entries Component -->
    <Component Id="TrustedCatalogEntries" Directory="INSTALLFOLDER" Guid="12345678-1234-1234-1234-123456789004">
      
      <!-- Custom Action Data for Registration -->
      <RegistryValue Root="HKCU" 
                     Key="Software\Al-Bayan\Connect\Registration" 
                     Name="CatalogId" 
                     Value="{al-bayan-connect-catalog}" 
                     Type="string" 
                     KeyPath="yes" />

      <!-- Office Trust Settings -->
      <RegistryValue Root="HKCU" 
                     Key="Software\Microsoft\Office\16.0\Common\Security\Trusted Locations\Location99" 
                     Name="Path" 
                     Value="[INSTALLFOLDER]" 
                     Type="string" />
      
      <RegistryValue Root="HKCU" 
                     Key="Software\Microsoft\Office\16.0\Common\Security\Trusted Locations\Location99" 
                     Name="Description" 
                     Value="Al-Bayan Connect Installation Directory" 
                     Type="string" />
      
      <RegistryValue Root="HKCU" 
                     Key="Software\Microsoft\Office\16.0\Common\Security\Trusted Locations\Location99" 
                     Name="AllowSubFolders" 
                     Value="1" 
                     Type="integer" />

    </Component>

    <!-- Office Add-in Cleanup Component -->
    <Component Id="OfficeAddinCleanup" Directory="INSTALLFOLDER" Guid="12345678-1234-1234-1234-123456789005">
      
      <!-- Cleanup Registry Entry -->
      <RegistryValue Root="HKCU" 
                     Key="Software\Al-Bayan\Connect\Cleanup" 
                     Name="RequiresCleanup" 
                     Value="1" 
                     Type="integer" 
                     KeyPath="yes" />

      <!-- Custom Action for Office Process Cleanup -->
      <util:CloseApplication Id="CloseOfficeApps" 
                             Target="WINWORD.EXE" 
                             CloseMessage="yes" 
                             Description="!(loc.CloseOfficeMessage)" />
      
      <util:CloseApplication Id="ClosePowerPoint" 
                             Target="POWERPNT.EXE" 
                             CloseMessage="yes" 
                             Description="!(loc.CloseOfficeMessage)" />
      
      <util:CloseApplication Id="CloseOutlook" 
                             Target="OUTLOOK.EXE" 
                             CloseMessage="yes" 
                             Description="!(loc.CloseOfficeMessage)" />

    </Component>

    <!-- Start Menu Shortcuts Component -->
    <Component Id="StartMenuShortcuts" Directory="ApplicationProgramsFolder" Guid="12345678-1234-1234-1234-123456789006">
      
      <!-- Main Application Shortcut -->
      <Shortcut Id="ApplicationStartMenuShortcut"
                Name="!(loc.ProductName)"
                Description="!(loc.ProductDescription)"
                Target="[INSTALLFOLDER]AlBayanConnectLauncher.exe"
                WorkingDirectory="INSTALLFOLDER"
                Icon="ProductIcon" />

      <!-- Settings Shortcut -->
      <Shortcut Id="SettingsStartMenuShortcut"
                Name="!(loc.ProductName) Settings"
                Description="Configure Al-Bayan Connect settings"
                Target="[INSTALLFOLDER]AlBayanConnectLauncher.exe"
                Arguments="/settings"
                WorkingDirectory="INSTALLFOLDER"
                Icon="ProductIcon" />

      <!-- Uninstall Shortcut -->
      <Shortcut Id="UninstallStartMenuShortcut"
                Name="Uninstall !(loc.ProductName)"
                Description="Remove Al-Bayan Connect from your computer"
                Target="msiexec.exe"
                Arguments="/x [ProductCode]"
                Icon="ProductIcon" />

      <!-- Registry key for Start Menu folder -->
      <RegistryValue Root="HKCU" 
                     Key="Software\Al-Bayan\Connect\Installation" 
                     Name="StartMenuFolder" 
                     Value="[ApplicationProgramsFolder]" 
                     Type="string" 
                     KeyPath="yes" />

      <!-- Remove Start Menu folder on uninstall -->
      <RemoveFolder Id="ApplicationProgramsFolder" On="uninstall" />

    </Component>

    <!-- Desktop Shortcut Component -->
    <Component Id="DesktopShortcutComponent" Directory="DesktopFolder" Guid="12345678-1234-1234-1234-123456789007">
      
      <Shortcut Id="ApplicationDesktopShortcut"
                Name="!(loc.ProductName)"
                Description="!(loc.ProductDescription)"
                Target="[INSTALLFOLDER]AlBayanConnectLauncher.exe"
                WorkingDirectory="INSTALLFOLDER"
                Icon="ProductIcon" />

      <RegistryValue Root="HKCU" 
                     Key="Software\Al-Bayan\Connect\Installation" 
                     Name="DesktopShortcut" 
                     Value="1" 
                     Type="integer" 
                     KeyPath="yes" />

    </Component>

  </Fragment>

  <!-- Additional Directory References -->
  <Fragment>
    <Directory Id="TARGETDIR" Name="SourceDir">
      <Directory Id="DesktopFolder" Name="Desktop" />
    </Directory>
  </Fragment>

</Wix>
