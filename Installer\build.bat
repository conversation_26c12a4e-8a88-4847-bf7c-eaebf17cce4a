@echo off
REM Al-Bayan Connect - Master Build Script
REM Author: Dr. <PERSON> Esmail
REM Copyright: © 2025 Al-Bayan AI Platform

setlocal enabledelayedexpansion

echo ===============================================
echo Al-Bayan Connect Master Build
echo ===============================================
echo Date: %DATE% %TIME%
echo ===============================================

REM Default configuration
set "BUILD_CONFIG=Release"
set "BUILD_PLATFORM=x86"
set "ENABLE_SIGNING=false"
set "ENABLE_VALIDATION=false"
set "CREATE_PACKAGE=false"
set "CLEAN_BUILD=false"
set "VERBOSE_OUTPUT=false"

REM Parse command line arguments
:parse_args
if "%~1"=="" goto :start_build
if /i "%~1"=="/config" (
    set "BUILD_CONFIG=%~2"
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="/platform" (
    set "BUILD_PLATFORM=%~2"
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="/sign" (
    set "ENABLE_SIGNING=true"
    shift
    goto :parse_args
)
if /i "%~1"=="/validate" (
    set "ENABLE_VALIDATION=true"
    shift
    goto :parse_args
)
if /i "%~1"=="/package" (
    set "CREATE_PACKAGE=true"
    shift
    goto :parse_args
)
if /i "%~1"=="/clean" (
    set "CLEAN_BUILD=true"
    shift
    goto :parse_args
)
if /i "%~1"=="/verbose" (
    set "VERBOSE_OUTPUT=true"
    shift
    goto :parse_args
)
if /i "%~1"=="/?" goto :show_help
if /i "%~1"=="/help" goto :show_help
shift
goto :parse_args

:start_build
echo Build Configuration:
echo   Configuration: %BUILD_CONFIG%
echo   Platform: %BUILD_PLATFORM%
echo   Enable Signing: %ENABLE_SIGNING%
echo   Enable Validation: %ENABLE_VALIDATION%
echo   Create Package: %CREATE_PACKAGE%
echo   Clean Build: %CLEAN_BUILD%
echo   Verbose Output: %VERBOSE_OUTPUT%
echo.

REM Check if PowerShell is available
powershell.exe -Command "Write-Host 'PowerShell available'" >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: PowerShell is required but not available
    goto :error_exit
)

REM Prepare PowerShell command
set "PS_COMMAND=powershell.exe -ExecutionPolicy Bypass -File Scripts\BuildInstaller.ps1"
set "PS_COMMAND=%PS_COMMAND% -Configuration %BUILD_CONFIG%"
set "PS_COMMAND=%PS_COMMAND% -Platform %BUILD_PLATFORM%"

if "%ENABLE_SIGNING%"=="true" (
    set "PS_COMMAND=%PS_COMMAND% -Sign"
)

if "%ENABLE_VALIDATION%"=="true" (
    set "PS_COMMAND=%PS_COMMAND% -Validate"
)

if "%CREATE_PACKAGE%"=="true" (
    set "PS_COMMAND=%PS_COMMAND% -Package"
)

if "%CLEAN_BUILD%"=="true" (
    set "PS_COMMAND=%PS_COMMAND% -Clean"
)

if "%VERBOSE_OUTPUT%"=="true" (
    set "PS_COMMAND=%PS_COMMAND% -Verbose"
)

REM Add certificate parameters if environment variables are set
if not "%CODE_SIGNING_CERT_THUMBPRINT%"=="" (
    set "PS_COMMAND=%PS_COMMAND% -CertificateThumbprint %CODE_SIGNING_CERT_THUMBPRINT%"
)

if not "%CODE_SIGNING_CERT_PATH%"=="" (
    set "PS_COMMAND=%PS_COMMAND% -CertificatePath %CODE_SIGNING_CERT_PATH%"
)

if not "%CODE_SIGNING_CERT_PASSWORD%"=="" (
    set "PS_COMMAND=%PS_COMMAND% -CertificatePassword %CODE_SIGNING_CERT_PASSWORD%"
)

echo Executing build command:
echo %PS_COMMAND%
echo.

REM Execute the PowerShell build script
%PS_COMMAND%
set "BUILD_RESULT=%errorlevel%"

echo.
echo Build completed with exit code: %BUILD_RESULT%

if %BUILD_RESULT% equ 0 (
    echo.
    echo ===============================================
    echo Build Summary
    echo ===============================================
    echo Status: SUCCESS
    echo Configuration: %BUILD_CONFIG%
    echo Platform: %BUILD_PLATFORM%
    echo.
    
    REM Display output file information
    set "OUTPUT_DIR=bin\%BUILD_CONFIG%"
    set "OUTPUT_FILE=%OUTPUT_DIR%\AlBayanConnectInstaller.msi"
    
    if exist "%OUTPUT_FILE%" (
        echo Output File: %OUTPUT_FILE%
        
        REM Get file size
        for %%A in ("%OUTPUT_FILE%") do (
            set "FILE_SIZE=%%~zA"
            set /a "FILE_SIZE_MB=!FILE_SIZE! / 1024 / 1024"
            echo File Size: !FILE_SIZE_MB! MB
        )
        
        echo.
        echo The installer is ready for distribution!
        echo.
        echo Next steps:
        echo - Test the installer on clean systems
        echo - Distribute to beta testers
        echo - Deploy to production
        echo.
    )
    
    echo For support, visit: https://github.com/al-bayan-ai/al-bayan-connect
    echo ===============================================
    goto :success_exit
) else (
    echo.
    echo ===============================================
    echo Build Failed
    echo ===============================================
    echo Status: FAILED
    echo Exit Code: %BUILD_RESULT%
    echo.
    echo Please check the build logs for detailed error information.
    echo Common issues:
    echo - Missing build tools (MSBuild, WiX Toolset)
    echo - Missing source files or dependencies
    echo - Certificate configuration issues
    echo - Insufficient permissions
    echo.
    echo For support, visit: https://github.com/al-bayan-ai/al-bayan-connect
    echo ===============================================
    goto :error_exit
)

:show_help
echo.
echo Al-Bayan Connect Master Build Script
echo.
echo Usage: build.bat [OPTIONS]
echo.
echo Options:
echo   /config ^<config^>     Build configuration (Debug/Release)
echo   /platform ^<platform^> Target platform (x86/x64/AnyCPU)
echo   /sign               Enable code signing
echo   /validate           Enable validation
echo   /package            Create distribution package
echo   /clean              Clean before build
echo   /verbose            Enable verbose output
echo   /help or /?         Show this help message
echo.
echo Examples:
echo   build.bat
echo   build.bat /config Release /sign /package
echo   build.bat /clean /verbose
echo.
echo Environment Variables:
echo   CODE_SIGNING_CERT_THUMBPRINT  Certificate thumbprint
echo   CODE_SIGNING_CERT_PATH        Certificate file path
echo   CODE_SIGNING_CERT_PASSWORD    Certificate password
echo   BUILD_VERSION                 Product version
echo   BUILD_NUMBER                  Build number
echo.
exit /b 0

:error_exit
exit /b %BUILD_RESULT%

:success_exit
exit /b 0
