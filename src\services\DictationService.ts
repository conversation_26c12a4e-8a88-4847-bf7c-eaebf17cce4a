// Al-Bayan Connect - Core Dictation Service
// Author: Dr. <PERSON>
// Copyright: © 2025 Al-Bayan AI Platform

import {
  SpeechRecognitionResult,
  Language,
  DictationState,
  DictationEvent,
  DictationEventType,
  UserSettings,
  ErrorType,
  EventHandler
} from '@types/index';
import { LanguageDetectionService } from './LanguageDetectionService';
import { VoiceCommandProcessor } from './VoiceCommandProcessor';
import { AnalyticsService } from './AnalyticsService';

export class DictationService {
  private recognition: SpeechRecognition | null = null;
  private isListening = false;
  private currentLanguage: Language = Language.AUTO_DETECT;
  private state: DictationState = DictationState.IDLE;
  private settings: UserSettings;
  private eventHandlers: Map<DictationEventType, EventHandler[]> = new Map();
  
  private languageDetection: LanguageDetectionService;
  private commandProcessor: VoiceCommandProcessor;
  private analytics: AnalyticsService;
  
  private sessionStartTime: Date | null = null;
  private sessionResults: SpeechRecognitionResult[] = [];

  constructor(settings: UserSettings) {
    this.settings = settings;
    this.languageDetection = new LanguageDetectionService();
    this.commandProcessor = new VoiceCommandProcessor();
    this.analytics = new AnalyticsService();
    
    this.initializeSpeechRecognition();
  }

  private initializeSpeechRecognition(): void {
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      this.emitEvent(DictationEventType.ERROR, {
        type: ErrorType.SPEECH_RECOGNITION,
        message: 'Speech recognition not supported in this browser'
      });
      return;
    }

    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    this.recognition = new SpeechRecognition();
    
    this.setupRecognitionConfig();
    this.setupRecognitionEvents();
  }

  private setupRecognitionConfig(): void {
    if (!this.recognition) return;

    this.recognition.continuous = true;
    this.recognition.interimResults = true;
    this.recognition.maxAlternatives = 3;
    
    // Set language based on settings
    this.updateLanguage(this.settings.defaultLanguage);
  }

  private setupRecognitionEvents(): void {
    if (!this.recognition) return;

    this.recognition.onstart = () => {
      this.isListening = true;
      this.state = DictationState.LISTENING;
      this.sessionStartTime = new Date();
      this.sessionResults = [];
      
      this.emitEvent(DictationEventType.STARTED, {
        language: this.currentLanguage,
        timestamp: new Date()
      });
    };

    this.recognition.onresult = (event: SpeechRecognitionEvent) => {
      this.handleRecognitionResult(event);
    };

    this.recognition.onerror = (event: SpeechRecognitionErrorEvent) => {
      this.handleRecognitionError(event);
    };

    this.recognition.onend = () => {
      this.isListening = false;
      this.state = DictationState.IDLE;
      
      if (this.sessionStartTime) {
        this.analytics.recordSession({
          startTime: this.sessionStartTime,
          endTime: new Date(),
          results: this.sessionResults,
          language: this.currentLanguage
        });
      }
      
      this.emitEvent(DictationEventType.STOPPED, {
        timestamp: new Date(),
        sessionDuration: this.sessionStartTime ? 
          Date.now() - this.sessionStartTime.getTime() : 0
      });
    };
  }

  private async handleRecognitionResult(event: SpeechRecognitionEvent): Promise<void> {
    const lastResult = event.results[event.results.length - 1];
    const transcript = lastResult[0].transcript;
    const confidence = lastResult[0].confidence;
    const isFinal = lastResult.isFinal;

    // Detect language if auto-detection is enabled
    let detectedLanguage = this.currentLanguage;
    if (this.settings.autoLanguageDetection && transcript.trim().length > 3) {
      const detection = await this.languageDetection.detectLanguage(transcript);
      if (detection.confidence > 0.8) {
        detectedLanguage = detection.language;
        
        // Switch language if different from current
        if (detectedLanguage !== this.currentLanguage) {
          this.updateLanguage(detectedLanguage);
          this.emitEvent(DictationEventType.LANGUAGE_DETECTED, {
            language: detectedLanguage,
            confidence: detection.confidence,
            transcript
          });
        }
      }
    }

    const result: SpeechRecognitionResult = {
      transcript,
      confidence,
      isFinal,
      language: detectedLanguage,
      timestamp: new Date()
    };

    // Store result for analytics
    if (isFinal) {
      this.sessionResults.push(result);
    }

    // Check for voice commands
    if (isFinal && confidence > this.settings.confidenceThreshold) {
      const command = await this.commandProcessor.processText(transcript, detectedLanguage);
      if (command) {
        this.emitEvent(DictationEventType.COMMAND_EXECUTED, {
          command,
          transcript,
          timestamp: new Date()
        });
        return; // Don't emit regular result if it was a command
      }
    }

    this.emitEvent(DictationEventType.RESULT, result);
  }

  private handleRecognitionError(event: SpeechRecognitionErrorEvent): void {
    let errorType = ErrorType.SPEECH_RECOGNITION;
    let message = event.error;

    switch (event.error) {
      case 'network':
        errorType = ErrorType.NETWORK;
        message = 'Network error occurred during speech recognition';
        break;
      case 'not-allowed':
        errorType = ErrorType.PERMISSION;
        message = 'Microphone permission denied';
        break;
      case 'no-speech':
        message = 'No speech detected';
        break;
      case 'audio-capture':
        message = 'Audio capture failed';
        break;
      default:
        message = `Speech recognition error: ${event.error}`;
    }

    this.state = DictationState.ERROR;
    
    this.emitEvent(DictationEventType.ERROR, {
      type: errorType,
      message,
      originalError: event.error
    });
  }

  public startDictation(): void {
    if (!this.recognition) {
      this.emitEvent(DictationEventType.ERROR, {
        type: ErrorType.SPEECH_RECOGNITION,
        message: 'Speech recognition not available'
      });
      return;
    }

    if (this.isListening) {
      return;
    }

    try {
      this.recognition.start();
    } catch (error) {
      this.emitEvent(DictationEventType.ERROR, {
        type: ErrorType.SPEECH_RECOGNITION,
        message: `Failed to start dictation: ${error}`
      });
    }
  }

  public stopDictation(): void {
    if (!this.recognition || !this.isListening) {
      return;
    }

    try {
      this.recognition.stop();
    } catch (error) {
      this.emitEvent(DictationEventType.ERROR, {
        type: ErrorType.SPEECH_RECOGNITION,
        message: `Failed to stop dictation: ${error}`
      });
    }
  }

  public toggleDictation(): void {
    if (this.isListening) {
      this.stopDictation();
    } else {
      this.startDictation();
    }
  }

  public updateLanguage(language: Language): void {
    this.currentLanguage = language;
    
    if (this.recognition && language !== Language.AUTO_DETECT) {
      const langCode = this.getLanguageCode(language);
      this.recognition.lang = langCode;
    }
  }

  private getLanguageCode(language: Language): string {
    switch (language) {
      case Language.ARABIC:
        return 'ar-SA'; // Saudi Arabic as default
      case Language.ENGLISH:
        return 'en-US'; // US English as default
      default:
        return 'en-US';
    }
  }

  public updateSettings(settings: UserSettings): void {
    this.settings = settings;
    this.updateLanguage(settings.defaultLanguage);
    
    // Update related services
    this.languageDetection.updateSettings(settings);
    this.commandProcessor.updateSettings(settings);
    this.analytics.updateSettings(settings);
  }

  public getCurrentState(): DictationState {
    return this.state;
  }

  public isCurrentlyListening(): boolean {
    return this.isListening;
  }

  public getCurrentLanguage(): Language {
    return this.currentLanguage;
  }

  // Event handling
  public addEventListener(type: DictationEventType, handler: EventHandler): void {
    if (!this.eventHandlers.has(type)) {
      this.eventHandlers.set(type, []);
    }
    this.eventHandlers.get(type)!.push(handler);
  }

  public removeEventListener(type: DictationEventType, handler: EventHandler): void {
    const handlers = this.eventHandlers.get(type);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  private emitEvent(type: DictationEventType, data: any): void {
    const handlers = this.eventHandlers.get(type);
    if (handlers) {
      const event: DictationEvent = {
        type,
        data,
        timestamp: new Date()
      };
      
      handlers.forEach(handler => {
        try {
          handler(event);
        } catch (error) {
          console.error(`Error in event handler for ${type}:`, error);
        }
      });
    }
  }

  public dispose(): void {
    this.stopDictation();
    this.eventHandlers.clear();
    
    if (this.recognition) {
      this.recognition.onstart = null;
      this.recognition.onresult = null;
      this.recognition.onerror = null;
      this.recognition.onend = null;
    }
  }
}

// Extend the Window interface for TypeScript
declare global {
  interface Window {
    SpeechRecognition: typeof SpeechRecognition;
    webkitSpeechRecognition: typeof SpeechRecognition;
  }
}
