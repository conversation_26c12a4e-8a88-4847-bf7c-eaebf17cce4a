# Al-Bayan Connect - Certificate Management System
# Author: Dr. <PERSON>smail
# Copyright: © 2025 Al-Bayan AI Platform

param(
    [string]$Action = "Info",
    [string]$CertificateProvider = "",
    [string]$CertificatePath = "",
    [string]$OutputPath = "",
    [switch]$CreateCSR,
    [switch]$InstallCertificate,
    [switch]$ValidateCertificate,
    [switch]$ExportCertificate,
    [switch]$SetupEnvironment,
    [switch]$Verbose,
    [switch]$Help
)

# Configuration
$script:ScriptVersion = "1.0.0"
$script:LogFile = "$env:TEMP\AlBayanConnect_CertificateManager.log"

# Certificate providers and their requirements
$script:CertificateProviders = @{
    "DigiCert" = @{
        Name = "DigiCert"
        Type = "EV Code Signing"
        ValidationLevel = "Extended Validation"
        Price = "$474/year"
        DeliveryTime = "1-5 business days"
        Requirements = @(
            "Business registration documents",
            "D-U-N-S number",
            "Phone verification",
            "Physical address verification",
            "Hardware security module (HSM) or USB token"
        )
        Website = "https://www.digicert.com/code-signing/"
    }
    "Sectigo" = @{
        Name = "Sectigo (formerly Comodo)"
        Type = "EV Code Signing"
        ValidationLevel = "Extended Validation"
        Price = "$474/year"
        DeliveryTime = "1-7 business days"
        Requirements = @(
            "Business registration documents",
            "D-U-N-S number",
            "Phone verification",
            "Physical address verification",
            "Hardware security module (HSM) or USB token"
        )
        Website = "https://sectigo.com/ssl-certificates-tls/code-signing"
    }
    "GlobalSign" = @{
        Name = "GlobalSign"
        Type = "EV Code Signing"
        ValidationLevel = "Extended Validation"
        Price = "$599/year"
        DeliveryTime = "1-5 business days"
        Requirements = @(
            "Business registration documents",
            "D-U-N-S number",
            "Phone verification",
            "Physical address verification",
            "Hardware security module (HSM) or USB token"
        )
        Website = "https://www.globalsign.com/en/code-signing-certificate/"
    }
    "Entrust" = @{
        Name = "Entrust"
        Type = "EV Code Signing"
        ValidationLevel = "Extended Validation"
        Price = "$549/year"
        DeliveryTime = "1-5 business days"
        Requirements = @(
            "Business registration documents",
            "D-U-N-S number",
            "Phone verification",
            "Physical address verification",
            "Hardware security module (HSM) or USB token"
        )
        Website = "https://www.entrust.com/digital-security/certificate-solutions/products/digital-certificates/code-signing-certificates"
    }
}

function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    if ($Verbose -or $Level -eq "ERROR" -or $Level -eq "WARN") {
        $color = switch ($Level) {
            "ERROR" { "Red" }
            "WARN" { "Yellow" }
            "SUCCESS" { "Green" }
            "CERT" { "Cyan" }
            default { "White" }
        }
        Write-Host $logEntry -ForegroundColor $color
    }
    
    Add-Content -Path $script:LogFile -Value $logEntry -ErrorAction SilentlyContinue
}

function Show-Help {
    Write-Host @"

Al-Bayan Connect Certificate Management System v$script:ScriptVersion

DESCRIPTION:
    Comprehensive certificate management for code signing, including CSR generation,
    certificate installation, validation, and environment setup.

SYNTAX:
    .\CertificateManager.ps1 [OPTIONS]

PARAMETERS:
    -Action <action>              Action to perform (Info/CSR/Install/Validate/Export/Setup)
    -CertificateProvider <name>   Certificate provider (DigiCert/Sectigo/GlobalSign/Entrust)
    -CertificatePath <path>       Path to certificate file
    -OutputPath <path>            Output directory for generated files
    -CreateCSR                    Generate Certificate Signing Request
    -InstallCertificate          Install certificate to store
    -ValidateCertificate         Validate existing certificate
    -ExportCertificate           Export certificate for distribution
    -SetupEnvironment            Setup certificate environment
    -Verbose                     Enable verbose output
    -Help                        Show this help message

EXAMPLES:
    # Show certificate provider information
    .\CertificateManager.ps1 -Action Info

    # Generate CSR for DigiCert
    .\CertificateManager.ps1 -CreateCSR -CertificateProvider DigiCert -OutputPath "C:\Certificates"

    # Install certificate
    .\CertificateManager.ps1 -InstallCertificate -CertificatePath "certificate.pfx"

    # Validate certificate
    .\CertificateManager.ps1 -ValidateCertificate -CertificatePath "certificate.pfx"

    # Setup complete environment
    .\CertificateManager.ps1 -SetupEnvironment -CertificateProvider DigiCert

CERTIFICATE PROVIDERS:
    DigiCert    - Industry leader, fastest validation
    Sectigo     - Cost-effective, reliable
    GlobalSign  - International presence
    Entrust     - Enterprise-focused

"@
}

function Show-CertificateProviderInfo {
    Write-Host @"

=== Al-Bayan Connect Certificate Provider Information ===

For Al-Bayan Connect, we recommend Extended Validation (EV) Code Signing certificates
to ensure maximum trust and compatibility with Windows SmartScreen and antivirus software.

"@ -ForegroundColor Cyan

    foreach ($providerName in $script:CertificateProviders.Keys) {
        $provider = $script:CertificateProviders[$providerName]
        
        Write-Host "`n--- $($provider.Name) ---" -ForegroundColor Yellow
        Write-Host "Type: $($provider.Type)"
        Write-Host "Validation Level: $($provider.ValidationLevel)"
        Write-Host "Price: $($provider.Price)"
        Write-Host "Delivery Time: $($provider.DeliveryTime)"
        Write-Host "Website: $($provider.Website)"
        Write-Host "Requirements:"
        foreach ($req in $provider.Requirements) {
            Write-Host "  • $req" -ForegroundColor Gray
        }
    }
    
    Write-Host @"

=== Recommended Provider for Al-Bayan Connect ===

For Al-Bayan Connect, we recommend DigiCert EV Code Signing Certificate because:

✓ Fastest validation process (1-5 business days)
✓ Highest industry reputation and trust
✓ Excellent compatibility with Microsoft ecosystem
✓ Superior customer support
✓ Immediate SmartScreen reputation
✓ Best for Microsoft AppSource submission

=== Next Steps ===

1. Choose a certificate provider
2. Gather required business documents
3. Generate Certificate Signing Request (CSR)
4. Submit application to chosen provider
5. Complete validation process
6. Install certificate and configure environment

Use this script to generate CSR and manage certificates throughout the process.

"@ -ForegroundColor Green
}

function New-CertificateSigningRequest {
    param(
        [string]$Provider,
        [string]$OutputDir
    )
    
    Write-Log "Generating Certificate Signing Request for $Provider..." "CERT"
    
    if (-not $OutputDir) {
        $OutputDir = "$env:USERPROFILE\Documents\AlBayanConnect_Certificates"
    }
    
    # Create output directory
    if (-not (Test-Path $OutputDir)) {
        New-Item -Path $OutputDir -ItemType Directory -Force | Out-Null
    }
    
    # Certificate subject information for Al-Bayan Connect
    $subject = @{
        CommonName = "Al-Bayan AI Platform"
        Organization = "Al-Bayan AI Platform"
        OrganizationalUnit = "Software Development"
        Country = "SA"  # Saudi Arabia
        State = "Riyadh"
        City = "Riyadh"
        Email = "<EMAIL>"
    }
    
    # Generate private key
    $keyPath = Join-Path $OutputDir "al-bayan-connect-private.key"
    $csrPath = Join-Path $OutputDir "al-bayan-connect.csr"
    $infPath = Join-Path $OutputDir "al-bayan-connect.inf"
    
    # Create certificate request configuration file
    $infContent = @"
[Version]
Signature = "`$Windows NT`$"

[NewRequest]
Subject = "CN=$($subject.CommonName), O=$($subject.Organization), OU=$($subject.OrganizationalUnit), L=$($subject.City), S=$($subject.State), C=$($subject.Country)"
KeySpec = 1
KeyLength = 2048
Exportable = TRUE
MachineKeySet = FALSE
SMIME = FALSE
PrivateKeyArchive = FALSE
UserProtected = FALSE
UseExistingKeySet = FALSE
ProviderName = "Microsoft RSA SChannel Cryptographic Provider"
ProviderType = 12
RequestType = PKCS10
KeyUsage = 0xa0
HashAlgorithm = SHA256

[EnhancedKeyUsageExtension]
OID = 1.3.6.1.5.5.7.3.3 ; Code Signing

[RequestAttributes]
CertificateTemplate = "CodeSigning"

[Extensions]
2.5.29.17 = "{text}"
_continue_ = "email=$($subject.Email)&"
"@
    
    Set-Content -Path $infPath -Value $infContent -Encoding ASCII
    
    try {
        # Generate CSR using certreq
        Write-Log "Generating CSR using certreq..." "CERT"
        $result = Start-Process -FilePath "certreq.exe" -ArgumentList @("-new", "`"$infPath`"", "`"$csrPath`"") -Wait -PassThru -NoNewWindow
        
        if ($result.ExitCode -eq 0 -and (Test-Path $csrPath)) {
            Write-Log "CSR generated successfully: $csrPath" "SUCCESS"
            
            # Create submission package
            $packagePath = Join-Path $OutputDir "AlBayanConnect_CSR_Package"
            if (-not (Test-Path $packagePath)) {
                New-Item -Path $packagePath -ItemType Directory -Force | Out-Null
            }
            
            # Copy CSR to package
            Copy-Item -Path $csrPath -Destination $packagePath
            
            # Create submission instructions
            $instructionsPath = Join-Path $packagePath "SUBMISSION_INSTRUCTIONS.txt"
            $instructions = @"
Al-Bayan Connect - Certificate Signing Request Submission Package
================================================================

Generated: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
Provider: $Provider
CSR File: al-bayan-connect.csr

SUBMISSION INSTRUCTIONS:
========================

1. Submit the CSR file (al-bayan-connect.csr) to $Provider
2. Provide the following organization details:

   Organization: $($subject.Organization)
   Common Name: $($subject.CommonName)
   Country: $($subject.Country)
   State/Province: $($subject.State)
   City: $($subject.City)
   Email: $($subject.Email)

3. Required Documents for EV Validation:
   - Business registration certificate
   - D-U-N-S number verification
   - Phone number verification
   - Physical address verification
   - Authorized representative identification

4. Certificate Type: Extended Validation (EV) Code Signing Certificate

5. Key Usage: Code Signing

6. Validity Period: 1-3 years (recommended: 1 year for first certificate)

IMPORTANT NOTES:
================

- Keep the private key secure and backed up
- The private key file is: al-bayan-connect-private.key
- Do NOT share the private key with anyone
- After receiving the certificate, use CertificateManager.ps1 to install it

NEXT STEPS:
===========

1. Submit CSR to $Provider via their website
2. Complete business validation process
3. Receive certificate file (.p7b or .cer format)
4. Install certificate using: .\CertificateManager.ps1 -InstallCertificate
5. Configure build environment for code signing

For support: <EMAIL>
"@
            
            Set-Content -Path $instructionsPath -Value $instructions -Encoding UTF8
            
            # Create provider-specific information
            if ($script:CertificateProviders.ContainsKey($Provider)) {
                $providerInfo = $script:CertificateProviders[$Provider]
                $providerInfoPath = Join-Path $packagePath "PROVIDER_INFO_$Provider.txt"
                
                $providerDetails = @"
$($providerInfo.Name) Certificate Information
============================================

Website: $($providerInfo.Website)
Certificate Type: $($providerInfo.Type)
Validation Level: $($providerInfo.ValidationLevel)
Estimated Price: $($providerInfo.Price)
Delivery Time: $($providerInfo.DeliveryTime)

Required Documents:
"@
                foreach ($req in $providerInfo.Requirements) {
                    $providerDetails += "`n- $req"
                }
                
                Set-Content -Path $providerInfoPath -Value $providerDetails -Encoding UTF8
            }
            
            Write-Log "CSR submission package created: $packagePath" "SUCCESS"
            Write-Log "Please review the submission instructions and submit to $Provider" "INFO"
            
            return $packagePath
        } else {
            throw "CSR generation failed with exit code: $($result.ExitCode)"
        }
    }
    catch {
        Write-Log "CSR generation failed: $($_.Exception.Message)" "ERROR"
        return $null
    }
    finally {
        # Clean up temporary files
        if (Test-Path $infPath) {
            Remove-Item -Path $infPath -Force -ErrorAction SilentlyContinue
        }
    }
}

function Install-CodeSigningCertificate {
    param([string]$CertPath)
    
    Write-Log "Installing code signing certificate..." "CERT"
    
    if (-not (Test-Path $CertPath)) {
        throw "Certificate file not found: $CertPath"
    }
    
    try {
        # Get certificate information
        $cert = New-Object System.Security.Cryptography.X509Certificates.X509Certificate2($CertPath)
        
        Write-Log "Certificate Subject: $($cert.Subject)" "INFO"
        Write-Log "Certificate Issuer: $($cert.Issuer)" "INFO"
        Write-Log "Certificate Valid From: $($cert.NotBefore)" "INFO"
        Write-Log "Certificate Valid Until: $($cert.NotAfter)" "INFO"
        Write-Log "Certificate Thumbprint: $($cert.Thumbprint)" "INFO"
        
        # Check if certificate is valid for code signing
        $codeSigningOid = "1.3.6.1.5.5.7.3.3"
        $hasCodeSigning = $cert.Extensions | Where-Object { $_.Oid.Value -eq "2.5.29.37" } | ForEach-Object {
            $_.Format($false) -match $codeSigningOid
        }
        
        if (-not $hasCodeSigning) {
            Write-Log "WARNING: Certificate may not be valid for code signing" "WARN"
        }
        
        # Install certificate to Personal store
        $store = New-Object System.Security.Cryptography.X509Certificates.X509Store("My", "CurrentUser")
        $store.Open("ReadWrite")
        $store.Add($cert)
        $store.Close()
        
        Write-Log "Certificate installed successfully to Personal store" "SUCCESS"
        
        # Create environment configuration
        $configPath = "$env:USERPROFILE\Documents\AlBayanConnect_Certificate_Config.json"
        $config = @{
            CertificateThumbprint = $cert.Thumbprint
            CertificateSubject = $cert.Subject
            CertificateIssuer = $cert.Issuer
            ValidFrom = $cert.NotBefore.ToString("yyyy-MM-dd")
            ValidUntil = $cert.NotAfter.ToString("yyyy-MM-dd")
            InstallDate = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
            StoreName = "My"
            StoreLocation = "CurrentUser"
        }
        
        $config | ConvertTo-Json -Depth 3 | Set-Content -Path $configPath -Encoding UTF8
        Write-Log "Certificate configuration saved: $configPath" "SUCCESS"
        
        # Set environment variables
        [Environment]::SetEnvironmentVariable("CODE_SIGNING_CERT_THUMBPRINT", $cert.Thumbprint, "User")
        Write-Log "Environment variable set: CODE_SIGNING_CERT_THUMBPRINT" "SUCCESS"
        
        return $cert.Thumbprint
    }
    catch {
        Write-Log "Certificate installation failed: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

function Test-CertificateValidation {
    param([string]$CertPath)
    
    Write-Log "Validating certificate..." "CERT"
    
    try {
        if ($CertPath) {
            # Validate certificate file
            $cert = New-Object System.Security.Cryptography.X509Certificates.X509Certificate2($CertPath)
        } else {
            # Validate installed certificate
            $thumbprint = [Environment]::GetEnvironmentVariable("CODE_SIGNING_CERT_THUMBPRINT", "User")
            if (-not $thumbprint) {
                throw "No certificate thumbprint found in environment variables"
            }
            
            $store = New-Object System.Security.Cryptography.X509Certificates.X509Store("My", "CurrentUser")
            $store.Open("ReadOnly")
            $cert = $store.Certificates | Where-Object { $_.Thumbprint -eq $thumbprint }
            $store.Close()
            
            if (-not $cert) {
                throw "Certificate not found in store with thumbprint: $thumbprint"
            }
        }
        
        # Validation checks
        $validationResults = @()
        
        # Check expiration
        $now = Get-Date
        if ($cert.NotAfter -gt $now) {
            $validationResults += "Expiration: VALID (expires $($cert.NotAfter.ToString('yyyy-MM-dd')))"
            Write-Log "Certificate expiration: VALID" "SUCCESS"
        } else {
            $validationResults += "Expiration: EXPIRED"
            Write-Log "Certificate expiration: EXPIRED" "ERROR"
        }
        
        # Check if not yet valid
        if ($cert.NotBefore -le $now) {
            $validationResults += "Validity Start: VALID"
            Write-Log "Certificate validity start: VALID" "SUCCESS"
        } else {
            $validationResults += "Validity Start: NOT_YET_VALID"
            Write-Log "Certificate validity start: NOT YET VALID" "ERROR"
        }
        
        # Check code signing capability
        $extensions = $cert.Extensions | Where-Object { $_.Oid.Value -eq "2.5.29.37" }
        if ($extensions -and $extensions.Format($false) -match "1.3.6.1.5.5.7.3.3") {
            $validationResults += "Code Signing: VALID"
            Write-Log "Code signing capability: VALID" "SUCCESS"
        } else {
            $validationResults += "Code Signing: INVALID"
            Write-Log "Code signing capability: INVALID" "ERROR"
        }
        
        # Check private key
        if ($cert.HasPrivateKey) {
            $validationResults += "Private Key: AVAILABLE"
            Write-Log "Private key: AVAILABLE" "SUCCESS"
        } else {
            $validationResults += "Private Key: MISSING"
            Write-Log "Private key: MISSING" "ERROR"
        }
        
        # Check certificate chain
        $chain = New-Object System.Security.Cryptography.X509Certificates.X509Chain
        $chainValid = $chain.Build($cert)
        if ($chainValid) {
            $validationResults += "Certificate Chain: VALID"
            Write-Log "Certificate chain: VALID" "SUCCESS"
        } else {
            $validationResults += "Certificate Chain: INVALID"
            Write-Log "Certificate chain: INVALID" "WARN"
            
            foreach ($status in $chain.ChainStatus) {
                Write-Log "Chain Status: $($status.Status) - $($status.StatusInformation)" "WARN"
            }
        }
        
        Write-Log "Certificate validation completed" "SUCCESS"
        return $validationResults
    }
    catch {
        Write-Log "Certificate validation failed: $($_.Exception.Message)" "ERROR"
        return @("Validation: FAILED - $($_.Exception.Message)")
    }
}

function Set-CertificateEnvironment {
    param([string]$Provider)
    
    Write-Log "Setting up certificate environment..." "CERT"
    
    try {
        # Create certificates directory structure
        $certDir = "$env:USERPROFILE\Documents\AlBayanConnect_Certificates"
        $dirs = @(
            $certDir,
            "$certDir\CSR",
            "$certDir\Certificates",
            "$certDir\Backup",
            "$certDir\Scripts"
        )
        
        foreach ($dir in $dirs) {
            if (-not (Test-Path $dir)) {
                New-Item -Path $dir -ItemType Directory -Force | Out-Null
                Write-Log "Created directory: $dir" "SUCCESS"
            }
        }
        
        # Create certificate management scripts
        $scriptPath = "$certDir\Scripts\SignInstaller.bat"
        $signScript = @"
@echo off
REM Al-Bayan Connect - Quick Signing Script
REM Generated by Certificate Manager

echo Signing Al-Bayan Connect Installer...

if "%CODE_SIGNING_CERT_THUMBPRINT%"=="" (
    echo ERROR: CODE_SIGNING_CERT_THUMBPRINT environment variable not set
    echo Please run CertificateManager.ps1 -InstallCertificate first
    exit /b 1
)

if "%1"=="" (
    echo Usage: SignInstaller.bat ^<installer-path^>
    echo Example: SignInstaller.bat AlBayanConnectInstaller.msi
    exit /b 1
)

powershell.exe -ExecutionPolicy Bypass -Command "& { Get-AuthenticodeSignature '%1' | Format-Table -AutoSize }"

signtool.exe sign /sha1 %CODE_SIGNING_CERT_THUMBPRINT% /fd SHA256 /tr http://timestamp.digicert.com /td SHA256 "%1"

if %errorlevel% equ 0 (
    echo Signing completed successfully!
    powershell.exe -ExecutionPolicy Bypass -Command "& { Get-AuthenticodeSignature '%1' | Format-Table -AutoSize }"
) else (
    echo Signing failed with error code %errorlevel%
)

pause
"@
        
        Set-Content -Path $scriptPath -Value $signScript -Encoding ASCII
        Write-Log "Created signing script: $scriptPath" "SUCCESS"
        
        # Create PowerShell signing script
        $psScriptPath = "$certDir\Scripts\SignInstaller.ps1"
        $psSignScript = @"
# Al-Bayan Connect - PowerShell Signing Script
# Generated by Certificate Manager

param(
    [Parameter(Mandatory=`$true)]
    [string]`$FilePath,
    [string]`$TimestampServer = "http://timestamp.digicert.com"
)

`$thumbprint = [Environment]::GetEnvironmentVariable("CODE_SIGNING_CERT_THUMBPRINT", "User")
if (-not `$thumbprint) {
    Write-Error "CODE_SIGNING_CERT_THUMBPRINT environment variable not set"
    exit 1
}

if (-not (Test-Path `$FilePath)) {
    Write-Error "File not found: `$FilePath"
    exit 1
}

Write-Host "Signing file: `$FilePath" -ForegroundColor Green
Write-Host "Certificate: `$thumbprint" -ForegroundColor Green
Write-Host "Timestamp Server: `$TimestampServer" -ForegroundColor Green

try {
    `$cert = Get-ChildItem -Path "Cert:\CurrentUser\My" | Where-Object { `$_.Thumbprint -eq `$thumbprint }
    if (-not `$cert) {
        throw "Certificate not found in store"
    }
    
    Set-AuthenticodeSignature -FilePath `$FilePath -Certificate `$cert -TimestampServer `$TimestampServer -HashAlgorithm SHA256
    
    `$signature = Get-AuthenticodeSignature -FilePath `$FilePath
    if (`$signature.Status -eq "Valid") {
        Write-Host "File signed successfully!" -ForegroundColor Green
        `$signature | Format-Table -AutoSize
    } else {
        Write-Error "Signing failed: `$(`$signature.Status)"
    }
}
catch {
    Write-Error "Signing error: `$(`$_.Exception.Message)"
    exit 1
}
"@
        
        Set-Content -Path $psScriptPath -Value $psSignScript -Encoding UTF8
        Write-Log "Created PowerShell signing script: $psScriptPath" "SUCCESS"
        
        # Create environment setup instructions
        $instructionsPath = "$certDir\ENVIRONMENT_SETUP.md"
        $instructions = @"
# Al-Bayan Connect Certificate Environment Setup

## Environment Created
- Certificate directory: $certDir
- Signing scripts: $certDir\Scripts\
- Backup location: $certDir\Backup\

## Next Steps

### 1. Obtain Certificate
If you haven't already:
1. Generate CSR: ``.\CertificateManager.ps1 -CreateCSR -CertificateProvider $Provider``
2. Submit CSR to certificate authority
3. Complete validation process
4. Download certificate

### 2. Install Certificate
``.\CertificateManager.ps1 -InstallCertificate -CertificatePath "path\to\certificate.pfx"``

### 3. Validate Installation
``.\CertificateManager.ps1 -ValidateCertificate``

### 4. Sign Files
Using batch script:
``$certDir\Scripts\SignInstaller.bat "AlBayanConnectInstaller.msi"``

Using PowerShell:
``$certDir\Scripts\SignInstaller.ps1 -FilePath "AlBayanConnectInstaller.msi"``

### 5. Integrate with Build Process
Add to your build script:
``````
set CODE_SIGNING_CERT_THUMBPRINT=%CODE_SIGNING_CERT_THUMBPRINT%
call "$certDir\Scripts\SignInstaller.bat" "AlBayanConnectInstaller.msi"
``````

## Environment Variables
- ``CODE_SIGNING_CERT_THUMBPRINT``: Certificate thumbprint for signing

## Support
For certificate issues: <EMAIL>
"@
        
        Set-Content -Path $instructionsPath -Value $instructions -Encoding UTF8
        Write-Log "Created setup instructions: $instructionsPath" "SUCCESS"
        
        Write-Log "Certificate environment setup completed" "SUCCESS"
        Write-Log "Next step: Generate CSR or install certificate" "INFO"
        
        return $certDir
    }
    catch {
        Write-Log "Environment setup failed: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

function Main {
    Write-Log "=== Al-Bayan Connect Certificate Manager Started ===" "INFO"
    Write-Log "Manager Version: $script:ScriptVersion" "INFO"
    
    if ($Help) {
        Show-Help
        return 0
    }
    
    try {
        switch ($Action.ToLower()) {
            "info" {
                Show-CertificateProviderInfo
            }
            "csr" {
                if (-not $CertificateProvider) {
                    Write-Log "Certificate provider required for CSR generation" "ERROR"
                    return 1
                }
                $result = New-CertificateSigningRequest -Provider $CertificateProvider -OutputDir $OutputPath
                if ($result) {
                    Write-Log "CSR package created: $result" "SUCCESS"
                    Start-Process $result
                }
            }
            "install" {
                if (-not $CertificatePath) {
                    Write-Log "Certificate path required for installation" "ERROR"
                    return 1
                }
                $thumbprint = Install-CodeSigningCertificate -CertPath $CertificatePath
                if ($thumbprint) {
                    Write-Log "Certificate installed with thumbprint: $thumbprint" "SUCCESS"
                }
            }
            "validate" {
                $results = Test-CertificateValidation -CertPath $CertificatePath
                Write-Host "`nValidation Results:" -ForegroundColor Cyan
                foreach ($result in $results) {
                    Write-Host "  $result" -ForegroundColor White
                }
            }
            "setup" {
                $envPath = Set-CertificateEnvironment -Provider $CertificateProvider
                if ($envPath) {
                    Write-Log "Environment setup completed: $envPath" "SUCCESS"
                    Start-Process $envPath
                }
            }
            default {
                # Handle individual switches
                if ($CreateCSR) {
                    if (-not $CertificateProvider) {
                        Write-Log "Certificate provider required for CSR generation" "ERROR"
                        return 1
                    }
                    $result = New-CertificateSigningRequest -Provider $CertificateProvider -OutputDir $OutputPath
                }
                
                if ($InstallCertificate) {
                    if (-not $CertificatePath) {
                        Write-Log "Certificate path required for installation" "ERROR"
                        return 1
                    }
                    Install-CodeSigningCertificate -CertPath $CertificatePath
                }
                
                if ($ValidateCertificate) {
                    Test-CertificateValidation -CertPath $CertificatePath
                }
                
                if ($SetupEnvironment) {
                    Set-CertificateEnvironment -Provider $CertificateProvider
                }
                
                if (-not ($CreateCSR -or $InstallCertificate -or $ValidateCertificate -or $SetupEnvironment)) {
                    Show-CertificateProviderInfo
                }
        }
        
        Write-Log "Certificate management completed successfully" "SUCCESS"
        return 0
    }
    catch {
        Write-Log "Certificate management failed: $($_.Exception.Message)" "ERROR"
        return 1
    }
}

# Execute main function
$exitCode = Main
exit $exitCode
