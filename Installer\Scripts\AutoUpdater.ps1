# Al-Bayan Connect - Auto-Updater Script
# Author: Dr. <PERSON>
# Copyright: © 2025 Al-Bayan AI Platform

param(
    [string]$UpdateServer = "https://updates.al-bayan.ai",
    [string]$Channel = "stable",
    [string]$CurrentVersion = "",
    [switch]$CheckOnly,
    [switch]$Force,
    [switch]$Silent,
    [switch]$Verbose,
    [switch]$Help
)

# Configuration
$script:ScriptVersion = "1.0.0"
$script:ProductName = "Al-Bayan Connect"
$script:UpdateManifestUrl = "$UpdateServer/api/v1/updates/manifest"
$script:LogFile = "$env:TEMP\AlBayanConnect_AutoUpdater.log"
$script:DownloadPath = "$env:TEMP\AlBayanConnect_Updates"
$script:BackupPath = "$env:LOCALAPPDATA\Al-Bayan Connect\Backup"

function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    if ($Verbose -or $Level -eq "ERROR" -or $Level -eq "WARN" -or -not $Silent) {
        $color = switch ($Level) {
            "ERROR" { "Red" }
            "WARN" { "Yellow" }
            "SUCCESS" { "Green" }
            default { "White" }
        }
        Write-Host $logEntry -ForegroundColor $color
    }
    
    Add-Content -Path $script:LogFile -Value $logEntry -ErrorAction SilentlyContinue
}

function Show-Help {
    Write-Host @"

Al-Bayan Connect Auto-Updater v$script:ScriptVersion

DESCRIPTION:
    Automatically checks for and installs updates for Al-Bayan Connect.

SYNTAX:
    .\AutoUpdater.ps1 [OPTIONS]

PARAMETERS:
    -UpdateServer <url>       Update server URL
    -Channel <channel>        Update channel (stable/beta/dev)
    -CurrentVersion <version> Current version (auto-detected if not specified)
    -CheckOnly               Only check for updates, don't install
    -Force                   Force update even if same version
    -Silent                  Run silently without user interaction
    -Verbose                 Enable verbose output
    -Help                    Show this help message

EXAMPLES:
    # Check for updates
    .\AutoUpdater.ps1 -CheckOnly

    # Update from beta channel
    .\AutoUpdater.ps1 -Channel beta

    # Silent update
    .\AutoUpdater.ps1 -Silent

    # Force update
    .\AutoUpdater.ps1 -Force

UPDATE CHANNELS:
    stable  - Stable releases (recommended)
    beta    - Beta releases with new features
    dev     - Development builds (not recommended for production)

"@
}

function Get-CurrentVersion {
    if (-not [string]::IsNullOrEmpty($CurrentVersion)) {
        return $CurrentVersion
    }
    
    try {
        # Try to get version from registry
        $regPath = "HKCU:\Software\Al-Bayan\Connect"
        $version = Get-ItemProperty -Path $regPath -Name "Version" -ErrorAction SilentlyContinue
        if ($version) {
            Write-Log "Current version from registry: $($version.Version)" "INFO"
            return $version.Version
        }
        
        # Try to get version from installed files
        $installPath = Get-ItemProperty -Path $regPath -Name "InstallPath" -ErrorAction SilentlyContinue
        if ($installPath -and (Test-Path $installPath.InstallPath)) {
            $launcherPath = Join-Path $installPath.InstallPath "AlBayanConnectLauncher.exe"
            if (Test-Path $launcherPath) {
                $fileVersion = [System.Diagnostics.FileVersionInfo]::GetVersionInfo($launcherPath)
                Write-Log "Current version from file: $($fileVersion.FileVersion)" "INFO"
                return $fileVersion.FileVersion
            }
        }
        
        Write-Log "Could not determine current version" "WARN"
        return "0.0.0.0"
    }
    catch {
        Write-Log "Error getting current version: $($_.Exception.Message)" "ERROR"
        return "0.0.0.0"
    }
}

function Get-UpdateManifest {
    try {
        Write-Log "Fetching update manifest from: $script:UpdateManifestUrl" "INFO"
        
        $headers = @{
            "User-Agent" = "Al-Bayan Connect Auto-Updater/$script:ScriptVersion"
            "X-Current-Version" = $currentVersion
            "X-Update-Channel" = $Channel
            "X-Platform" = "Windows"
            "X-Architecture" = $env:PROCESSOR_ARCHITECTURE
        }
        
        $response = Invoke-RestMethod -Uri $script:UpdateManifestUrl -Headers $headers -TimeoutSec 30
        
        Write-Log "Update manifest retrieved successfully" "INFO"
        return $response
    }
    catch {
        Write-Log "Failed to fetch update manifest: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

function Compare-Versions {
    param(
        [string]$Version1,
        [string]$Version2
    )
    
    try {
        $v1 = [System.Version]::Parse($Version1)
        $v2 = [System.Version]::Parse($Version2)
        
        return $v1.CompareTo($v2)
    }
    catch {
        Write-Log "Error comparing versions: $($_.Exception.Message)" "ERROR"
        return 0
    }
}

function Test-UpdateAvailable {
    param([object]$Manifest)
    
    if (-not $Manifest -or -not $Manifest.latestVersion) {
        return $false
    }
    
    $latestVersion = $Manifest.latestVersion
    $comparison = Compare-Versions -Version1 $latestVersion -Version2 $currentVersion
    
    Write-Log "Version comparison: Current=$currentVersion, Latest=$latestVersion" "INFO"
    
    if ($Force) {
        Write-Log "Force update requested" "INFO"
        return $true
    }
    
    return $comparison -gt 0
}

function Download-Update {
    param([object]$UpdateInfo)
    
    try {
        $downloadUrl = $UpdateInfo.downloadUrl
        $fileName = $UpdateInfo.fileName
        $fileSize = $UpdateInfo.fileSize
        $checksum = $UpdateInfo.sha256Checksum
        
        Write-Log "Downloading update: $fileName" "INFO"
        Write-Log "Download URL: $downloadUrl" "INFO"
        Write-Log "File size: $([math]::Round($fileSize / 1MB, 2)) MB" "INFO"
        
        # Create download directory
        if (-not (Test-Path $script:DownloadPath)) {
            New-Item -Path $script:DownloadPath -ItemType Directory -Force | Out-Null
        }
        
        $localPath = Join-Path $script:DownloadPath $fileName
        
        # Download with progress
        $webClient = New-Object System.Net.WebClient
        $webClient.Headers.Add("User-Agent", "Al-Bayan Connect Auto-Updater/$script:ScriptVersion")
        
        if (-not $Silent) {
            Register-ObjectEvent -InputObject $webClient -EventName DownloadProgressChanged -Action {
                $percent = $Event.SourceEventArgs.ProgressPercentage
                Write-Progress -Activity "Downloading Update" -Status "$percent% Complete" -PercentComplete $percent
            } | Out-Null
        }
        
        $webClient.DownloadFile($downloadUrl, $localPath)
        $webClient.Dispose()
        
        if (-not $Silent) {
            Write-Progress -Activity "Downloading Update" -Completed
        }
        
        # Verify checksum
        Write-Log "Verifying download integrity..." "INFO"
        $actualChecksum = Get-FileHash -Path $localPath -Algorithm SHA256
        
        if ($actualChecksum.Hash -eq $checksum) {
            Write-Log "Download verification: PASSED" "SUCCESS"
            return $localPath
        } else {
            Write-Log "Download verification: FAILED" "ERROR"
            Write-Log "Expected: $checksum" "ERROR"
            Write-Log "Actual: $($actualChecksum.Hash)" "ERROR"
            Remove-Item -Path $localPath -Force -ErrorAction SilentlyContinue
            return $null
        }
    }
    catch {
        Write-Log "Download failed: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

function Backup-CurrentInstallation {
    try {
        Write-Log "Creating backup of current installation..." "INFO"
        
        # Get current installation path
        $regPath = "HKCU:\Software\Al-Bayan\Connect"
        $installPath = Get-ItemProperty -Path $regPath -Name "InstallPath" -ErrorAction SilentlyContinue
        
        if (-not $installPath -or -not (Test-Path $installPath.InstallPath)) {
            Write-Log "Current installation path not found" "WARN"
            return $false
        }
        
        # Create backup directory
        $backupDir = Join-Path $script:BackupPath (Get-Date -Format "yyyy-MM-dd_HH-mm-ss")
        if (-not (Test-Path $backupDir)) {
            New-Item -Path $backupDir -ItemType Directory -Force | Out-Null
        }
        
        # Copy critical files
        $criticalFiles = @(
            "AlBayanConnectLauncher.exe",
            "manifest.xml",
            "Scripts\*.vbs",
            "Scripts\*.ps1"
        )
        
        foreach ($pattern in $criticalFiles) {
            $sourcePath = Join-Path $installPath.InstallPath $pattern
            $files = Get-ChildItem -Path $sourcePath -ErrorAction SilentlyContinue
            
            foreach ($file in $files) {
                $relativePath = $file.FullName.Substring($installPath.InstallPath.Length + 1)
                $destPath = Join-Path $backupDir $relativePath
                $destDir = Split-Path $destPath -Parent
                
                if (-not (Test-Path $destDir)) {
                    New-Item -Path $destDir -ItemType Directory -Force | Out-Null
                }
                
                Copy-Item -Path $file.FullName -Destination $destPath -Force
            }
        }
        
        # Save current version info
        $versionInfo = @{
            Version = $currentVersion
            InstallPath = $installPath.InstallPath
            BackupDate = Get-Date
            Channel = $Channel
        }
        
        $versionInfo | ConvertTo-Json | Set-Content -Path (Join-Path $backupDir "version.json")
        
        Write-Log "Backup created: $backupDir" "SUCCESS"
        return $true
    }
    catch {
        Write-Log "Backup failed: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Install-Update {
    param([string]$InstallerPath)
    
    try {
        Write-Log "Installing update from: $InstallerPath" "INFO"
        
        # Verify installer signature
        $signature = Get-AuthenticodeSignature -FilePath $InstallerPath
        if ($signature.Status -ne "Valid") {
            Write-Log "Installer signature verification failed: $($signature.Status)" "ERROR"
            return $false
        }
        
        Write-Log "Installer signature verified" "SUCCESS"
        
        # Prepare installation arguments
        $msiArgs = @(
            "/i", "`"$InstallerPath`"",
            "/quiet",
            "/norestart",
            "/l*v", "`"$script:LogFile.Replace('.log', '_install.log')`""
        )
        
        if ($Force) {
            $msiArgs += "REINSTALL=ALL"
            $msiArgs += "REINSTALLMODE=vomus"
        }
        
        # Execute installation
        Write-Log "Executing installer..." "INFO"
        $process = Start-Process -FilePath "msiexec.exe" -ArgumentList $msiArgs -Wait -PassThru -NoNewWindow
        
        $exitCode = $process.ExitCode
        Write-Log "Installation completed with exit code: $exitCode" "INFO"
        
        switch ($exitCode) {
            0 { 
                Write-Log "Update installed successfully" "SUCCESS"
                return $true
            }
            3010 { 
                Write-Log "Update installed successfully (restart required)" "SUCCESS"
                return $true
            }
            1602 { 
                Write-Log "Installation cancelled by user" "WARN"
                return $false
            }
            1603 { 
                Write-Log "Fatal error during installation" "ERROR"
                return $false
            }
            default { 
                Write-Log "Installation failed with exit code: $exitCode" "ERROR"
                return $false
            }
        }
    }
    catch {
        Write-Log "Installation error: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Cleanup-UpdateFiles {
    try {
        Write-Log "Cleaning up update files..." "INFO"
        
        if (Test-Path $script:DownloadPath) {
            Remove-Item -Path $script:DownloadPath -Recurse -Force -ErrorAction SilentlyContinue
        }
        
        # Clean old backups (keep last 5)
        if (Test-Path $script:BackupPath) {
            $backups = Get-ChildItem -Path $script:BackupPath | Sort-Object CreationTime -Descending
            if ($backups.Count -gt 5) {
                $oldBackups = $backups | Select-Object -Skip 5
                foreach ($backup in $oldBackups) {
                    Remove-Item -Path $backup.FullName -Recurse -Force -ErrorAction SilentlyContinue
                    Write-Log "Removed old backup: $($backup.Name)" "INFO"
                }
            }
        }
        
        Write-Log "Cleanup completed" "INFO"
    }
    catch {
        Write-Log "Cleanup error: $($_.Exception.Message)" "WARN"
    }
}

function Main {
    Write-Log "=== Al-Bayan Connect Auto-Updater Started ===" "INFO"
    Write-Log "Script Version: $script:ScriptVersion" "INFO"
    Write-Log "Update Channel: $Channel" "INFO"
    Write-Log "Update Server: $UpdateServer" "INFO"
    
    if ($Help) {
        Show-Help
        return 0
    }
    
    # Get current version
    $script:currentVersion = Get-CurrentVersion
    Write-Log "Current Version: $script:currentVersion" "INFO"
    
    # Get update manifest
    $manifest = Get-UpdateManifest
    if (-not $manifest) {
        Write-Log "Failed to retrieve update manifest" "ERROR"
        return 1
    }
    
    # Check if update is available
    $updateAvailable = Test-UpdateAvailable -Manifest $manifest
    
    if (-not $updateAvailable) {
        Write-Log "No updates available" "INFO"
        return 0
    }
    
    $latestVersion = $manifest.latestVersion
    Write-Log "Update available: $latestVersion" "SUCCESS"
    
    if ($CheckOnly) {
        Write-Log "Check-only mode: Update available but not installing" "INFO"
        return 0
    }
    
    # Create backup
    if (-not (Backup-CurrentInstallation)) {
        Write-Log "Backup failed. Aborting update for safety." "ERROR"
        return 2
    }
    
    # Download update
    $updateInfo = $manifest.updates | Where-Object { $_.version -eq $latestVersion -and $_.channel -eq $Channel } | Select-Object -First 1
    if (-not $updateInfo) {
        Write-Log "Update information not found for version $latestVersion" "ERROR"
        return 3
    }
    
    $installerPath = Download-Update -UpdateInfo $updateInfo
    if (-not $installerPath) {
        Write-Log "Update download failed" "ERROR"
        return 4
    }
    
    # Install update
    $installSuccess = Install-Update -InstallerPath $installerPath
    
    # Cleanup
    Cleanup-UpdateFiles
    
    if ($installSuccess) {
        Write-Log "=== Update Completed Successfully ===" "SUCCESS"
        Write-Log "Al-Bayan Connect has been updated to version $latestVersion" "SUCCESS"
        return 0
    } else {
        Write-Log "=== Update Failed ===" "ERROR"
        return 5
    }
}

# Execute main function
$exitCode = Main
Write-Log "Auto-updater completed with exit code: $exitCode" "INFO"
exit $exitCode
