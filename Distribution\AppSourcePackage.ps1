# Al-<PERSON>an Connect - Microsoft AppSource Package Creator
# Author: Dr. <PERSON>smail
# Copyright: © 2025 Al-Bayan AI Platform

param(
    [string]$Version = "*******",
    [string]$InstallerPath = "",
    [string]$OutputPath = "",
    [string]$ManifestPath = "",
    [switch]$CreatePackage,
    [switch]$ValidatePackage,
    [switch]$GenerateMetadata,
    [switch]$CreateScreenshots,
    [switch]$PrepareSubmission,
    [switch]$Verbose,
    [switch]$Help
)

# Configuration
$script:ScriptVersion = "1.0.0"
$script:LogFile = "$env:TEMP\AlBayanConnect_AppSource.log"

# AppSource package requirements
$script:AppSourceRequirements = @{
    MaxInstallerSize = 500MB
    SupportedFormats = @(".msi", ".exe")
    RequiredFiles = @("manifest.xml", "installer", "screenshots", "metadata")
    ScreenshotFormats = @(".png", ".jpg")
    ScreenshotSizes = @("1366x768", "1920x1080", "2560x1440")
    Languages = @("en-US", "ar-SA")
    Categories = @("Productivity", "Business", "Office Add-ins")
}

function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    if ($Verbose -or $Level -eq "ERROR" -or $Level -eq "WARN") {
        $color = switch ($Level) {
            "ERROR" { "Red" }
            "WARN" { "Yellow" }
            "SUCCESS" { "Green" }
            "PACKAGE" { "Magenta" }
            default { "White" }
        }
        Write-Host $logEntry -ForegroundColor $color
    }
    
    Add-Content -Path $script:LogFile -Value $logEntry -ErrorAction SilentlyContinue
}

function Show-Help {
    Write-Host @"

Al-Bayan Connect AppSource Package Creator v$script:ScriptVersion

DESCRIPTION:
    Creates Microsoft AppSource submission package for Al-Bayan Connect
    with all required metadata, screenshots, and validation.

SYNTAX:
    .\AppSourcePackage.ps1 [OPTIONS]

PARAMETERS:
    -Version <version>        Product version (e.g., *******)
    -InstallerPath <path>     Path to signed MSI installer
    -OutputPath <path>        Output directory for package
    -ManifestPath <path>      Path to Office Add-in manifest
    -CreatePackage           Create complete AppSource package
    -ValidatePackage         Validate existing package
    -GenerateMetadata        Generate AppSource metadata
    -CreateScreenshots       Create application screenshots
    -PrepareSubmission       Prepare final submission package
    -Verbose                 Enable verbose output
    -Help                    Show this help message

EXAMPLES:
    # Create complete AppSource package
    .\AppSourcePackage.ps1 -CreatePackage -Version "*******" -InstallerPath "installer.msi"

    # Generate metadata only
    .\AppSourcePackage.ps1 -GenerateMetadata -Version "*******"

    # Validate existing package
    .\AppSourcePackage.ps1 -ValidatePackage -OutputPath "AppSourcePackage"

    # Prepare final submission
    .\AppSourcePackage.ps1 -PrepareSubmission -OutputPath "AppSourcePackage"

APPSOURCE REQUIREMENTS:
    - Signed MSI installer (max 500MB)
    - Office Add-in manifest
    - Application screenshots (multiple sizes)
    - Detailed metadata and descriptions
    - Privacy policy and terms of service
    - Support documentation

"@
}

function Initialize-AppSourcePackage {
    Write-Log "Initializing AppSource package creation..." "PACKAGE"
    
    # Set default paths
    if (-not $OutputPath) {
        $OutputPath = "AppSourcePackage_v$Version"
    }
    
    if (-not $InstallerPath) {
        $InstallerPath = "bin\Release\AlBayanConnectInstaller.msi"
    }
    
    if (-not $ManifestPath) {
        $ManifestPath = "manifest.xml"
    }
    
    # Create package directory structure
    $packageDirs = @(
        $OutputPath,
        "$OutputPath\Installer",
        "$OutputPath\Screenshots",
        "$OutputPath\Metadata",
        "$OutputPath\Documentation",
        "$OutputPath\Localization",
        "$OutputPath\Assets"
    )
    
    foreach ($dir in $packageDirs) {
        if (-not (Test-Path $dir)) {
            New-Item -Path $dir -ItemType Directory -Force | Out-Null
            Write-Log "Created directory: $dir" "SUCCESS"
        }
    }
    
    Write-Log "Package structure initialized: $OutputPath" "SUCCESS"
    return $OutputPath
}

function Copy-InstallerFiles {
    param([string]$PackagePath)
    
    Write-Log "Copying installer files..." "PACKAGE"
    
    # Validate installer
    if (-not (Test-Path $InstallerPath)) {
        throw "Installer not found: $InstallerPath"
    }
    
    $installerInfo = Get-Item $InstallerPath
    $installerSize = $installerInfo.Length
    
    if ($installerSize -gt $script:AppSourceRequirements.MaxInstallerSize) {
        throw "Installer size ($([math]::Round($installerSize / 1MB, 2)) MB) exceeds AppSource limit (500 MB)"
    }
    
    # Check digital signature
    $signature = Get-AuthenticodeSignature -FilePath $InstallerPath
    if ($signature.Status -ne "Valid") {
        throw "Installer must be digitally signed for AppSource submission"
    }
    
    # Copy installer
    $installerDest = Join-Path $PackagePath "Installer\AlBayanConnectInstaller.msi"
    Copy-Item -Path $InstallerPath -Destination $installerDest -Force
    Write-Log "Installer copied: $installerDest" "SUCCESS"
    
    # Copy manifest if available
    if (Test-Path $ManifestPath) {
        $manifestDest = Join-Path $PackagePath "Installer\manifest.xml"
        Copy-Item -Path $ManifestPath -Destination $manifestDest -Force
        Write-Log "Manifest copied: $manifestDest" "SUCCESS"
    }
    
    return @{
        InstallerPath = $installerDest
        ManifestPath = if (Test-Path $ManifestPath) { $manifestDest } else { $null }
        Size = $installerSize
        Signature = $signature
    }
}

function New-AppSourceMetadata {
    param([string]$PackagePath)
    
    Write-Log "Generating AppSource metadata..." "PACKAGE"
    
    # Create app metadata
    $appMetadata = @{
        AppId = "al-bayan-connect"
        DisplayName = "Al-Bayan Connect"
        Version = $Version
        Publisher = "Al-Bayan AI Platform"
        PublisherDisplayName = "Dr. Mohammed Yagoub Esmail"
        Category = "Productivity"
        Subcategory = "Office Add-ins"
        SupportedLanguages = @("en-US", "ar-SA")
        SupportedOfficeVersions = @("Office 2016", "Office 2019", "Office 365")
        SupportedPlatforms = @("Windows 10", "Windows 11")
        Description = @{
            Short = "Revolutionary bilingual dictation add-in for Microsoft Office with intelligent Arabic-English language detection."
            Long = @"
Al-Bayan Connect transforms your Microsoft Office experience with cutting-edge bilingual dictation technology. Seamlessly switch between Arabic and English with intelligent language detection, advanced voice commands, and enterprise-grade security.

Key Features:
• Intelligent bilingual dictation (Arabic/English)
• Real-time language detection and switching
• Advanced voice commands and custom shortcuts
• Enterprise security and compliance
• Comprehensive analytics and productivity insights
• Seamless integration with Word, PowerPoint, and Outlook
• Offline and online processing capabilities
• Custom command creation and training

Perfect for:
• Bilingual professionals and businesses
• Educational institutions
• Government organizations
• International corporations
• Content creators and writers
• Anyone working with Arabic and English content

Al-Bayan Connect leverages advanced AI to understand context, improve accuracy over time, and provide a natural dictation experience that adapts to your speaking style and vocabulary.
"@
        }
        Keywords = @(
            "dictation", "voice", "speech", "Arabic", "English", "bilingual",
            "productivity", "office", "accessibility", "AI", "voice commands"
        )
        Screenshots = @()
        Videos = @()
        SupportInfo = @{
            SupportUrl = "https://support.al-bayan.ai"
            DocumentationUrl = "https://docs.al-bayan.ai"
            CommunityUrl = "https://community.al-bayan.ai"
            ContactEmail = "<EMAIL>"
        }
        LegalInfo = @{
            PrivacyPolicyUrl = "https://al-bayan.ai/privacy"
            TermsOfServiceUrl = "https://al-bayan.ai/terms"
            LicenseUrl = "https://al-bayan.ai/license"
        }
        Pricing = @{
            Model = "Freemium"
            FreeFeatures = @(
                "Basic dictation in Arabic and English",
                "Standard voice commands",
                "Basic analytics"
            )
            PremiumFeatures = @(
                "Advanced language detection",
                "Custom voice commands",
                "Enterprise security features",
                "Advanced analytics",
                "Priority support"
            )
        }
        SystemRequirements = @{
            MinimumOS = "Windows 10 version 1903"
            RecommendedOS = "Windows 11"
            OfficeVersions = @("Office 2016", "Office 2019", "Office 365")
            Memory = "4 GB RAM minimum, 8 GB recommended"
            Storage = "500 MB available space"
            Network = "Internet connection required for initial setup and updates"
            Audio = "Microphone required for dictation"
            Browser = "Chrome, Edge, or Firefox for web features"
        }
    }
    
    # Save metadata as JSON
    $metadataPath = Join-Path $PackagePath "Metadata\app-metadata.json"
    $appMetadata | ConvertTo-Json -Depth 10 | Set-Content -Path $metadataPath -Encoding UTF8
    Write-Log "App metadata saved: $metadataPath" "SUCCESS"
    
    # Create AppSource submission manifest
    $submissionManifest = @{
        SchemaVersion = "1.0"
        AppInfo = @{
            AppId = $appMetadata.AppId
            Version = $appMetadata.Version
            DisplayName = $appMetadata.DisplayName
            Publisher = $appMetadata.Publisher
        }
        PackageInfo = @{
            PackageType = "OfficeAddin"
            InstallationType = "MSI"
            SupportedPlatforms = $appMetadata.SupportedPlatforms
            SupportedLanguages = $appMetadata.SupportedLanguages
        }
        Certification = @{
            SecurityReview = "Pending"
            CompatibilityTesting = "Pending"
            ContentReview = "Pending"
            TechnicalValidation = "Pending"
        }
        Submission = @{
            SubmissionDate = Get-Date -Format "yyyy-MM-dd"
            SubmissionVersion = "1.0"
            Notes = "Initial submission of Al-Bayan Connect bilingual dictation add-in"
        }
    }
    
    $manifestPath = Join-Path $PackagePath "Metadata\submission-manifest.json"
    $submissionManifest | ConvertTo-Json -Depth 10 | Set-Content -Path $manifestPath -Encoding UTF8
    Write-Log "Submission manifest saved: $manifestPath" "SUCCESS"
    
    return $appMetadata
}

function New-AppSourceScreenshots {
    param([string]$PackagePath)
    
    Write-Log "Creating AppSource screenshots..." "PACKAGE"
    
    # Define required screenshots
    $screenshots = @(
        @{
            Name = "main-interface-en"
            Title = "Al-Bayan Connect Main Interface (English)"
            Description = "Main dictation interface showing English language mode with voice commands panel"
            Size = "1920x1080"
            Language = "en-US"
        },
        @{
            Name = "main-interface-ar"
            Title = "Al-Bayan Connect Main Interface (Arabic)"
            Description = "Main dictation interface showing Arabic language mode with RTL layout"
            Size = "1920x1080"
            Language = "ar-SA"
        },
        @{
            Name = "word-integration"
            Title = "Microsoft Word Integration"
            Description = "Al-Bayan Connect seamlessly integrated into Microsoft Word ribbon"
            Size = "1366x768"
            Language = "en-US"
        },
        @{
            Name = "language-detection"
            Title = "Intelligent Language Detection"
            Description = "Real-time language detection switching between Arabic and English"
            Size = "1920x1080"
            Language = "en-US"
        },
        @{
            Name = "voice-commands"
            Title = "Advanced Voice Commands"
            Description = "Custom voice commands and shortcuts configuration panel"
            Size = "1366x768"
            Language = "en-US"
        },
        @{
            Name = "analytics-dashboard"
            Title = "Productivity Analytics"
            Description = "Comprehensive analytics dashboard showing usage statistics and productivity insights"
            Size = "1920x1080"
            Language = "en-US"
        },
        @{
            Name = "settings-panel"
            Title = "Settings and Configuration"
            Description = "Advanced settings panel with enterprise security and customization options"
            Size = "1366x768"
            Language = "en-US"
        }
    )
    
    # Create screenshot metadata
    $screenshotMetadata = @{
        Screenshots = @()
        Requirements = $script:AppSourceRequirements.ScreenshotSizes
        Formats = $script:AppSourceRequirements.ScreenshotFormats
        TotalCount = $screenshots.Count
        Languages = @("en-US", "ar-SA")
    }
    
    foreach ($screenshot in $screenshots) {
        # Create placeholder screenshot info (actual screenshots would be created separately)
        $screenshotInfo = @{
            FileName = "$($screenshot.Name).png"
            Title = $screenshot.Title
            Description = $screenshot.Description
            Size = $screenshot.Size
            Language = $screenshot.Language
            FilePath = "Screenshots\$($screenshot.Name).png"
            Status = "Required - To be created"
        }
        
        $screenshotMetadata.Screenshots += $screenshotInfo
        
        # Create placeholder file with instructions
        $placeholderPath = Join-Path $PackagePath "Screenshots\$($screenshot.Name).png"
        $instructionFile = $placeholderPath.Replace(".png", "_INSTRUCTIONS.txt")
        
        $instructions = @"
Screenshot Required: $($screenshot.Title)
=====================================

Description: $($screenshot.Description)
Size: $($screenshot.Size)
Language: $($screenshot.Language)
Format: PNG (preferred) or JPG

Instructions:
1. Take screenshot of Al-Bayan Connect showing: $($screenshot.Description)
2. Ensure screenshot is exactly $($screenshot.Size) pixels
3. Save as high-quality PNG file
4. Name the file: $($screenshot.Name).png
5. Replace this instruction file with the actual screenshot

Quality Requirements:
- High resolution and clarity
- Professional appearance
- Show actual Al-Bayan Connect interface
- Include relevant UI elements and text
- Ensure proper language display ($($screenshot.Language))

Note: Screenshots are critical for AppSource approval and user engagement.
"@
        
        Set-Content -Path $instructionFile -Value $instructions -Encoding UTF8
    }
    
    # Save screenshot metadata
    $metadataPath = Join-Path $PackagePath "Screenshots\screenshot-metadata.json"
    $screenshotMetadata | ConvertTo-Json -Depth 10 | Set-Content -Path $metadataPath -Encoding UTF8
    
    Write-Log "Screenshot templates created: $($screenshots.Count) screenshots required" "SUCCESS"
    Write-Log "Screenshot metadata saved: $metadataPath" "SUCCESS"
    
    return $screenshotMetadata
}

function New-DocumentationPackage {
    param([string]$PackagePath)
    
    Write-Log "Creating documentation package..." "PACKAGE"
    
    # Create user guide
    $userGuide = @"
# Al-Bayan Connect User Guide

## Getting Started

### Installation
1. Download Al-Bayan Connect installer
2. Run the installer as administrator
3. Follow the installation wizard
4. Restart Microsoft Office applications

### First Use
1. Open Microsoft Word, PowerPoint, or Outlook
2. Look for the Al-Bayan Connect tab in the ribbon
3. Click "Start Dictation" to begin
4. Speak naturally in Arabic or English

## Features

### Bilingual Dictation
- Automatic language detection
- Seamless switching between Arabic and English
- Context-aware language selection
- Real-time transcription

### Voice Commands
- Built-in formatting commands
- Custom command creation
- Keyboard shortcuts via voice
- Document navigation commands

### Analytics
- Usage statistics
- Productivity insights
- Accuracy metrics
- Performance tracking

## Support
- Email: <EMAIL>
- Documentation: https://docs.al-bayan.ai
- Community: https://community.al-bayan.ai
"@
    
    $userGuidePath = Join-Path $PackagePath "Documentation\UserGuide.md"
    Set-Content -Path $userGuidePath -Value $userGuide -Encoding UTF8
    
    # Create admin guide
    $adminGuide = @"
# Al-Bayan Connect Administrator Guide

## Enterprise Deployment

### System Requirements
- Windows 10 version 1903 or later
- Microsoft Office 2016 or later
- 4 GB RAM minimum
- Internet connection for activation

### Silent Installation
```
msiexec /i AlBayanConnectInstaller.msi /quiet INSTALLFOLDER="C:\Program Files\Al-Bayan Connect"
```

### Group Policy Deployment
1. Copy MSI to network share
2. Create new GPO
3. Add software installation policy
4. Configure deployment options

### Enterprise Configuration
- Centralized settings management
- Usage analytics and reporting
- Security policy enforcement
- License management

## Support
- Enterprise Support: <EMAIL>
- Technical Documentation: https://docs.al-bayan.ai/admin
"@
    
    $adminGuidePath = Join-Path $PackagePath "Documentation\AdminGuide.md"
    Set-Content -Path $adminGuidePath -Value $adminGuide -Encoding UTF8
    
    # Create privacy policy
    $privacyPolicy = @"
# Al-Bayan Connect Privacy Policy

## Data Collection
Al-Bayan Connect collects minimal data necessary for functionality:
- Voice input for transcription (processed locally when possible)
- Usage analytics (anonymized)
- Error reports (no personal data)

## Data Processing
- Voice data processed locally when possible
- Cloud processing only when necessary for accuracy
- No voice data stored permanently
- All data encrypted in transit and at rest

## Data Sharing
- No personal data shared with third parties
- Anonymized analytics may be used for product improvement
- No voice recordings stored or shared

## User Rights
- Access to personal data
- Data deletion requests
- Opt-out of analytics
- Data portability

Contact: <EMAIL>
"@
    
    $privacyPath = Join-Path $PackagePath "Documentation\PrivacyPolicy.md"
    Set-Content -Path $privacyPath -Value $privacyPolicy -Encoding UTF8
    
    Write-Log "Documentation package created" "SUCCESS"
}

function Test-AppSourcePackage {
    param([string]$PackagePath)
    
    Write-Log "Validating AppSource package..." "PACKAGE"
    
    $validationResults = @()
    
    # Check required files
    $requiredFiles = @(
        "Installer\AlBayanConnectInstaller.msi",
        "Metadata\app-metadata.json",
        "Metadata\submission-manifest.json",
        "Screenshots\screenshot-metadata.json",
        "Documentation\UserGuide.md",
        "Documentation\PrivacyPolicy.md"
    )
    
    foreach ($file in $requiredFiles) {
        $filePath = Join-Path $PackagePath $file
        if (Test-Path $filePath) {
            $validationResults += "✓ $file: FOUND"
        } else {
            $validationResults += "✗ $file: MISSING"
        }
    }
    
    # Check installer signature
    $installerPath = Join-Path $PackagePath "Installer\AlBayanConnectInstaller.msi"
    if (Test-Path $installerPath) {
        $signature = Get-AuthenticodeSignature -FilePath $installerPath
        if ($signature.Status -eq "Valid") {
            $validationResults += "✓ Installer signature: VALID"
        } else {
            $validationResults += "✗ Installer signature: INVALID"
        }
    }
    
    # Check screenshots
    $screenshotDir = Join-Path $PackagePath "Screenshots"
    $screenshotFiles = Get-ChildItem -Path $screenshotDir -Filter "*.png" -ErrorAction SilentlyContinue
    $validationResults += "📷 Screenshots: $($screenshotFiles.Count) files found"
    
    # Generate validation report
    $reportPath = Join-Path $PackagePath "validation-report.txt"
    $report = @"
Al-Bayan Connect AppSource Package Validation Report
===================================================

Generated: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
Package Path: $PackagePath
Version: $Version

Validation Results:
"@
    
    foreach ($result in $validationResults) {
        $report += "`n$result"
    }
    
    $report += @"

Next Steps:
1. Review validation results above
2. Create required screenshots (see Screenshots folder)
3. Test installer on clean systems
4. Submit to Microsoft AppSource Partner Center
5. Monitor certification process

For support: <EMAIL>
"@
    
    Set-Content -Path $reportPath -Value $report -Encoding UTF8
    
    Write-Log "Validation report created: $reportPath" "SUCCESS"
    return $validationResults
}

function New-SubmissionPackage {
    param([string]$PackagePath)
    
    Write-Log "Preparing final submission package..." "PACKAGE"
    
    $submissionPath = "$PackagePath\SUBMISSION_READY"
    if (-not (Test-Path $submissionPath)) {
        New-Item -Path $submissionPath -ItemType Directory -Force | Out-Null
    }
    
    # Copy essential files for submission
    $submissionFiles = @(
        @{ Source = "Installer\AlBayanConnectInstaller.msi"; Dest = "AlBayanConnectInstaller.msi" },
        @{ Source = "Installer\manifest.xml"; Dest = "manifest.xml" },
        @{ Source = "Metadata\app-metadata.json"; Dest = "app-metadata.json" },
        @{ Source = "Documentation\UserGuide.md"; Dest = "UserGuide.md" },
        @{ Source = "Documentation\PrivacyPolicy.md"; Dest = "PrivacyPolicy.md" }
    )
    
    foreach ($file in $submissionFiles) {
        $sourcePath = Join-Path $PackagePath $file.Source
        $destPath = Join-Path $submissionPath $file.Dest
        
        if (Test-Path $sourcePath) {
            Copy-Item -Path $sourcePath -Destination $destPath -Force
            Write-Log "Copied: $($file.Dest)" "SUCCESS"
        }
    }
    
    # Copy screenshots
    $screenshotSource = Join-Path $PackagePath "Screenshots"
    $screenshotDest = Join-Path $submissionPath "Screenshots"
    if (Test-Path $screenshotSource) {
        Copy-Item -Path $screenshotSource -Destination $screenshotDest -Recurse -Force
    }
    
    # Create submission checklist
    $checklist = @"
Al-Bayan Connect AppSource Submission Checklist
==============================================

Pre-Submission Requirements:
☐ Installer is digitally signed
☐ All screenshots are created and high-quality
☐ Privacy policy is complete and accessible
☐ Terms of service are complete and accessible
☐ Support documentation is comprehensive
☐ App has been tested on clean systems
☐ Office Add-in manifest is validated
☐ All metadata is accurate and complete

Submission Process:
☐ Create Microsoft Partner Center account
☐ Start new Office Add-in submission
☐ Upload installer package
☐ Upload screenshots and metadata
☐ Complete store listing information
☐ Submit for certification
☐ Respond to certification feedback
☐ Monitor certification status

Post-Submission:
☐ Monitor certification progress
☐ Respond to Microsoft feedback promptly
☐ Prepare for potential re-submission
☐ Plan marketing and launch activities

Contact Information:
- AppSource Support: <EMAIL>
- Technical Support: <EMAIL>
- Business Inquiries: <EMAIL>

Submission Date: _______________
Submitted By: _______________
Certification Status: _______________
"@
    
    $checklistPath = Join-Path $submissionPath "SUBMISSION_CHECKLIST.txt"
    Set-Content -Path $checklistPath -Value $checklist -Encoding UTF8
    
    Write-Log "Submission package ready: $submissionPath" "SUCCESS"
    return $submissionPath
}

function Main {
    Write-Log "=== Al-Bayan Connect AppSource Package Creator Started ===" "INFO"
    Write-Log "Creator Version: $script:ScriptVersion" "INFO"
    Write-Log "Product Version: $Version" "INFO"
    
    if ($Help) {
        Show-Help
        return 0
    }
    
    try {
        # Initialize package
        $packagePath = Initialize-AppSourcePackage
        
        if ($CreatePackage -or $GenerateMetadata) {
            # Copy installer files
            $installerInfo = Copy-InstallerFiles -PackagePath $packagePath
            Write-Log "Installer validated and copied" "SUCCESS"
            
            # Generate metadata
            $metadata = New-AppSourceMetadata -PackagePath $packagePath
            Write-Log "Metadata generated" "SUCCESS"
        }
        
        if ($CreatePackage -or $CreateScreenshots) {
            # Create screenshots
            $screenshots = New-AppSourceScreenshots -PackagePath $packagePath
            Write-Log "Screenshot templates created" "SUCCESS"
        }
        
        if ($CreatePackage) {
            # Create documentation
            New-DocumentationPackage -PackagePath $packagePath
            Write-Log "Documentation package created" "SUCCESS"
        }
        
        if ($ValidatePackage) {
            # Validate package
            $validation = Test-AppSourcePackage -PackagePath $packagePath
            Write-Host "`nValidation Results:" -ForegroundColor Cyan
            foreach ($result in $validation) {
                Write-Host "  $result" -ForegroundColor White
            }
        }
        
        if ($PrepareSubmission) {
            # Prepare submission
            $submissionPath = New-SubmissionPackage -PackagePath $packagePath
            Write-Log "Submission package prepared: $submissionPath" "SUCCESS"
        }
        
        Write-Log "=== AppSource Package Creation Completed ===" "SUCCESS"
        Write-Log "Package Location: $packagePath" "INFO"
        
        # Open package directory
        Start-Process $packagePath
        
        return 0
    }
    catch {
        Write-Log "AppSource package creation failed: $($_.Exception.Message)" "ERROR"
        return 1
    }
}

# Execute main function
$exitCode = Main
exit $exitCode
