// <PERSON><PERSON><PERSON><PERSON> Connect - Voice Command Processor
// Author: Dr. <PERSON>
// Copyright: © 2025 Al-Bayan AI Platform

import {
  VoiceCommand,
  CustomCommand,
  Language,
  CommandAction,
  CommandCategory,
  UserSettings
} from '@types/index';

export class VoiceCommandProcessor {
  private settings: UserSettings | null = null;
  private customCommands: CustomCommand[] = [];
  
  // Built-in bilingual commands
  private readonly BUILT_IN_COMMANDS: VoiceCommand[] = [
    // English Punctuation Commands
    {
      id: 'en-period',
      phrase: 'period',
      action: CommandAction.INSERT_TEXT,
      language: Language.ENGLISH,
      category: CommandCategory.PUNCTUATION,
      isCustom: false,
      createdAt: new Date(),
      usageCount: 0
    },
    {
      id: 'en-comma',
      phrase: 'comma',
      action: CommandAction.INSERT_TEXT,
      language: Language.ENGLISH,
      category: CommandCategory.PUNCTUATION,
      isCustom: false,
      createdAt: new Date(),
      usageCount: 0
    },
    {
      id: 'en-question-mark',
      phrase: 'question mark',
      action: CommandAction.INSERT_TEXT,
      language: Language.ENGLISH,
      category: CommandCategory.PUNCTUATION,
      isCustom: false,
      createdAt: new Date(),
      usageCount: 0
    },
    {
      id: 'en-exclamation',
      phrase: 'exclamation point',
      action: CommandAction.INSERT_TEXT,
      language: Language.ENGLISH,
      category: CommandCategory.PUNCTUATION,
      isCustom: false,
      createdAt: new Date(),
      usageCount: 0
    },
    
    // Arabic Punctuation Commands
    {
      id: 'ar-period',
      phrase: 'نقطة',
      action: CommandAction.INSERT_TEXT,
      language: Language.ARABIC,
      category: CommandCategory.PUNCTUATION,
      isCustom: false,
      createdAt: new Date(),
      usageCount: 0
    },
    {
      id: 'ar-comma',
      phrase: 'فاصلة',
      action: CommandAction.INSERT_TEXT,
      language: Language.ARABIC,
      category: CommandCategory.PUNCTUATION,
      isCustom: false,
      createdAt: new Date(),
      usageCount: 0
    },
    {
      id: 'ar-question-mark',
      phrase: 'علامة استفهام',
      action: CommandAction.INSERT_TEXT,
      language: Language.ARABIC,
      category: CommandCategory.PUNCTUATION,
      isCustom: false,
      createdAt: new Date(),
      usageCount: 0
    },
    {
      id: 'ar-exclamation',
      phrase: 'علامة تعجب',
      action: CommandAction.INSERT_TEXT,
      language: Language.ARABIC,
      category: CommandCategory.PUNCTUATION,
      isCustom: false,
      createdAt: new Date(),
      usageCount: 0
    },
    
    // English Formatting Commands
    {
      id: 'en-bold',
      phrase: 'bold that',
      action: CommandAction.FORMAT_TEXT,
      language: Language.ENGLISH,
      category: CommandCategory.FORMATTING,
      isCustom: false,
      createdAt: new Date(),
      usageCount: 0
    },
    {
      id: 'en-italic',
      phrase: 'italicize that',
      action: CommandAction.FORMAT_TEXT,
      language: Language.ENGLISH,
      category: CommandCategory.FORMATTING,
      isCustom: false,
      createdAt: new Date(),
      usageCount: 0
    },
    {
      id: 'en-underline',
      phrase: 'underline that',
      action: CommandAction.FORMAT_TEXT,
      language: Language.ENGLISH,
      category: CommandCategory.FORMATTING,
      isCustom: false,
      createdAt: new Date(),
      usageCount: 0
    },
    
    // Arabic Formatting Commands
    {
      id: 'ar-bold',
      phrase: 'غامق',
      action: CommandAction.FORMAT_TEXT,
      language: Language.ARABIC,
      category: CommandCategory.FORMATTING,
      isCustom: false,
      createdAt: new Date(),
      usageCount: 0
    },
    {
      id: 'ar-italic',
      phrase: 'مائل',
      action: CommandAction.FORMAT_TEXT,
      language: Language.ARABIC,
      category: CommandCategory.FORMATTING,
      isCustom: false,
      createdAt: new Date(),
      usageCount: 0
    },
    {
      id: 'ar-underline',
      phrase: 'تسطير',
      action: CommandAction.FORMAT_TEXT,
      language: Language.ARABIC,
      category: CommandCategory.FORMATTING,
      isCustom: false,
      createdAt: new Date(),
      usageCount: 0
    },
    
    // English Navigation Commands
    {
      id: 'en-new-line',
      phrase: 'new line',
      action: CommandAction.NAVIGATE,
      language: Language.ENGLISH,
      category: CommandCategory.NAVIGATION,
      isCustom: false,
      createdAt: new Date(),
      usageCount: 0
    },
    {
      id: 'en-new-paragraph',
      phrase: 'new paragraph',
      action: CommandAction.NAVIGATE,
      language: Language.ENGLISH,
      category: CommandCategory.NAVIGATION,
      isCustom: false,
      createdAt: new Date(),
      usageCount: 0
    },
    
    // Arabic Navigation Commands
    {
      id: 'ar-new-line',
      phrase: 'سطر جديد',
      action: CommandAction.NAVIGATE,
      language: Language.ARABIC,
      category: CommandCategory.NAVIGATION,
      isCustom: false,
      createdAt: new Date(),
      usageCount: 0
    },
    {
      id: 'ar-new-paragraph',
      phrase: 'فقرة جديدة',
      action: CommandAction.NAVIGATE,
      language: Language.ARABIC,
      category: CommandCategory.NAVIGATION,
      isCustom: false,
      createdAt: new Date(),
      usageCount: 0
    },
    
    // English Editing Commands
    {
      id: 'en-delete',
      phrase: 'delete that',
      action: CommandAction.DELETE,
      language: Language.ENGLISH,
      category: CommandCategory.EDITING,
      isCustom: false,
      createdAt: new Date(),
      usageCount: 0
    },
    {
      id: 'en-undo',
      phrase: 'undo',
      action: CommandAction.UNDO,
      language: Language.ENGLISH,
      category: CommandCategory.EDITING,
      isCustom: false,
      createdAt: new Date(),
      usageCount: 0
    },
    
    // Arabic Editing Commands
    {
      id: 'ar-delete',
      phrase: 'حذف ذلك',
      action: CommandAction.DELETE,
      language: Language.ARABIC,
      category: CommandCategory.EDITING,
      isCustom: false,
      createdAt: new Date(),
      usageCount: 0
    },
    {
      id: 'ar-undo',
      phrase: 'تراجع',
      action: CommandAction.UNDO,
      language: Language.ARABIC,
      category: CommandCategory.EDITING,
      isCustom: false,
      createdAt: new Date(),
      usageCount: 0
    }
  ];

  // Command action mappings
  private readonly COMMAND_ACTIONS = {
    // Punctuation mappings
    'period': '.',
    'comma': ',',
    'question mark': '?',
    'exclamation point': '!',
    'نقطة': '.',
    'فاصلة': '،', // Arabic comma
    'علامة استفهام': '؟', // Arabic question mark
    'علامة تعجب': '!',
    
    // Navigation mappings
    'new line': '\n',
    'new paragraph': '\n\n',
    'سطر جديد': '\n',
    'فقرة جديدة': '\n\n'
  };

  constructor() {
    this.loadCustomCommands();
  }

  public async processText(text: string, language: Language): Promise<VoiceCommand | null> {
    const normalizedText = this.normalizeText(text);
    
    // Check custom commands first (higher priority)
    if (this.settings?.enableCustomCommands) {
      const customCommand = this.findCustomCommand(normalizedText, language);
      if (customCommand) {
        this.incrementUsageCount(customCommand);
        return customCommand;
      }
    }
    
    // Check built-in commands
    const builtInCommand = this.findBuiltInCommand(normalizedText, language);
    if (builtInCommand) {
      this.incrementUsageCount(builtInCommand);
      return builtInCommand;
    }
    
    return null;
  }

  private normalizeText(text: string): string {
    return text.trim().toLowerCase();
  }

  private findCustomCommand(text: string, language: Language): CustomCommand | null {
    return this.customCommands.find(command => 
      command.isActive &&
      (command.language === language || command.language === Language.AUTO_DETECT) &&
      this.matchesPhrase(text, command.phrase)
    ) || null;
  }

  private findBuiltInCommand(text: string, language: Language): VoiceCommand | null {
    return this.BUILT_IN_COMMANDS.find(command => 
      (command.language === language || command.language === Language.AUTO_DETECT) &&
      this.matchesPhrase(text, command.phrase)
    ) || null;
  }

  private matchesPhrase(text: string, phrase: string): boolean {
    const normalizedPhrase = this.normalizeText(phrase);
    
    // Exact match
    if (text === normalizedPhrase) {
      return true;
    }
    
    // Fuzzy matching for slight variations
    const similarity = this.calculateSimilarity(text, normalizedPhrase);
    return similarity > 0.85; // 85% similarity threshold
  }

  private calculateSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) {
      return 1.0;
    }
    
    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => 
      Array(str1.length + 1).fill(null)
    );
    
    for (let i = 0; i <= str1.length; i++) {
      matrix[0][i] = i;
    }
    
    for (let j = 0; j <= str2.length; j++) {
      matrix[j][0] = j;
    }
    
    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,     // deletion
          matrix[j - 1][i] + 1,     // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        );
      }
    }
    
    return matrix[str2.length][str1.length];
  }

  public getCommandAction(command: VoiceCommand): string | null {
    switch (command.action) {
      case CommandAction.INSERT_TEXT:
        return this.COMMAND_ACTIONS[command.phrase] || null;
      case CommandAction.FORMAT_TEXT:
        return this.getFormattingAction(command);
      case CommandAction.NAVIGATE:
        return this.COMMAND_ACTIONS[command.phrase] || null;
      case CommandAction.DELETE:
        return 'DELETE_SELECTION';
      case CommandAction.UNDO:
        return 'UNDO_LAST_ACTION';
      case CommandAction.CUSTOM:
        return this.getCustomCommandAction(command as CustomCommand);
      default:
        return null;
    }
  }

  private getFormattingAction(command: VoiceCommand): string {
    const formatMap = {
      'bold that': 'BOLD',
      'غامق': 'BOLD',
      'italicize that': 'ITALIC',
      'مائل': 'ITALIC',
      'underline that': 'UNDERLINE',
      'تسطير': 'UNDERLINE'
    };
    
    return formatMap[command.phrase] || 'UNKNOWN_FORMAT';
  }

  private getCustomCommandAction(command: CustomCommand): string {
    return command.parameters?.action || command.phrase;
  }

  private incrementUsageCount(command: VoiceCommand): void {
    command.usageCount++;
    
    if (command.isCustom) {
      this.saveCustomCommands();
    }
  }

  // Custom command management
  public addCustomCommand(command: Omit<CustomCommand, 'id' | 'createdAt' | 'usageCount'>): CustomCommand {
    const newCommand: CustomCommand = {
      ...command,
      id: this.generateCommandId(),
      createdAt: new Date(),
      usageCount: 0,
      isCustom: true
    };
    
    this.customCommands.push(newCommand);
    this.saveCustomCommands();
    
    return newCommand;
  }

  public updateCustomCommand(id: string, updates: Partial<CustomCommand>): boolean {
    const index = this.customCommands.findIndex(cmd => cmd.id === id);
    if (index === -1) return false;
    
    this.customCommands[index] = { ...this.customCommands[index], ...updates };
    this.saveCustomCommands();
    
    return true;
  }

  public deleteCustomCommand(id: string): boolean {
    const index = this.customCommands.findIndex(cmd => cmd.id === id);
    if (index === -1) return false;
    
    this.customCommands.splice(index, 1);
    this.saveCustomCommands();
    
    return true;
  }

  public getCustomCommands(): CustomCommand[] {
    return [...this.customCommands];
  }

  public getBuiltInCommands(): VoiceCommand[] {
    return [...this.BUILT_IN_COMMANDS];
  }

  public getAllCommands(): VoiceCommand[] {
    return [...this.BUILT_IN_COMMANDS, ...this.customCommands];
  }

  public getCommandsByCategory(category: CommandCategory): VoiceCommand[] {
    return this.getAllCommands().filter(cmd => cmd.category === category);
  }

  public getCommandsByLanguage(language: Language): VoiceCommand[] {
    return this.getAllCommands().filter(cmd => 
      cmd.language === language || cmd.language === Language.AUTO_DETECT
    );
  }

  private generateCommandId(): string {
    return `custom-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private loadCustomCommands(): void {
    try {
      const stored = localStorage.getItem('al-bayan-custom-commands');
      if (stored) {
        this.customCommands = JSON.parse(stored);
      }
    } catch (error) {
      console.error('Failed to load custom commands:', error);
      this.customCommands = [];
    }
  }

  private saveCustomCommands(): void {
    try {
      localStorage.setItem('al-bayan-custom-commands', JSON.stringify(this.customCommands));
    } catch (error) {
      console.error('Failed to save custom commands:', error);
    }
  }

  public updateSettings(settings: UserSettings): void {
    this.settings = settings;
  }

  public exportCommands(): string {
    return JSON.stringify({
      customCommands: this.customCommands,
      exportDate: new Date().toISOString(),
      version: '1.0'
    }, null, 2);
  }

  public importCommands(data: string): boolean {
    try {
      const parsed = JSON.parse(data);
      if (parsed.customCommands && Array.isArray(parsed.customCommands)) {
        this.customCommands = parsed.customCommands;
        this.saveCustomCommands();
        return true;
      }
    } catch (error) {
      console.error('Failed to import commands:', error);
    }
    return false;
  }
}
