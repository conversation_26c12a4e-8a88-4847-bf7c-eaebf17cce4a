// Al-Bayan Connect - Language Selector Component
// Author: Dr. <PERSON>
// Copyright: © 2025 Al-Bayan AI Platform

import React from 'react';
import { Button, Text } from '@fluentui/react-components';
import { Language } from '@types/index';

interface LanguageSelectorProps {
  currentLanguage: Language;
  onLanguageChange: (language: Language) => void;
  autoDetectionEnabled: boolean;
}

export const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  currentLanguage,
  onLanguageChange,
  autoDetectionEnabled
}) => {
  const languages = [
    { code: Language.AUTO_DETECT, label: 'Auto', arabicLabel: 'تلقائي' },
    { code: Language.ENGLISH, label: 'English', arabicLabel: 'English' },
    { code: Language.ARABIC, label: 'العربية', arabicLabel: 'العربية' }
  ];

  return (
    <div className="language-controls">
      <div className="language-selector">
        <Text size={300} weight="medium">Language:</Text>
        
        <div className="language-toggle">
          {languages.map((lang) => (
            <Button
              key={lang.code}
              className={`language-option ${currentLanguage === lang.code ? 'active' : ''}`}
              onClick={() => onLanguageChange(lang.code)}
              size="small"
              appearance={currentLanguage === lang.code ? 'primary' : 'subtle'}
            >
              {lang.label}
            </Button>
          ))}
        </div>
      </div>

      {autoDetectionEnabled && currentLanguage === Language.AUTO_DETECT && (
        <div className={`auto-detect-indicator ${currentLanguage !== Language.AUTO_DETECT ? '' : 'active'}`}>
          <Text size={200}>🤖 Auto-detection enabled</Text>
        </div>
      )}

      {currentLanguage !== Language.AUTO_DETECT && (
        <div className="current-language-info">
          <Text size={200} style={{
            direction: currentLanguage === Language.ARABIC ? 'rtl' : 'ltr',
            fontFamily: currentLanguage === Language.ARABIC ? 
              "'Traditional Arabic', 'Arabic Typesetting', 'Tahoma', sans-serif" : 
              "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif"
          }}>
            {currentLanguage === Language.ARABIC ? 
              'اللغة الحالية: العربية' : 
              'Current language: English'
            }
          </Text>
        </div>
      )}
    </div>
  );
};
