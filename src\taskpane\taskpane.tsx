// Al-Bayan Connect - Main Taskpane Component
// Author: Dr. <PERSON>
// Copyright: © 2025 Al-Bayan AI Platform

import React from 'react';
import ReactDOM from 'react-dom/client';
import { FluentProvider, webLightTheme } from '@fluentui/react-components';
import { DictationPanel } from '../components/DictationPanel';
import { ErrorBoundary } from '../components/ErrorBoundary';
import { SettingsProvider } from '../components/SettingsProvider';
import './taskpane.css';

const App: React.FC = () => {
  return (
    <FluentProvider theme={webLightTheme}>
      <ErrorBoundary>
        <SettingsProvider>
          <div className="al-bayan-app">
            <DictationPanel />
          </div>
        </SettingsProvider>
      </ErrorBoundary>
    </FluentProvider>
  );
};

// Initialize the React app when Office is ready
Office.onReady(() => {
  const container = document.getElementById('app-root');
  if (container) {
    const root = ReactDOM.createRoot(container);
    root.render(<App />);
  }
});
