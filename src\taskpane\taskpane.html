<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Al-Bayan Connect - Dictation Panel</title>
    
    <!-- Office.js -->
    <script type="text/javascript" src="https://appsforoffice.microsoft.com/lib/1/hosted/office.js"></script>
    
    <!-- Fluent UI Styles -->
    <link rel="stylesheet" href="https://static2.sharepointonline.com/files/fabric/office-ui-fabric-core/11.0.0/css/fabric.min.css">
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #faf9f8;
            direction: ltr;
        }
        
        .app-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
            color: white;
            padding: 16px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }
        
        .header .subtitle {
            margin: 4px 0 0 0;
            font-size: 12px;
            opacity: 0.9;
        }
        
        .content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            flex-direction: column;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #0078d4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error-message {
            background-color: #fde7e9;
            border: 1px solid #f1aeb5;
            color: #a4262c;
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 16px;
        }
        
        .success-message {
            background-color: #dff6dd;
            border: 1px solid #9fd89f;
            color: #107c10;
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 16px;
        }
        
        /* Arabic text support */
        .rtl {
            direction: rtl;
            text-align: right;
        }
        
        .arabic-text {
            font-family: 'Traditional Arabic', 'Arabic Typesetting', 'Tahoma', sans-serif;
            direction: rtl;
        }
        
        /* Responsive design */
        @media (max-width: 480px) {
            .content {
                padding: 12px;
            }
            
            .header h1 {
                font-size: 16px;
            }
        }
    </style>
</head>

<body>
    <div class="app-container">
        <div class="header">
            <h1>🎤 Al-Bayan Connect</h1>
            <div class="subtitle">Revolutionary Bilingual Dictation</div>
        </div>
        
        <div class="content">
            <div id="loading" class="loading">
                <div class="loading-spinner"></div>
                <div>Initializing Al-Bayan Connect...</div>
            </div>
            
            <div id="app-root" style="display: none;"></div>
        </div>
    </div>

    <script>
        // Initialize Office and show loading state
        Office.onReady((info) => {
            console.log('Office is ready. Host:', info.host, 'Platform:', info.platform);
            
            // Hide loading and show app
            document.getElementById('loading').style.display = 'none';
            document.getElementById('app-root').style.display = 'block';
            
            // The React app will be mounted here by webpack
        });
        
        // Error handling
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
            
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.innerHTML = `
                <strong>Error:</strong> ${event.error?.message || 'An unexpected error occurred'}
                <br><small>Please refresh the add-in or contact support if the problem persists.</small>
            `;
            
            const content = document.querySelector('.content');
            content.insertBefore(errorDiv, content.firstChild);
        });
        
        // Unhandled promise rejection handling
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.innerHTML = `
                <strong>Promise Error:</strong> ${event.reason?.message || 'An async operation failed'}
                <br><small>Please try again or contact support if the problem persists.</small>
            `;
            
            const content = document.querySelector('.content');
            content.insertBefore(errorDiv, content.firstChild);
        });
    </script>
</body>
</html>
