@echo off
REM Al-Bayan Connect - Silent Installation Script
REM Author: Dr<PERSON> <PERSON> Esmail
REM Copyright: © 2025 Al-Bayan AI Platform

setlocal enabledelayedexpansion

REM Configuration
set "INSTALLER_NAME=AlBayanConnectInstaller.msi"
set "LOG_FILE=%TEMP%\AlBayanConnect_SilentInstall.log"
set "ERROR_LOG=%TEMP%\AlBayanConnect_SilentInstall_Error.log"
set "INSTALL_DIR="
set "LANGUAGE=en"
set "FEATURES=ALL"
set "DESKTOP_SHORTCUT=1"
set "START_MENU=1"
set "AUTO_UPDATE=1"

echo ===============================================
echo Al-Bayan Connect Silent Installation
echo ===============================================
echo Date: %DATE% %TIME%
echo Computer: %COMPUTERNAME%
echo User: %USERNAME%
echo ===============================================

REM Parse command line arguments
:parse_args
if "%~1"=="" goto :start_install
if /i "%~1"=="/INSTALLDIR" (
    set "INSTALL_DIR=%~2"
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="/LANGUAGE" (
    set "LANGUAGE=%~2"
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="/FEATURES" (
    set "FEATURES=%~2"
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="/NODESKTOP" (
    set "DESKTOP_SHORTCUT=0"
    shift
    goto :parse_args
)
if /i "%~1"=="/NOSTARTMENU" (
    set "START_MENU=0"
    shift
    goto :parse_args
)
if /i "%~1"=="/NOAUTOUPDATE" (
    set "AUTO_UPDATE=0"
    shift
    goto :parse_args
)
if /i "%~1"=="/?" goto :show_help
if /i "%~1"=="/HELP" goto :show_help
shift
goto :parse_args

:start_install
echo Starting silent installation...
echo Installation parameters:
echo   Install Directory: %INSTALL_DIR%
echo   Language: %LANGUAGE%
echo   Features: %FEATURES%
echo   Desktop Shortcut: %DESKTOP_SHORTCUT%
echo   Start Menu: %START_MENU%
echo   Auto Update: %AUTO_UPDATE%
echo.

REM Check if installer exists
if not exist "%~dp0%INSTALLER_NAME%" (
    echo ERROR: Installer file not found: %~dp0%INSTALLER_NAME%
    echo Please ensure the installer is in the same directory as this script.
    goto :error_exit
)

REM Check for administrator privileges
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: Running without administrator privileges.
    echo Some features may not install correctly.
    echo.
)

REM Prepare installation command
set "MSIEXEC_CMD=msiexec.exe /i "%~dp0%INSTALLER_NAME%" /quiet /norestart"
set "MSIEXEC_CMD=%MSIEXEC_CMD% /l*v "%LOG_FILE%""

REM Add custom properties
if not "%INSTALL_DIR%"=="" (
    set "MSIEXEC_CMD=%MSIEXEC_CMD% INSTALLFOLDER="%INSTALL_DIR%""
)

set "MSIEXEC_CMD=%MSIEXEC_CMD% LANGUAGE_SELECTION=%LANGUAGE%"
set "MSIEXEC_CMD=%MSIEXEC_CMD% ADDLOCAL=%FEATURES%"

if "%DESKTOP_SHORTCUT%"=="0" (
    set "MSIEXEC_CMD=%MSIEXEC_CMD% REMOVE=DesktopShortcut"
)

if "%START_MENU%"=="0" (
    set "MSIEXEC_CMD=%MSIEXEC_CMD% STARTMENU_SHORTCUTS=0"
)

set "MSIEXEC_CMD=%MSIEXEC_CMD% AUTO_UPDATE_ENABLED=%AUTO_UPDATE%"

echo Executing installation command:
echo %MSIEXEC_CMD%
echo.

REM Execute installation
echo Installing Al-Bayan Connect...
%MSIEXEC_CMD%
set "INSTALL_RESULT=%errorlevel%"

echo.
echo Installation completed with exit code: %INSTALL_RESULT%

REM Check installation result
if %INSTALL_RESULT% equ 0 (
    echo SUCCESS: Al-Bayan Connect has been installed successfully.
    goto :success_exit
) else if %INSTALL_RESULT% equ 1602 (
    echo ERROR: Installation was cancelled by user.
    goto :error_exit
) else if %INSTALL_RESULT% equ 1603 (
    echo ERROR: A fatal error occurred during installation.
    goto :error_exit
) else if %INSTALL_RESULT% equ 1618 (
    echo ERROR: Another installation is already in progress.
    goto :error_exit
) else if %INSTALL_RESULT% equ 1619 (
    echo ERROR: This installation package could not be opened.
    goto :error_exit
) else if %INSTALL_RESULT% equ 1633 (
    echo ERROR: This installation package is not supported on this platform.
    goto :error_exit
) else if %INSTALL_RESULT% equ 1638 (
    echo ERROR: Another version of this product is already installed.
    goto :error_exit
) else if %INSTALL_RESULT% equ 3010 (
    echo SUCCESS: Installation completed successfully. A restart is required.
    goto :success_exit
) else (
    echo ERROR: Installation failed with exit code %INSTALL_RESULT%.
    goto :error_exit
)

:success_exit
echo.
echo ===============================================
echo Installation Summary
echo ===============================================
echo Status: SUCCESS
echo Log File: %LOG_FILE%
echo.
echo Al-Bayan Connect is now ready to use!
echo The add-in will appear in Microsoft Office applications.
echo.
echo For support, visit: https://github.com/al-bayan-ai/al-bayan-connect
echo ===============================================
exit /b 0

:error_exit
echo.
echo ===============================================
echo Installation Summary
echo ===============================================
echo Status: FAILED
echo Exit Code: %INSTALL_RESULT%
echo Log File: %LOG_FILE%
echo.
echo Please check the log file for detailed error information.
echo For support, visit: https://github.com/al-bayan-ai/al-bayan-connect
echo ===============================================
exit /b %INSTALL_RESULT%

:show_help
echo.
echo Al-Bayan Connect Silent Installation Script
echo.
echo Usage: SilentInstall.bat [OPTIONS]
echo.
echo Options:
echo   /INSTALLDIR "path"    Specify custom installation directory
echo   /LANGUAGE lang        Set installation language (en/ar)
echo   /FEATURES features    Specify features to install (ALL/MainFeature)
echo   /NODESKTOP           Do not create desktop shortcut
echo   /NOSTARTMENU         Do not create start menu entries
echo   /NOAUTOUPDATE        Disable automatic updates
echo   /HELP or /?          Show this help message
echo.
echo Examples:
echo   SilentInstall.bat
echo   SilentInstall.bat /INSTALLDIR "C:\Program Files\Al-Bayan Connect"
echo   SilentInstall.bat /LANGUAGE ar /NODESKTOP
echo   SilentInstall.bat /FEATURES MainFeature /NOAUTOUPDATE
echo.
echo For enterprise deployment, you can also use:
echo   msiexec.exe /i AlBayanConnectInstaller.msi /quiet INSTALLFOLDER="C:\Program Files\Al-Bayan Connect"
echo.
exit /b 0
