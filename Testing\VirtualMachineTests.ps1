# Al-<PERSON>an Connect - Virtual Machine Testing Automation
# Author: Dr. <PERSON>
# Copyright: © 2025 Al-Bayan AI Platform

param(
    [string]$VMProvider = "Hyper-V",
    [string]$TestMatrix = "Standard",
    [string]$InstallerPath = "",
    [switch]$CreateVMs,
    [switch]$RunTests,
    [switch]$CleanupVMs,
    [switch]$GenerateReport,
    [switch]$Verbose,
    [switch]$Help
)

# Configuration
$script:ScriptVersion = "1.0.0"
$script:TestResults = @()
$script:VMConfigurations = @()

# Define test matrix configurations
$script:TestMatrixes = @{
    "Standard" = @(
        @{ OS = "Windows 10"; Office = "2016"; Language = "en-US"; Browser = "Chrome" },
        @{ OS = "Windows 10"; Office = "2019"; Language = "en-US"; Browser = "Edge" },
        @{ OS = "Windows 11"; Office = "365"; Language = "en-US"; Browser = "Chrome" },
        @{ OS = "Windows 10"; Office = "2019"; Language = "ar-SA"; Browser = "Edge" }
    )
    "Extended" = @(
        @{ OS = "Windows 10"; Office = "2016"; Language = "en-US"; Browser = "Chrome" },
        @{ OS = "Windows 10"; Office = "2016"; Language = "ar-SA"; Browser = "Chrome" },
        @{ OS = "Windows 10"; Office = "2019"; Language = "en-US"; Browser = "Edge" },
        @{ OS = "Windows 10"; Office = "2019"; Language = "ar-SA"; Browser = "Edge" },
        @{ OS = "Windows 11"; Office = "365"; Language = "en-US"; Browser = "Chrome" },
        @{ OS = "Windows 11"; Office = "365"; Language = "ar-SA"; Browser = "Chrome" },
        @{ OS = "Windows 10"; Office = "2013"; Language = "en-US"; Browser = "Firefox" }
    )
    "Minimal" = @(
        @{ OS = "Windows 10"; Office = "2019"; Language = "en-US"; Browser = "Edge" },
        @{ OS = "Windows 10"; Office = "2019"; Language = "ar-SA"; Browser = "Edge" }
    )
}

function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    if ($Verbose -or $Level -eq "ERROR" -or $Level -eq "WARN") {
        $color = switch ($Level) {
            "ERROR" { "Red" }
            "WARN" { "Yellow" }
            "SUCCESS" { "Green" }
            "VM" { "Magenta" }
            default { "White" }
        }
        Write-Host $logEntry -ForegroundColor $color
    }
}

function Show-Help {
    Write-Host @"

Al-Bayan Connect Virtual Machine Testing v$script:ScriptVersion

DESCRIPTION:
    Automates testing across multiple virtual machine configurations
    to ensure compatibility across different Windows and Office versions.

SYNTAX:
    .\VirtualMachineTests.ps1 [OPTIONS]

PARAMETERS:
    -VMProvider <provider>    VM provider (Hyper-V/VMware/VirtualBox)
    -TestMatrix <matrix>      Test matrix (Standard/Extended/Minimal)
    -InstallerPath <path>     Path to installer MSI file
    -CreateVMs               Create test virtual machines
    -RunTests                Execute tests on VMs
    -CleanupVMs              Remove test VMs after completion
    -GenerateReport          Generate comprehensive test report
    -Verbose                 Enable verbose output
    -Help                    Show this help message

EXAMPLES:
    # Create VMs and run standard tests
    .\VirtualMachineTests.ps1 -CreateVMs -RunTests -TestMatrix Standard

    # Run tests on existing VMs
    .\VirtualMachineTests.ps1 -RunTests -InstallerPath "installer.msi"

    # Full test cycle with cleanup
    .\VirtualMachineTests.ps1 -CreateVMs -RunTests -CleanupVMs -GenerateReport

TEST MATRICES:
    Standard - Core compatibility testing (4 configurations)
    Extended - Comprehensive testing (7 configurations)
    Minimal  - Quick validation (2 configurations)

"@
}

function Initialize-VMTesting {
    Write-Log "Initializing VM testing environment..." "INFO"
    
    # Check VM provider availability
    switch ($VMProvider) {
        "Hyper-V" {
            if (-not (Get-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V-All).State -eq "Enabled") {
                throw "Hyper-V is not enabled. Please enable Hyper-V feature."
            }
        }
        "VMware" {
            if (-not (Get-Command "vmrun.exe" -ErrorAction SilentlyContinue)) {
                throw "VMware Workstation/Player not found. Please install VMware."
            }
        }
        "VirtualBox" {
            if (-not (Get-Command "VBoxManage.exe" -ErrorAction SilentlyContinue)) {
                throw "VirtualBox not found. Please install VirtualBox."
            }
        }
        default {
            throw "Unsupported VM provider: $VMProvider"
        }
    }
    
    # Load test matrix
    $script:VMConfigurations = $script:TestMatrixes[$TestMatrix]
    if (-not $script:VMConfigurations) {
        throw "Invalid test matrix: $TestMatrix"
    }
    
    Write-Log "VM Provider: $VMProvider" "INFO"
    Write-Log "Test Matrix: $TestMatrix ($($script:VMConfigurations.Count) configurations)" "INFO"
}

function New-TestVirtualMachine {
    param(
        [hashtable]$Config,
        [int]$Index
    )
    
    $vmName = "AlBayan-Test-$($Config.OS.Replace(' ', ''))-Office$($Config.Office)-$($Config.Language)-$Index"
    Write-Log "Creating VM: $vmName" "VM"
    
    try {
        switch ($VMProvider) {
            "Hyper-V" {
                New-HyperVTestVM -Name $vmName -Config $Config
            }
            "VMware" {
                New-VMwareTestVM -Name $vmName -Config $Config
            }
            "VirtualBox" {
                New-VirtualBoxTestVM -Name $vmName -Config $Config
            }
        }
        
        Write-Log "VM created successfully: $vmName" "SUCCESS"
        return $vmName
    }
    catch {
        Write-Log "Failed to create VM $vmName : $($_.Exception.Message)" "ERROR"
        return $null
    }
}

function New-HyperVTestVM {
    param(
        [string]$Name,
        [hashtable]$Config
    )
    
    # Create VM with Hyper-V
    $vmPath = "C:\VMs\$Name"
    New-Item -Path $vmPath -ItemType Directory -Force | Out-Null
    
    New-VM -Name $Name -MemoryStartupBytes 4GB -Path $vmPath -Generation 2
    Set-VM -Name $Name -ProcessorCount 2
    
    # Create virtual hard disk
    $vhdPath = Join-Path $vmPath "$Name.vhdx"
    New-VHD -Path $vhdPath -SizeBytes 60GB -Dynamic
    Add-VMHardDiskDrive -VMName $Name -Path $vhdPath
    
    # Configure network
    $switchName = "Default Switch"
    Connect-VMNetworkAdapter -VMName $Name -SwitchName $switchName
    
    # Set boot order
    Set-VMFirmware -VMName $Name -EnableSecureBoot Off
    
    Write-Log "Hyper-V VM '$Name' created with $($Config.OS) configuration" "VM"
}

function New-VMwareTestVM {
    param(
        [string]$Name,
        [hashtable]$Config
    )
    
    # VMware VM creation would be implemented here
    # This is a placeholder for VMware-specific VM creation
    Write-Log "VMware VM creation not implemented in this demo" "WARN"
}

function New-VirtualBoxTestVM {
    param(
        [string]$Name,
        [hashtable]$Config
    )
    
    # VirtualBox VM creation would be implemented here
    # This is a placeholder for VirtualBox-specific VM creation
    Write-Log "VirtualBox VM creation not implemented in this demo" "WARN"
}

function Start-VMTesting {
    Write-Log "Starting VM testing process..." "INFO"
    
    $testIndex = 0
    foreach ($config in $script:VMConfigurations) {
        $testIndex++
        $vmName = "AlBayan-Test-$($config.OS.Replace(' ', ''))-Office$($config.Office)-$($config.Language)-$testIndex"
        
        Write-Log "Testing configuration $testIndex/$($script:VMConfigurations.Count): $($config.OS) + Office $($config.Office) ($($config.Language))" "VM"
        
        try {
            # Start VM
            Start-TestVM -VMName $vmName
            
            # Wait for VM to boot
            Wait-VMReady -VMName $vmName
            
            # Copy installer to VM
            Copy-InstallerToVM -VMName $vmName -InstallerPath $InstallerPath
            
            # Execute tests on VM
            $testResult = Invoke-VMTest -VMName $vmName -Config $config
            
            # Collect results
            $script:TestResults += $testResult
            
            # Stop VM
            Stop-TestVM -VMName $vmName
            
            Write-Log "Test completed for $vmName : $($testResult.Status)" "VM"
        }
        catch {
            Write-Log "Test failed for $vmName : $($_.Exception.Message)" "ERROR"
            
            # Record failure
            $failureResult = [PSCustomObject]@{
                VMName = $vmName
                Configuration = $config
                Status = "FAIL"
                Error = $_.Exception.Message
                StartTime = Get-Date
                EndTime = Get-Date
                Duration = 0
                Details = "VM test execution failed"
            }
            
            $script:TestResults += $failureResult
        }
    }
}

function Start-TestVM {
    param([string]$VMName)
    
    Write-Log "Starting VM: $VMName" "VM"
    
    switch ($VMProvider) {
        "Hyper-V" {
            Start-VM -Name $VMName
        }
        "VMware" {
            & vmrun start "$VMName.vmx"
        }
        "VirtualBox" {
            & VBoxManage startvm $VMName --type headless
        }
    }
}

function Wait-VMReady {
    param([string]$VMName)
    
    Write-Log "Waiting for VM to be ready: $VMName" "VM"
    
    $timeout = 300 # 5 minutes
    $elapsed = 0
    
    while ($elapsed -lt $timeout) {
        try {
            # Check if VM is responsive (this would need actual implementation)
            # For demo purposes, we'll just wait a fixed time
            Start-Sleep -Seconds 30
            Write-Log "VM $VMName is ready" "SUCCESS"
            return
        }
        catch {
            Start-Sleep -Seconds 10
            $elapsed += 10
        }
    }
    
    throw "VM $VMName failed to become ready within timeout"
}

function Copy-InstallerToVM {
    param(
        [string]$VMName,
        [string]$InstallerPath
    )
    
    Write-Log "Copying installer to VM: $VMName" "VM"
    
    # This would implement actual file copying to VM
    # Implementation depends on VM provider and guest tools
    switch ($VMProvider) {
        "Hyper-V" {
            # Use PowerShell Direct or copy via shared folder
            Write-Log "Copying installer via Hyper-V integration" "VM"
        }
        "VMware" {
            # Use vmrun copyFileFromHostToGuest
            Write-Log "Copying installer via VMware tools" "VM"
        }
        "VirtualBox" {
            # Use VBoxManage guestcontrol copyto
            Write-Log "Copying installer via VirtualBox guest additions" "VM"
        }
    }
}

function Invoke-VMTest {
    param(
        [string]$VMName,
        [hashtable]$Config
    )
    
    Write-Log "Executing tests on VM: $VMName" "VM"
    
    $startTime = Get-Date
    
    try {
        # Execute test framework on VM
        $testScript = @"
# Remote test execution script
Set-Location C:\AlBayanTest
.\TestFramework.ps1 -TestSuite All -InstallerPath AlBayanConnectInstaller.msi -GenerateReport
"@
        
        # Execute script on VM (implementation depends on VM provider)
        $result = Invoke-VMScript -VMName $VMName -Script $testScript
        
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalMinutes
        
        # Parse results
        $testResult = [PSCustomObject]@{
            VMName = $VMName
            Configuration = $Config
            Status = if ($result.ExitCode -eq 0) { "PASS" } else { "FAIL" }
            StartTime = $startTime
            EndTime = $endTime
            Duration = $duration
            Details = "VM test completed"
            ExitCode = $result.ExitCode
            Output = $result.Output
        }
        
        return $testResult
    }
    catch {
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalMinutes
        
        return [PSCustomObject]@{
            VMName = $VMName
            Configuration = $Config
            Status = "ERROR"
            StartTime = $startTime
            EndTime = $endTime
            Duration = $duration
            Details = $_.Exception.Message
            ExitCode = -1
            Output = ""
        }
    }
}

function Invoke-VMScript {
    param(
        [string]$VMName,
        [string]$Script
    )
    
    # This would implement actual script execution on VM
    # Implementation depends on VM provider and guest tools
    
    Write-Log "Executing script on VM: $VMName" "VM"
    
    # Simulate script execution
    Start-Sleep -Seconds 60 # Simulate test execution time
    
    return @{
        ExitCode = 0
        Output = "Test execution completed successfully"
    }
}

function Stop-TestVM {
    param([string]$VMName)
    
    Write-Log "Stopping VM: $VMName" "VM"
    
    switch ($VMProvider) {
        "Hyper-V" {
            Stop-VM -Name $VMName -Force
        }
        "VMware" {
            & vmrun stop "$VMName.vmx"
        }
        "VirtualBox" {
            & VBoxManage controlvm $VMName poweroff
        }
    }
}

function Remove-TestVMs {
    Write-Log "Cleaning up test VMs..." "INFO"
    
    foreach ($config in $script:VMConfigurations) {
        $vmName = "AlBayan-Test-$($config.OS.Replace(' ', ''))-Office$($config.Office)-$($config.Language)-*"
        
        try {
            switch ($VMProvider) {
                "Hyper-V" {
                    Get-VM -Name $vmName -ErrorAction SilentlyContinue | ForEach-Object {
                        Remove-VM -Name $_.Name -Force
                        Write-Log "Removed VM: $($_.Name)" "SUCCESS"
                    }
                }
                "VMware" {
                    # VMware cleanup implementation
                    Write-Log "VMware cleanup not implemented in demo" "WARN"
                }
                "VirtualBox" {
                    # VirtualBox cleanup implementation
                    Write-Log "VirtualBox cleanup not implemented in demo" "WARN"
                }
            }
        }
        catch {
            Write-Log "Failed to remove VM $vmName : $($_.Exception.Message)" "ERROR"
        }
    }
}

function Generate-VMTestReport {
    Write-Log "Generating VM test report..." "INFO"
    
    $totalTests = $script:TestResults.Count
    $passedTests = ($script:TestResults | Where-Object { $_.Status -eq "PASS" }).Count
    $failedTests = ($script:TestResults | Where-Object { $_.Status -eq "FAIL" }).Count
    $errorTests = ($script:TestResults | Where-Object { $_.Status -eq "ERROR" }).Count
    
    $reportPath = "VMTestReport_$(Get-Date -Format 'yyyyMMdd_HHmmss').html"
    
    $html = @"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Al-Bayan Connect VM Test Report</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { color: #2c5aa0; font-size: 2.5em; font-weight: bold; margin-bottom: 10px; }
        .subtitle { color: #666; font-style: italic; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; text-align: center; }
        .metric h3 { margin: 0 0 10px 0; font-size: 1.2em; }
        .metric .value { font-size: 2em; font-weight: bold; }
        .pass { background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); }
        .fail { background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%); }
        .error { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); }
        .results-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        .results-table th, .results-table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .results-table th { background-color: #f8f9fa; font-weight: 600; }
        .status-pass { color: #28a745; font-weight: bold; }
        .status-fail { color: #dc3545; font-weight: bold; }
        .status-error { color: #fd7e14; font-weight: bold; }
        .config-details { font-size: 0.9em; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">البيان كونكت</div>
            <div class="logo">Al-Bayan Connect</div>
            <div class="subtitle">Virtual Machine Test Report - $(Get-Date -Format 'MMMM dd, yyyy HH:mm')</div>
            <div class="subtitle">Test Matrix: $TestMatrix | VM Provider: $VMProvider</div>
        </div>
        
        <div class="summary">
            <div class="metric">
                <h3>Total Tests</h3>
                <div class="value">$totalTests</div>
            </div>
            <div class="metric pass">
                <h3>Passed</h3>
                <div class="value">$passedTests</div>
            </div>
            <div class="metric fail">
                <h3>Failed</h3>
                <div class="value">$failedTests</div>
            </div>
            <div class="metric error">
                <h3>Errors</h3>
                <div class="value">$errorTests</div>
            </div>
        </div>
        
        <h2>VM Test Results</h2>
        <table class="results-table">
            <thead>
                <tr>
                    <th>VM Configuration</th>
                    <th>Status</th>
                    <th>Duration</th>
                    <th>Details</th>
                    <th>Timestamp</th>
                </tr>
            </thead>
            <tbody>
"@
    
    foreach ($result in $script:TestResults) {
        $statusClass = switch ($result.Status) {
            "PASS" { "status-pass" }
            "FAIL" { "status-fail" }
            "ERROR" { "status-error" }
            default { "" }
        }
        
        $configText = "$($result.Configuration.OS) + Office $($result.Configuration.Office) ($($result.Configuration.Language))"
        
        $html += @"
                <tr>
                    <td>
                        <strong>$($result.VMName)</strong><br>
                        <span class="config-details">$configText</span>
                    </td>
                    <td class="$statusClass">$($result.Status)</td>
                    <td>${($result.Duration)} min</td>
                    <td>$($result.Details)</td>
                    <td>$($result.EndTime.ToString('HH:mm:ss'))</td>
                </tr>
"@
    }
    
    $html += @"
            </tbody>
        </table>
        
        <div class="footer" style="text-align: center; margin-top: 30px; color: #666; font-size: 0.9em;">
            <p><strong>Al-Bayan Connect VM Testing Framework v$script:ScriptVersion</strong></p>
            <p>"Together, we build a better community for Al-Bayan"</p>
            <p>© 2025 Al-Bayan AI Platform - Dr. Mohammed Yagoub Esmail</p>
        </div>
    </div>
</body>
</html>
"@
    
    Set-Content -Path $reportPath -Value $html -Encoding UTF8
    Write-Log "VM test report generated: $reportPath" "SUCCESS"
    
    return $reportPath
}

function Main {
    Write-Log "=== Al-Bayan Connect VM Testing Started ===" "INFO"
    Write-Log "Framework Version: $script:ScriptVersion" "INFO"
    
    if ($Help) {
        Show-Help
        return 0
    }
    
    try {
        # Initialize VM testing environment
        Initialize-VMTesting
        
        # Create VMs if requested
        if ($CreateVMs) {
            Write-Log "Creating test virtual machines..." "INFO"
            $vmIndex = 0
            foreach ($config in $script:VMConfigurations) {
                $vmIndex++
                $vmName = New-TestVirtualMachine -Config $config -Index $vmIndex
                if (-not $vmName) {
                    Write-Log "Failed to create VM for configuration $vmIndex" "ERROR"
                }
            }
        }
        
        # Run tests if requested
        if ($RunTests) {
            if ([string]::IsNullOrEmpty($InstallerPath)) {
                $InstallerPath = "bin\Release\AlBayanConnectInstaller.msi"
            }
            
            if (-not (Test-Path $InstallerPath)) {
                throw "Installer not found: $InstallerPath"
            }
            
            Start-VMTesting
        }
        
        # Generate report if requested
        if ($GenerateReport) {
            $reportPath = Generate-VMTestReport
            Start-Process $reportPath
        }
        
        # Cleanup VMs if requested
        if ($CleanupVMs) {
            Remove-TestVMs
        }
        
        Write-Log "=== VM Testing Completed ===" "SUCCESS"
        return 0
    }
    catch {
        Write-Log "VM testing failed: $($_.Exception.Message)" "ERROR"
        return 1
    }
}

# Execute main function
$exitCode = Main
exit $exitCode
