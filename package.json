{"name": "al-bayan-connect", "version": "1.0.0", "description": "Revolutionary bilingual dictation add-in for Microsoft Office with seamless Arabic-English voice-to-text capabilities", "author": "Dr. <PERSON> <<EMAIL>>", "license": "MIT", "private": true, "main": "dist/taskpane.html", "keywords": ["office-add-in", "microsoft-office", "dictation", "bilingual", "arabic", "english", "speech-recognition", "voice-commands", "al-bayan", "productivity"], "repository": {"type": "git", "url": "https://github.com/al-bayan-ai/al-bayan-connect"}, "scripts": {"dev": "webpack serve --mode development", "dev-server": "npx http-server . -p 3000 --cors", "dev-secure": "npx http-server . -p 3000 --cors -S -C cert.pem -K key.pem", "build": "webpack --mode production", "build:dev": "webpack --mode development", "start": "node server.js", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "type-check": "tsc --noEmit", "validate": "npx office-addin-manifest validate manifest.xml", "sideload": "npx office-addin-dev-certs install && npx office-addin-debugging start manifest.xml", "stop": "npx office-addin-debugging stop manifest.xml", "install-certs": "npx office-addin-dev-certs install", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "deploy": "npm run build && echo 'Ready for deployment'", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@microsoft/office-js": "^1.1.91", "@microsoft/office-js-helpers": "^1.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "@fluentui/react": "^8.110.0", "@fluentui/react-icons": "^2.0.220", "@fluentui/react-components": "^9.40.0", "axios": "^1.6.0", "uuid": "^9.0.1", "lodash": "^4.17.21", "date-fns": "^2.30.0", "recharts": "^2.8.0", "react-router-dom": "^6.8.0", "zustand": "^4.4.0", "react-hook-form": "^7.45.0", "react-query": "^3.39.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/uuid": "^9.0.7", "@types/lodash": "^4.14.195", "@types/jest": "^29.5.0", "typescript": "^5.0.0", "webpack": "^5.88.0", "webpack-cli": "^5.1.0", "webpack-dev-server": "^4.15.0", "webpack-merge": "^5.9.0", "ts-loader": "^9.4.0", "html-webpack-plugin": "^5.5.0", "css-loader": "^6.8.0", "style-loader": "^3.3.0", "sass": "^1.62.0", "sass-loader": "^13.3.0", "file-loader": "^6.2.0", "url-loader": "^4.1.1", "copy-webpack-plugin": "^11.0.0", "clean-webpack-plugin": "^4.0.0", "eslint": "^8.45.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.0.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^5.16.0", "@testing-library/user-event": "^14.4.0", "office-addin-debugging": "^5.0.0", "office-addin-dev-certs": "^1.11.0", "office-addin-manifest": "^1.12.0", "rimraf": "^5.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}