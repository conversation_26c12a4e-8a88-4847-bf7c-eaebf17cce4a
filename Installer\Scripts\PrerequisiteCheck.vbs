' Al-Bayan Connect - Prerequisite Check Script
' Author: Dr. <PERSON>
' Copyright: © 2025 Al-Bayan AI Platform

Option Explicit

Dim shell, fso, wmi, logFile
Dim checkResults, errorCount, warningCount

' Initialize objects
Set shell = CreateObject("WScript.Shell")
Set fso = CreateObject("Scripting.FileSystemObject")
Set wmi = GetObject("winmgmts:")

' Initialize variables
Set checkResults = CreateObject("Scripting.Dictionary")
errorCount = 0
warningCount = 0

' Configuration
logFile = shell.ExpandEnvironmentStrings("%TEMP%") & "\AlBayanConnect_PrerequisiteCheck.log"

' Main prerequisite check function
Function CheckAllPrerequisites()
    WriteLog "=== Al-Bayan Connect Prerequisite Check Started ==="
    WriteLog "Date: " & Now()
    WriteLog "Computer: " & shell.ExpandEnvironmentStrings("%COMPUTERNAME%")
    WriteLog "User: " & shell.ExpandEnvironmentStrings("%USERNAME%")
    WriteLog ""
    
    ' Perform all checks
    CheckOperatingSystem()
    CheckMemory()
    CheckDiskSpace()
    CheckOfficeVersions()
    CheckNetFramework()
    CheckBrowserSupport()
    CheckAudioDevices()
    CheckInternetConnection()
    CheckUserPermissions()
    
    ' Generate summary
    GenerateSummary()
    
    ' Return result
    If errorCount = 0 Then
        CheckAllPrerequisites = 0 ' Success
    Else
        CheckAllPrerequisites = 1 ' Failure
    End If
End Function

' Check Operating System
Sub CheckOperatingSystem()
    Dim osVersion, osName, buildNumber, isSupported
    
    WriteLog "Checking Operating System..."
    
    On Error Resume Next
    osVersion = shell.RegRead("HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\CurrentVersion")
    osName = shell.RegRead("HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\ProductName")
    buildNumber = shell.RegRead("HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\CurrentBuildNumber")
    
    If Err.Number <> 0 Then
        RecordError "Unable to detect operating system version"
        Exit Sub
    End If
    
    WriteLog "OS Name: " & osName
    WriteLog "OS Version: " & osVersion
    WriteLog "Build Number: " & buildNumber
    
    ' Check if OS is supported (Windows 10 or later)
    isSupported = False
    If CDbl(osVersion) >= 10.0 Then
        isSupported = True
    ElseIf CDbl(osVersion) = 6.3 And CInt(buildNumber) >= 9600 Then ' Windows 8.1
        isSupported = True
        RecordWarning "Windows 8.1 detected. Windows 10 or later is recommended."
    ElseIf CDbl(osVersion) = 6.1 And CInt(buildNumber) >= 7601 Then ' Windows 7 SP1
        isSupported = True
        RecordWarning "Windows 7 detected. This OS is no longer supported by Microsoft."
    End If
    
    If isSupported Then
        checkResults.Add "OS", "PASS"
        WriteLog "✓ Operating System: SUPPORTED"
    Else
        checkResults.Add "OS", "FAIL"
        RecordError "Unsupported operating system. Windows 10 or later required."
    End If
    
    WriteLog ""
End Sub

' Check System Memory
Sub CheckMemory()
    Dim totalMemory, memoryGB
    
    WriteLog "Checking System Memory..."
    
    On Error Resume Next
    For Each objItem In wmi.ExecQuery("SELECT TotalPhysicalMemory FROM Win32_ComputerSystem")
        totalMemory = objItem.TotalPhysicalMemory
    Next
    
    If Err.Number <> 0 Or totalMemory = "" Then
        RecordWarning "Unable to detect system memory"
        checkResults.Add "Memory", "UNKNOWN"
        Exit Sub
    End If
    
    memoryGB = Round(totalMemory / 1024 / 1024 / 1024, 1)
    WriteLog "Total Physical Memory: " & memoryGB & " GB"
    
    If memoryGB >= 4 Then
        checkResults.Add "Memory", "PASS"
        WriteLog "✓ Memory: SUFFICIENT (" & memoryGB & " GB)"
    ElseIf memoryGB >= 2 Then
        checkResults.Add "Memory", "WARNING"
        RecordWarning "Low memory detected (" & memoryGB & " GB). 4 GB or more recommended."
    Else
        checkResults.Add "Memory", "FAIL"
        RecordError "Insufficient memory (" & memoryGB & " GB). Minimum 2 GB required."
    End If
    
    WriteLog ""
End Sub

' Check Available Disk Space
Sub CheckDiskSpace()
    Dim drive, freeSpace, freeSpaceMB
    
    WriteLog "Checking Disk Space..."
    
    On Error Resume Next
    Set drive = fso.GetDrive(fso.GetDriveName(shell.ExpandEnvironmentStrings("%SYSTEMDRIVE%")))
    freeSpace = drive.FreeSpace
    
    If Err.Number <> 0 Then
        RecordWarning "Unable to check disk space"
        checkResults.Add "DiskSpace", "UNKNOWN"
        Exit Sub
    End If
    
    freeSpaceMB = Round(freeSpace / 1024 / 1024, 0)
    WriteLog "Free Disk Space: " & freeSpaceMB & " MB"
    
    If freeSpaceMB >= 500 Then
        checkResults.Add "DiskSpace", "PASS"
        WriteLog "✓ Disk Space: SUFFICIENT (" & freeSpaceMB & " MB)"
    ElseIf freeSpaceMB >= 100 Then
        checkResults.Add "DiskSpace", "WARNING"
        RecordWarning "Low disk space (" & freeSpaceMB & " MB). 500 MB or more recommended."
    Else
        checkResults.Add "DiskSpace", "FAIL"
        RecordError "Insufficient disk space (" & freeSpaceMB & " MB). Minimum 100 MB required."
    End If
    
    WriteLog ""
End Sub

' Check Office Versions
Sub CheckOfficeVersions()
    Dim officeVersions, version, installPath, isSupported
    
    WriteLog "Checking Microsoft Office Installation..."
    
    officeVersions = Array("16.0", "15.0", "14.0")
    isSupported = False
    
    For Each version In officeVersions
        On Error Resume Next
        installPath = shell.RegRead("HKLM\SOFTWARE\Microsoft\Office\" & version & "\Common\InstallRoot\Path")
        
        If Err.Number = 0 And installPath <> "" Then
            WriteLog "Found Office " & version & " at: " & installPath
            
            Select Case version
                Case "16.0"
                    WriteLog "✓ Office 2016/2019/365: SUPPORTED"
                    isSupported = True
                Case "15.0"
                    WriteLog "✓ Office 2013: SUPPORTED (Limited)"
                    isSupported = True
                    RecordWarning "Office 2013 detected. Some features may be limited."
                Case "14.0"
                    WriteLog "⚠ Office 2010: LEGACY SUPPORT"
                    isSupported = True
                    RecordWarning "Office 2010 detected. Upgrade to Office 2016 or later recommended."
            End Select
        End If
        Err.Clear
    Next
    
    ' Check for Office 365 Click-to-Run
    On Error Resume Next
    Dim clickToRunPath
    clickToRunPath = shell.RegRead("HKLM\SOFTWARE\Microsoft\Office\ClickToRun\Configuration\ProductReleaseIds")
    If Err.Number = 0 And clickToRunPath <> "" Then
        WriteLog "✓ Office 365 Click-to-Run: DETECTED"
        isSupported = True
    End If
    Err.Clear
    
    If isSupported Then
        checkResults.Add "Office", "PASS"
        WriteLog "✓ Microsoft Office: SUPPORTED VERSION FOUND"
    Else
        checkResults.Add "Office", "FAIL"
        RecordError "No supported Microsoft Office version found. Office 2013 or later required."
    End If
    
    WriteLog ""
End Sub

' Check .NET Framework
Sub CheckNetFramework()
    Dim netVersions, version, releaseKey, isSupported
    
    WriteLog "Checking .NET Framework..."
    
    ' Check for .NET Framework 4.8
    On Error Resume Next
    releaseKey = shell.RegRead("HKLM\SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\Release")
    
    If Err.Number = 0 Then
        If releaseKey >= 528040 Then
            WriteLog "✓ .NET Framework 4.8: INSTALLED"
            checkResults.Add "NetFramework", "PASS"
            isSupported = True
        ElseIf releaseKey >= 461808 Then
            WriteLog "⚠ .NET Framework 4.7.2: INSTALLED"
            checkResults.Add "NetFramework", "WARNING"
            RecordWarning ".NET Framework 4.7.2 detected. 4.8 or later recommended."
            isSupported = True
        ElseIf releaseKey >= 460798 Then
            WriteLog "⚠ .NET Framework 4.7: INSTALLED"
            checkResults.Add "NetFramework", "WARNING"
            RecordWarning ".NET Framework 4.7 detected. 4.8 or later recommended."
            isSupported = True
        Else
            WriteLog "✗ .NET Framework: OUTDATED VERSION"
            checkResults.Add "NetFramework", "FAIL"
            RecordError "Outdated .NET Framework version. 4.7 or later required."
        End If
    Else
        WriteLog "✗ .NET Framework: NOT FOUND"
        checkResults.Add "NetFramework", "FAIL"
        RecordError ".NET Framework not found. 4.7 or later required."
    End If
    
    WriteLog ""
End Sub

' Check Browser Support
Sub CheckBrowserSupport()
    Dim browsers, browser, browserPath, supportedBrowsers
    
    WriteLog "Checking Browser Support (for Office Online)..."
    
    browsers = Array("chrome.exe", "msedge.exe", "firefox.exe")
    supportedBrowsers = 0
    
    For Each browser In browsers
        On Error Resume Next
        browserPath = shell.RegRead("HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\" & browser & "\")
        
        If Err.Number = 0 And browserPath <> "" Then
            WriteLog "✓ " & browser & ": FOUND at " & browserPath
            supportedBrowsers = supportedBrowsers + 1
        End If
        Err.Clear
    Next
    
    If supportedBrowsers > 0 Then
        checkResults.Add "Browser", "PASS"
        WriteLog "✓ Browser Support: " & supportedBrowsers & " supported browser(s) found"
    Else
        checkResults.Add "Browser", "WARNING"
        RecordWarning "No supported browsers found. Chrome, Edge, or Firefox recommended for Office Online."
    End If
    
    WriteLog ""
End Sub

' Check Audio Devices
Sub CheckAudioDevices()
    Dim audioDevices, deviceCount
    
    WriteLog "Checking Audio Devices..."
    
    deviceCount = 0
    On Error Resume Next
    
    For Each objItem In wmi.ExecQuery("SELECT Name FROM Win32_SoundDevice WHERE Status='OK'")
        WriteLog "Audio Device: " & objItem.Name
        deviceCount = deviceCount + 1
    Next
    
    If Err.Number <> 0 Then
        RecordWarning "Unable to enumerate audio devices"
        checkResults.Add "Audio", "UNKNOWN"
    ElseIf deviceCount > 0 Then
        checkResults.Add "Audio", "PASS"
        WriteLog "✓ Audio Devices: " & deviceCount & " device(s) found"
    Else
        checkResults.Add "Audio", "WARNING"
        RecordWarning "No audio devices found. Microphone required for dictation."
    End If
    
    WriteLog ""
End Sub

' Check Internet Connection
Sub CheckInternetConnection()
    Dim http, testUrl
    
    WriteLog "Checking Internet Connection..."
    
    On Error Resume Next
    Set http = CreateObject("MSXML2.XMLHTTP")
    testUrl = "https://www.microsoft.com"
    
    http.Open "HEAD", testUrl, False
    http.Send
    
    If Err.Number = 0 And http.Status = 200 Then
        checkResults.Add "Internet", "PASS"
        WriteLog "✓ Internet Connection: AVAILABLE"
    Else
        checkResults.Add "Internet", "WARNING"
        RecordWarning "Internet connection test failed. Some features may be limited."
    End If
    
    WriteLog ""
End Sub

' Check User Permissions
Sub CheckUserPermissions()
    Dim isAdmin, testKey
    
    WriteLog "Checking User Permissions..."
    
    ' Test if user can write to HKCU
    On Error Resume Next
    testKey = "HKCU\Software\Al-Bayan\Connect\Test\"
    shell.RegWrite testKey & "TestValue", "Test", "REG_SZ"
    
    If Err.Number = 0 Then
        shell.RegDelete testKey
        checkResults.Add "Permissions", "PASS"
        WriteLog "✓ User Permissions: SUFFICIENT"
    Else
        checkResults.Add "Permissions", "FAIL"
        RecordError "Insufficient user permissions. Unable to write to registry."
    End If
    
    WriteLog ""
End Sub

' Generate Summary Report
Sub GenerateSummary()
    Dim key, status, passCount, failCount, warningCount, unknownCount
    
    WriteLog "=== PREREQUISITE CHECK SUMMARY ==="
    
    passCount = 0
    failCount = 0
    warningCount = 0
    unknownCount = 0
    
    For Each key In checkResults.Keys
        status = checkResults(key)
        WriteLog key & ": " & status
        
        Select Case status
            Case "PASS"
                passCount = passCount + 1
            Case "FAIL"
                failCount = failCount + 1
            Case "WARNING"
                warningCount = warningCount + 1
            Case "UNKNOWN"
                unknownCount = unknownCount + 1
        End Select
    Next
    
    WriteLog ""
    WriteLog "RESULTS:"
    WriteLog "✓ Passed: " & passCount
    WriteLog "⚠ Warnings: " & warningCount
    WriteLog "✗ Failed: " & failCount
    WriteLog "? Unknown: " & unknownCount
    WriteLog ""
    
    If failCount = 0 Then
        WriteLog "OVERALL RESULT: SYSTEM READY FOR INSTALLATION"
    Else
        WriteLog "OVERALL RESULT: SYSTEM NOT READY - " & failCount & " CRITICAL ISSUE(S) FOUND"
    End If
    
    WriteLog "=== END OF PREREQUISITE CHECK ==="
End Sub

' Utility Functions
Sub RecordError(message)
    errorCount = errorCount + 1
    WriteLog "✗ ERROR: " & message
End Sub

Sub RecordWarning(message)
    warningCount = warningCount + 1
    WriteLog "⚠ WARNING: " & message
End Sub

Sub WriteLog(message)
    Dim logFileHandle
    
    On Error Resume Next
    Set logFileHandle = fso.OpenTextFile(logFile, 8, True)
    
    If Err.Number = 0 Then
        logFileHandle.WriteLine Now() & " - " & message
        logFileHandle.Close
    End If
    
    WScript.Echo message
End Sub

' Main execution
Dim result
result = CheckAllPrerequisites()

WScript.Echo ""
WScript.Echo "Prerequisite check completed. Log file: " & logFile
WScript.Quit result
