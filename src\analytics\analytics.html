<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Al-<PERSON>an Connect - Analytics Dashboard</title>
    
    <!-- Office.js -->
    <script type="text/javascript" src="https://appsforoffice.microsoft.com/lib/1/hosted/office.js"></script>
    
    <!-- Fluent UI Styles -->
    <link rel="stylesheet" href="https://static2.sharepointonline.com/files/fabric/office-ui-fabric-core/11.0.0/css/fabric.min.css">
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #faf9f8;
        }
        
        .analytics-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
            color: white;
            padding: 16px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }
        
        .content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            flex-direction: column;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #0078d4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>

<body>
    <div class="analytics-container">
        <div class="header">
            <h1>📊 Analytics Dashboard</h1>
        </div>
        
        <div class="content">
            <div id="loading" class="loading">
                <div class="loading-spinner"></div>
                <div>Loading analytics data...</div>
            </div>
            
            <div id="analytics-root" style="display: none;"></div>
        </div>
    </div>

    <script>
        Office.onReady((info) => {
            console.log('Analytics dashboard ready. Host:', info.host);
            
            // Hide loading and show analytics
            document.getElementById('loading').style.display = 'none';
            document.getElementById('analytics-root').style.display = 'block';
        });
    </script>
</body>
</html>
