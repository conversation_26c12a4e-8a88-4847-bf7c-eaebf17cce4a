// Al-Bayan Connect - Error Boundary Component
// Author: Dr. <PERSON>
// Copyright: © 2025 Al-Bayan AI Platform

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Button, Text, MessageBar, MessageBarType } from '@fluentui/react-components';
import { ErrorCircle24Regular, ArrowClockwise24Regular } from '@fluentui/react-icons';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // Log error to analytics service if available
    try {
      // You could send this to your analytics service
      const errorData = {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString()
      };
      
      localStorage.setItem('al-bayan-last-error', JSON.stringify(errorData));
    } catch (e) {
      console.error('Failed to log error:', e);
    }
  }

  handleReload = () => {
    window.location.reload();
  };

  handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
  };

  render() {
    if (this.state.hasError) {
      return (
        <div style={{ 
          padding: '20px', 
          height: '100vh', 
          display: 'flex', 
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          textAlign: 'center'
        }}>
          <div style={{ maxWidth: '400px', width: '100%' }}>
            <div style={{ 
              fontSize: '48px', 
              marginBottom: '16px',
              color: '#d13438'
            }}>
              <ErrorCircle24Regular />
            </div>
            
            <Text size={500} weight="semibold" style={{ marginBottom: '8px', display: 'block' }}>
              Something went wrong
            </Text>
            
            <Text size={300} style={{ marginBottom: '16px', display: 'block', color: '#605e5c' }}>
              Al-Bayan Connect encountered an unexpected error. We apologize for the inconvenience.
            </Text>

            <MessageBar
              messageBarType={MessageBarType.error}
              style={{ marginBottom: '16px', textAlign: 'left' }}
            >
              <Text size={200}>
                <strong>Error:</strong> {this.state.error?.message || 'Unknown error occurred'}
              </Text>
            </MessageBar>

            <div style={{ display: 'flex', gap: '8px', justifyContent: 'center' }}>
              <Button
                appearance="primary"
                icon={<ArrowClockwise24Regular />}
                onClick={this.handleReset}
              >
                Try Again
              </Button>
              
              <Button
                appearance="secondary"
                onClick={this.handleReload}
              >
                Reload Add-in
              </Button>
            </div>

            {process.env.NODE_ENV === 'development' && this.state.errorInfo && (
              <details style={{ 
                marginTop: '20px', 
                textAlign: 'left',
                background: '#f8f9fa',
                padding: '12px',
                borderRadius: '4px',
                fontSize: '12px'
              }}>
                <summary style={{ cursor: 'pointer', marginBottom: '8px' }}>
                  <Text size={200} weight="semibold">Error Details (Development)</Text>
                </summary>
                
                <div style={{ marginBottom: '12px' }}>
                  <Text size={100} weight="semibold">Error Stack:</Text>
                  <pre style={{ 
                    whiteSpace: 'pre-wrap', 
                    fontSize: '11px',
                    background: 'white',
                    padding: '8px',
                    borderRadius: '2px',
                    border: '1px solid #d2d0ce',
                    marginTop: '4px'
                  }}>
                    {this.state.error?.stack}
                  </pre>
                </div>
                
                <div>
                  <Text size={100} weight="semibold">Component Stack:</Text>
                  <pre style={{ 
                    whiteSpace: 'pre-wrap', 
                    fontSize: '11px',
                    background: 'white',
                    padding: '8px',
                    borderRadius: '2px',
                    border: '1px solid #d2d0ce',
                    marginTop: '4px'
                  }}>
                    {this.state.errorInfo?.componentStack}
                  </pre>
                </div>
              </details>
            )}

            <div style={{ 
              marginTop: '20px', 
              padding: '12px',
              background: '#f3f2f1',
              borderRadius: '4px'
            }}>
              <Text size={200} style={{ color: '#605e5c' }}>
                If this problem persists, please contact support at{' '}
                <a href="mailto:<EMAIL>" style={{ color: '#0078d4' }}>
                  <EMAIL>
                </a>
              </Text>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
