# Al-Bayan Connect - Enterprise Deployment Script
# Author: Dr. <PERSON>il
# Copyright: © 2025 Al-Bayan AI Platform

param(
    [string]$InstallerPath = "",
    [string]$InstallDirectory = "",
    [string]$Language = "en",
    [string]$Features = "ALL",
    [switch]$NoDesktopShortcut,
    [switch]$NoStartMenu,
    [switch]$NoAutoUpdate,
    [switch]$Force,
    [string]$LogPath = "",
    [string]$ConfigFile = "",
    [switch]$WhatIf,
    [switch]$Verbose,
    [switch]$Help
)

# Configuration
$script:ScriptVersion = "1.0.0"
$script:ProductName = "Al-Bayan Connect"
$script:DefaultInstaller = "AlBayanConnectInstaller.msi"
$script:DefaultLogPath = "$env:TEMP\AlBayanConnect_EnterpriseDeployment.log"

# Initialize logging
if ([string]::IsNullOrEmpty($LogPath)) {
    $LogPath = $script:<PERSON><PERSON>LogPath
}

function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    Write-Host $logEntry
    Add-Content -Path $LogPath -Value $logEntry -ErrorAction SilentlyContinue
}

function Show-Help {
    Write-Host @"

Al-Bayan Connect Enterprise Deployment Script v$script:ScriptVersion

DESCRIPTION:
    Deploys Al-Bayan Connect across enterprise environments with advanced
    configuration options and comprehensive logging.

SYNTAX:
    .\EnterpriseDeployment.ps1 [OPTIONS]

PARAMETERS:
    -InstallerPath <path>      Path to the MSI installer file
    -InstallDirectory <path>   Custom installation directory
    -Language <lang>           Installation language (en/ar)
    -Features <features>       Features to install (ALL/MainFeature)
    -NoDesktopShortcut        Skip desktop shortcut creation
    -NoStartMenu              Skip start menu entries
    -NoAutoUpdate             Disable automatic updates
    -Force                    Force installation even if already installed
    -LogPath <path>           Custom log file path
    -ConfigFile <path>        Load configuration from file
    -WhatIf                   Show what would be done without executing
    -Verbose                  Enable verbose output
    -Help                     Show this help message

EXAMPLES:
    # Basic installation
    .\EnterpriseDeployment.ps1

    # Custom installation directory with Arabic language
    .\EnterpriseDeployment.ps1 -InstallDirectory "C:\Apps\Al-Bayan Connect" -Language ar

    # Silent deployment without shortcuts
    .\EnterpriseDeployment.ps1 -NoDesktopShortcut -NoStartMenu -NoAutoUpdate

    # Test deployment (WhatIf mode)
    .\EnterpriseDeployment.ps1 -WhatIf -Verbose

    # Force reinstallation
    .\EnterpriseDeployment.ps1 -Force

    # Use configuration file
    .\EnterpriseDeployment.ps1 -ConfigFile "deployment-config.json"

CONFIGURATION FILE FORMAT (JSON):
    {
        "installDirectory": "C:\\Program Files\\Al-Bayan Connect",
        "language": "en",
        "features": "ALL",
        "createDesktopShortcut": true,
        "createStartMenu": true,
        "enableAutoUpdate": true,
        "customProperties": {
            "ENTERPRISE_MODE": "1",
            "TELEMETRY_ENABLED": "0"
        }
    }

EXIT CODES:
    0    Success
    1    General error
    2    Invalid parameters
    3    Installer not found
    4    Installation failed
    5    Prerequisites not met

"@
}

function Test-Prerequisites {
    Write-Log "Checking prerequisites..." "INFO"
    
    $issues = @()
    
    # Check PowerShell version
    if ($PSVersionTable.PSVersion.Major -lt 5) {
        $issues += "PowerShell 5.0 or later is required"
    }
    
    # Check if running as administrator
    $currentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
    if (-not $currentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)) {
        Write-Log "WARNING: Not running as administrator. Some features may not work correctly." "WARN"
    }
    
    # Check Windows version
    $osVersion = [System.Environment]::OSVersion.Version
    if ($osVersion.Major -lt 10) {
        $issues += "Windows 10 or later is recommended"
    }
    
    # Check available disk space
    $systemDrive = $env:SystemDrive
    $freeSpace = (Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='$systemDrive'").FreeSpace / 1MB
    if ($freeSpace -lt 500) {
        $issues += "Insufficient disk space (less than 500 MB available)"
    }
    
    if ($issues.Count -gt 0) {
        Write-Log "Prerequisites check failed:" "ERROR"
        foreach ($issue in $issues) {
            Write-Log "  - $issue" "ERROR"
        }
        return $false
    }
    
    Write-Log "Prerequisites check passed" "INFO"
    return $true
}

function Get-InstallerPath {
    if (-not [string]::IsNullOrEmpty($InstallerPath)) {
        return $InstallerPath
    }
    
    # Look for installer in script directory
    $scriptDir = Split-Path -Parent $MyInvocation.ScriptName
    $installerPath = Join-Path $scriptDir $script:DefaultInstaller
    
    if (Test-Path $installerPath) {
        return $installerPath
    }
    
    # Look in current directory
    $installerPath = Join-Path (Get-Location) $script:DefaultInstaller
    if (Test-Path $installerPath) {
        return $installerPath
    }
    
    return $null
}

function Load-ConfigurationFile {
    param([string]$ConfigPath)
    
    if ([string]::IsNullOrEmpty($ConfigPath) -or -not (Test-Path $ConfigPath)) {
        return $null
    }
    
    try {
        $config = Get-Content $ConfigPath | ConvertFrom-Json
        Write-Log "Configuration loaded from: $ConfigPath" "INFO"
        return $config
    }
    catch {
        Write-Log "Failed to load configuration file: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

function Build-InstallCommand {
    param(
        [string]$InstallerPath,
        [object]$Config
    )
    
    $msiexecArgs = @(
        "/i", "`"$InstallerPath`"",
        "/quiet",
        "/norestart",
        "/l*v", "`"$LogPath`""
    )
    
    # Apply configuration
    if ($Config) {
        if ($Config.installDirectory) { $msiexecArgs += "INSTALLFOLDER=`"$($Config.installDirectory)`"" }
        if ($Config.language) { $msiexecArgs += "LANGUAGE_SELECTION=$($Config.language)" }
        if ($Config.features) { $msiexecArgs += "ADDLOCAL=$($Config.features)" }
        if (-not $Config.createDesktopShortcut) { $msiexecArgs += "REMOVE=DesktopShortcut" }
        if (-not $Config.createStartMenu) { $msiexecArgs += "STARTMENU_SHORTCUTS=0" }
        if (-not $Config.enableAutoUpdate) { $msiexecArgs += "AUTO_UPDATE_ENABLED=0" }
        
        # Add custom properties
        if ($Config.customProperties) {
            foreach ($property in $Config.customProperties.PSObject.Properties) {
                $msiexecArgs += "$($property.Name)=$($property.Value)"
            }
        }
    } else {
        # Apply command line parameters
        if (-not [string]::IsNullOrEmpty($InstallDirectory)) { $msiexecArgs += "INSTALLFOLDER=`"$InstallDirectory`"" }
        $msiexecArgs += "LANGUAGE_SELECTION=$Language"
        $msiexecArgs += "ADDLOCAL=$Features"
        if ($NoDesktopShortcut) { $msiexecArgs += "REMOVE=DesktopShortcut" }
        if ($NoStartMenu) { $msiexecArgs += "STARTMENU_SHORTCUTS=0" }
        if ($NoAutoUpdate) { $msiexecArgs += "AUTO_UPDATE_ENABLED=0" }
    }
    
    if ($Force) {
        $msiexecArgs += "REINSTALL=ALL"
        $msiexecArgs += "REINSTALLMODE=vomus"
    }
    
    return $msiexecArgs
}

function Start-Installation {
    param(
        [string]$InstallerPath,
        [array]$Arguments
    )
    
    $commandLine = "msiexec.exe " + ($Arguments -join " ")
    
    if ($WhatIf) {
        Write-Log "WHATIF: Would execute: $commandLine" "INFO"
        return 0
    }
    
    Write-Log "Executing installation command: $commandLine" "INFO"
    
    try {
        $process = Start-Process -FilePath "msiexec.exe" -ArgumentList $Arguments -Wait -PassThru -NoNewWindow
        return $process.ExitCode
    }
    catch {
        Write-Log "Failed to start installation process: $($_.Exception.Message)" "ERROR"
        return 1
    }
}

function Get-ExitCodeDescription {
    param([int]$ExitCode)
    
    switch ($ExitCode) {
        0 { return "Success" }
        1602 { return "Installation was cancelled by user" }
        1603 { return "A fatal error occurred during installation" }
        1618 { return "Another installation is already in progress" }
        1619 { return "This installation package could not be opened" }
        1633 { return "This installation package is not supported on this platform" }
        1638 { return "Another version of this product is already installed" }
        3010 { return "Success - Restart required" }
        default { return "Unknown error (Exit code: $ExitCode)" }
    }
}

# Main execution
function Main {
    Write-Log "=== Al-Bayan Connect Enterprise Deployment Started ===" "INFO"
    Write-Log "Script Version: $script:ScriptVersion" "INFO"
    Write-Log "Date: $(Get-Date)" "INFO"
    Write-Log "Computer: $env:COMPUTERNAME" "INFO"
    Write-Log "User: $env:USERNAME" "INFO"
    Write-Log "PowerShell Version: $($PSVersionTable.PSVersion)" "INFO"
    
    if ($Help) {
        Show-Help
        return 0
    }
    
    # Load configuration file if specified
    $config = Load-ConfigurationFile $ConfigFile
    
    # Check prerequisites
    if (-not (Test-Prerequisites)) {
        Write-Log "Prerequisites check failed. Aborting installation." "ERROR"
        return 5
    }
    
    # Find installer
    $installerPath = Get-InstallerPath
    if (-not $installerPath -or -not (Test-Path $installerPath)) {
        Write-Log "Installer not found. Please specify the correct path with -InstallerPath parameter." "ERROR"
        return 3
    }
    
    Write-Log "Using installer: $installerPath" "INFO"
    
    # Build installation command
    $installArgs = Build-InstallCommand -InstallerPath $installerPath -Config $config
    
    # Execute installation
    $exitCode = Start-Installation -InstallerPath $installerPath -Arguments $installArgs
    $exitDescription = Get-ExitCodeDescription $exitCode
    
    Write-Log "Installation completed with exit code: $exitCode ($exitDescription)" "INFO"
    
    if ($exitCode -eq 0 -or $exitCode -eq 3010) {
        Write-Log "=== Al-Bayan Connect Enterprise Deployment Completed Successfully ===" "INFO"
        if ($exitCode -eq 3010) {
            Write-Log "A system restart is required to complete the installation." "WARN"
        }
    } else {
        Write-Log "=== Al-Bayan Connect Enterprise Deployment Failed ===" "ERROR"
        Write-Log "Please check the installation log for detailed error information: $LogPath" "ERROR"
    }
    
    return $exitCode
}

# Execute main function
$exitCode = Main
exit $exitCode
