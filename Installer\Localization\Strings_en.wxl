<?xml version="1.0" encoding="utf-8"?>
<WixLocalization Culture="en-us" xmlns="http://wixtoolset.org/schemas/v4/wxl">
  
  <!-- Product Information -->
  <String Id="Language">1033</String>
  <String Id="ProductName">Al-Bayan Connect</String>
  <String Id="ProductDescription">Revolutionary bilingual dictation add-in for Microsoft Office</String>
  <String Id="ProductComments">Seamless Arabic-English voice-to-text with intelligent language detection</String>
  <String Id="Manufacturer">Al-Bayan AI Platform - Dr. Mohammed Yagoub Esmail</String>
  <String Id="VersionInfo">Version 1.0.0 - January 2025</String>

  <!-- Error Messages -->
  <String Id="DowngradeError">A newer version of [ProductName] is already installed. Setup will now exit.</String>
  <String Id="OSVersionError">This application requires Windows 10 or later.</String>
  <String Id="OfficeVersionError">Microsoft Office 2016 or later is required to install this add-in.</String>
  <String Id="NetFrameworkError">Microsoft .NET Framework 4.8 or later is required.</String>

  <!-- Language Selection Dialog -->
  <String Id="LanguageSelectionTitle">Choose Installation Language</String>
  <String Id="LanguageSelectionDescription">Select your preferred language for the installation process</String>
  <String Id="SelectLanguageLabel">Please select the language you would like to use during installation:</String>
  <String Id="LanguageSelectionHelp">This setting only affects the installer interface. You can use both Arabic and English with Al-Bayan Connect after installation.</String>
  <String Id="LanguageOptions">Language Selection</String>

  <!-- Welcome Dialog -->
  <String Id="WelcomeTitle">Welcome to Al-Bayan Connect Setup</String>
  <String Id="WelcomeDlgTitle">Welcome to the Al-Bayan Connect Setup Wizard</String>
  <String Id="WelcomeDlgDescription">This wizard will guide you through the installation of Al-Bayan Connect, the revolutionary bilingual dictation add-in for Microsoft Office.

Al-Bayan Connect brings seamless Arabic-English voice-to-text capabilities directly to Word, PowerPoint, and Outlook with:

• Intelligent language detection
• Advanced voice commands  
• Custom command creation
• Enterprise-grade security
• Real-time productivity analytics

Click Next to continue or Cancel to exit the Setup Wizard.</String>

  <!-- License Agreement Dialog -->
  <String Id="LicenseAgreementTitle">License Agreement</String>
  <String Id="LicenseAgreementDescription">Please read the following license agreement carefully</String>
  <String Id="LicenseAcceptedText">I &amp;accept the terms in the License Agreement</String>

  <!-- Customize Dialog -->
  <String Id="CustomizeTitle">Custom Setup</String>
  <String Id="CustomizeDescription">Choose which features to install and where to install them</String>
  <String Id="InstallLocationLabel">Install Al-Bayan Connect to:</String>

  <!-- Verify Ready Dialog -->
  <String Id="VerifyReadyTitle">Ready to Install</String>
  <String Id="VerifyReadyDescription">The wizard is ready to begin installation</String>
  <String Id="VerifyReadyText">Click Install to begin the installation.

Al-Bayan Connect will be installed with the following features:
• Main dictation engine with bilingual support
• Office integration for Word, PowerPoint, and Outlook  
• Custom voice command builder
• Usage analytics and productivity insights
• Enterprise security features

If you want to review or change any of your installation settings, click Back. Click Cancel to exit the wizard.</String>

  <!-- Feature Descriptions -->
  <String Id="MainFeatureTitle">Al-Bayan Connect Core</String>
  <String Id="MainFeatureDescription">Essential components for bilingual dictation in Microsoft Office</String>
  <String Id="DesktopShortcutTitle">Desktop Shortcut</String>
  <String Id="DesktopShortcutDescription">Create a shortcut on the desktop for quick access to Al-Bayan Connect settings</String>

  <!-- Maintenance Dialogs -->
  <String Id="MaintenanceWelcomeTitle">Welcome to Al-Bayan Connect Maintenance</String>
  <String Id="MaintenanceWelcomeDescription">The Setup Wizard will allow you to repair or remove Al-Bayan Connect</String>
  <String Id="MaintenanceTypeTitle">Change, repair, or remove installation</String>
  <String Id="MaintenanceTypeDescription">Choose the maintenance option you would like to perform</String>

  <!-- Exit Dialog -->
  <String Id="ExitDialogOptionalText">Al-Bayan Connect has been successfully installed!

You can now start using bilingual dictation in Microsoft Office applications. The add-in will appear in the ribbon of Word, PowerPoint, and Outlook.

For help getting started, visit: https://github.com/al-bayan-ai/al-bayan-connect/wiki</String>
  <String Id="LaunchApplication">Open Al-Bayan Connect Settings</String>

  <!-- Common UI Strings -->
  <String Id="WixUINext">&amp;Next &gt;</String>
  <String Id="WixUIBack">&lt; &amp;Back</String>
  <String Id="WixUICancel">Cancel</String>
  <String Id="WixUIInstall">&amp;Install</String>
  <String Id="WixUIFinish">&amp;Finish</String>
  <String Id="WixUIBrowse">&amp;Browse...</String>
  <String Id="WixUIDiskCost">&amp;Disk Cost...</String>
  <String Id="WixUIBannerBmp">WixUIBannerBmp</String>
  <String Id="WixUIDialogBmp">WixUIDialogBmp</String>

  <!-- Progress Dialog -->
  <String Id="ProgressTitle">Installing Al-Bayan Connect</String>
  <String Id="ProgressDescription">Please wait while the Setup Wizard installs Al-Bayan Connect</String>
  <String Id="ProgressTextInstalling">Installing: [1]</String>
  <String Id="ProgressTextRemoving">Removing: [1]</String>

  <!-- Error Handling -->
  <String Id="ErrorDialogTitle">Al-Bayan Connect Setup Error</String>
  <String Id="ErrorDialogDescription">An error occurred during installation</String>
  <String Id="FatalErrorTitle">Fatal Error</String>
  <String Id="FatalErrorDescription">A fatal error occurred during installation</String>
  <String Id="UserExitTitle">Installation Cancelled</String>
  <String Id="UserExitDescription">The installation was cancelled by the user</String>

  <!-- File Descriptions -->
  <String Id="ManifestFileDescription">Office Add-in Manifest</String>
  <String Id="WebAssetsDescription">Web Application Files</String>
  <String Id="ScriptsDescription">Installation Scripts</String>

</WixLocalization>
