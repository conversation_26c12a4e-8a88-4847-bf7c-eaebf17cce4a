// Al-Bayan Connect - Office Integration Service
// Author: Dr. <PERSON>
// Copyright: © 2025 Al-Bayan AI Platform

import {
  DocumentContext,
  OfficeApplication,
  TextRange,
  TextFormatting,
  Language,
  TextDirection,
  VoiceCommand,
  CommandAction
} from '@types/index';

export class OfficeIntegrationService {
  private currentContext: DocumentContext | null = null;
  private isInitialized = false;

  constructor() {
    this.initializeOffice();
  }

  private async initializeOffice(): Promise<void> {
    try {
      await Office.onReady();
      this.isInitialized = true;
      this.updateDocumentContext();
    } catch (error) {
      console.error('Failed to initialize Office:', error);
    }
  }

  public async insertText(text: string, language?: Language): Promise<boolean> {
    if (!this.isInitialized) {
      await this.initializeOffice();
    }

    try {
      const app = this.getCurrentApplication();
      
      switch (app) {
        case OfficeApplication.WORD:
          return await this.insertTextInWord(text, language);
        case OfficeApplication.POWERPOINT:
          return await this.insertTextInPowerPoint(text, language);
        case OfficeApplication.OUTLOOK:
          return await this.insertTextInOutlook(text, language);
        default:
          console.warn('Unsupported Office application');
          return false;
      }
    } catch (error) {
      console.error('Failed to insert text:', error);
      return false;
    }
  }

  private async insertTextInWord(text: string, language?: Language): Promise<boolean> {
    return new Promise((resolve) => {
      Word.run(async (context) => {
        try {
          const selection = context.document.getSelection();
          
          // Apply language-specific formatting
          if (language) {
            const formatting = this.getLanguageFormatting(language);
            this.applyFormattingToRange(selection, formatting);
          }
          
          selection.insertText(text, Word.InsertLocation.replace);
          
          await context.sync();
          resolve(true);
        } catch (error) {
          console.error('Word insertion error:', error);
          resolve(false);
        }
      });
    });
  }

  private async insertTextInPowerPoint(text: string, language?: Language): Promise<boolean> {
    return new Promise((resolve) => {
      PowerPoint.run(async (context) => {
        try {
          const slides = context.presentation.slides;
          const currentSlide = slides.getActiveSlide();
          
          // Try to get the selected text box or create a new one
          const shapes = currentSlide.shapes;
          const textBoxes = shapes.getTextBoxes();
          
          context.load(textBoxes);
          await context.sync();
          
          let targetTextBox;
          if (textBoxes.items.length > 0) {
            targetTextBox = textBoxes.items[0]; // Use first text box
          } else {
            // Create a new text box
            targetTextBox = shapes.addTextBox(text);
          }
          
          const textRange = targetTextBox.textFrame.textRange;
          
          // Apply language-specific formatting
          if (language) {
            const formatting = this.getLanguageFormatting(language);
            this.applyFormattingToPowerPointRange(textRange, formatting);
          }
          
          textRange.insertText(text, PowerPoint.InsertLocation.replace);
          
          await context.sync();
          resolve(true);
        } catch (error) {
          console.error('PowerPoint insertion error:', error);
          resolve(false);
        }
      });
    });
  }

  private async insertTextInOutlook(text: string, language?: Language): Promise<boolean> {
    try {
      // For Outlook, we use the compose mode
      if (Office.context.mailbox.item) {
        const item = Office.context.mailbox.item;
        
        if (item.itemType === Office.MailboxEnums.ItemType.Message) {
          // Get current body content
          item.body.getAsync(Office.CoercionType.Html, (result) => {
            if (result.status === Office.AsyncResultStatus.Succeeded) {
              const currentBody = result.value;
              const newBody = currentBody + this.formatTextForHtml(text, language);
              
              item.body.setAsync(newBody, { coercionType: Office.CoercionType.Html });
            }
          });
          return true;
        }
      }
      return false;
    } catch (error) {
      console.error('Outlook insertion error:', error);
      return false;
    }
  }

  public async executeCommand(command: VoiceCommand): Promise<boolean> {
    try {
      const action = this.getCommandAction(command);
      
      switch (command.action) {
        case CommandAction.INSERT_TEXT:
          return await this.insertText(action);
        case CommandAction.FORMAT_TEXT:
          return await this.applyFormatting(action);
        case CommandAction.NAVIGATE:
          return await this.performNavigation(action);
        case CommandAction.DELETE:
          return await this.deleteSelection();
        case CommandAction.UNDO:
          return await this.undoLastAction();
        default:
          return false;
      }
    } catch (error) {
      console.error('Failed to execute command:', error);
      return false;
    }
  }

  private getCommandAction(command: VoiceCommand): string {
    // Map command phrases to actions
    const actionMap: Record<string, string> = {
      'period': '.',
      'comma': ',',
      'question mark': '?',
      'exclamation point': '!',
      'نقطة': '.',
      'فاصلة': '،',
      'علامة استفهام': '؟',
      'علامة تعجب': '!',
      'new line': '\n',
      'new paragraph': '\n\n',
      'سطر جديد': '\n',
      'فقرة جديدة': '\n\n',
      'bold that': 'BOLD',
      'غامق': 'BOLD',
      'italicize that': 'ITALIC',
      'مائل': 'ITALIC',
      'underline that': 'UNDERLINE',
      'تسطير': 'UNDERLINE'
    };
    
    return actionMap[command.phrase] || command.phrase;
  }

  private async applyFormatting(formatType: string): Promise<boolean> {
    const app = this.getCurrentApplication();
    
    switch (app) {
      case OfficeApplication.WORD:
        return await this.applyWordFormatting(formatType);
      case OfficeApplication.POWERPOINT:
        return await this.applyPowerPointFormatting(formatType);
      default:
        return false;
    }
  }

  private async applyWordFormatting(formatType: string): Promise<boolean> {
    return new Promise((resolve) => {
      Word.run(async (context) => {
        try {
          const selection = context.document.getSelection();
          
          switch (formatType) {
            case 'BOLD':
              selection.font.bold = true;
              break;
            case 'ITALIC':
              selection.font.italic = true;
              break;
            case 'UNDERLINE':
              selection.font.underline = Word.UnderlineType.single;
              break;
          }
          
          await context.sync();
          resolve(true);
        } catch (error) {
          console.error('Word formatting error:', error);
          resolve(false);
        }
      });
    });
  }

  private async applyPowerPointFormatting(formatType: string): Promise<boolean> {
    return new Promise((resolve) => {
      PowerPoint.run(async (context) => {
        try {
          const slides = context.presentation.slides;
          const currentSlide = slides.getActiveSlide();
          const shapes = currentSlide.shapes;
          const textBoxes = shapes.getTextBoxes();
          
          context.load(textBoxes);
          await context.sync();
          
          if (textBoxes.items.length > 0) {
            const textRange = textBoxes.items[0].textFrame.textRange;
            
            switch (formatType) {
              case 'BOLD':
                textRange.font.bold = true;
                break;
              case 'ITALIC':
                textRange.font.italic = true;
                break;
              case 'UNDERLINE':
                textRange.font.underline = PowerPoint.FontUnderlineStyle.single;
                break;
            }
            
            await context.sync();
          }
          
          resolve(true);
        } catch (error) {
          console.error('PowerPoint formatting error:', error);
          resolve(false);
        }
      });
    });
  }

  private async performNavigation(action: string): Promise<boolean> {
    switch (action) {
      case '\n':
        return await this.insertText('\n');
      case '\n\n':
        return await this.insertText('\n\n');
      default:
        return false;
    }
  }

  private async deleteSelection(): Promise<boolean> {
    const app = this.getCurrentApplication();
    
    switch (app) {
      case OfficeApplication.WORD:
        return await this.deleteWordSelection();
      case OfficeApplication.POWERPOINT:
        return await this.deletePowerPointSelection();
      default:
        return false;
    }
  }

  private async deleteWordSelection(): Promise<boolean> {
    return new Promise((resolve) => {
      Word.run(async (context) => {
        try {
          const selection = context.document.getSelection();
          selection.delete();
          await context.sync();
          resolve(true);
        } catch (error) {
          console.error('Word deletion error:', error);
          resolve(false);
        }
      });
    });
  }

  private async deletePowerPointSelection(): Promise<boolean> {
    // PowerPoint selection deletion is more complex
    // For now, we'll just clear the text in the active text box
    return new Promise((resolve) => {
      PowerPoint.run(async (context) => {
        try {
          const slides = context.presentation.slides;
          const currentSlide = slides.getActiveSlide();
          const shapes = currentSlide.shapes;
          const textBoxes = shapes.getTextBoxes();
          
          context.load(textBoxes);
          await context.sync();
          
          if (textBoxes.items.length > 0) {
            const textRange = textBoxes.items[0].textFrame.textRange;
            textRange.delete();
            await context.sync();
          }
          
          resolve(true);
        } catch (error) {
          console.error('PowerPoint deletion error:', error);
          resolve(false);
        }
      });
    });
  }

  private async undoLastAction(): Promise<boolean> {
    // Office.js doesn't provide direct undo functionality
    // We would need to implement our own undo stack
    console.warn('Undo functionality not yet implemented');
    return false;
  }

  private getLanguageFormatting(language: Language): TextFormatting {
    const baseFormatting: TextFormatting = {
      bold: false,
      italic: false,
      underline: false,
      fontSize: 12,
      fontFamily: 'Calibri',
      color: '#000000',
      direction: TextDirection.LTR
    };

    if (language === Language.ARABIC) {
      return {
        ...baseFormatting,
        fontFamily: 'Traditional Arabic',
        direction: TextDirection.RTL
      };
    }

    return baseFormatting;
  }

  private applyFormattingToRange(range: any, formatting: TextFormatting): void {
    if (range.font) {
      range.font.name = formatting.fontFamily;
      range.font.size = formatting.fontSize;
      range.font.color = formatting.color;
      range.font.bold = formatting.bold;
      range.font.italic = formatting.italic;
    }
  }

  private applyFormattingToPowerPointRange(range: any, formatting: TextFormatting): void {
    if (range.font) {
      range.font.name = formatting.fontFamily;
      range.font.size = formatting.fontSize;
      range.font.bold = formatting.bold;
      range.font.italic = formatting.italic;
    }
  }

  private formatTextForHtml(text: string, language?: Language): string {
    if (language === Language.ARABIC) {
      return `<span dir="rtl" style="font-family: 'Traditional Arabic'">${text}</span>`;
    }
    return `<span dir="ltr">${text}</span>`;
  }

  private getCurrentApplication(): OfficeApplication {
    if (typeof Word !== 'undefined') {
      return OfficeApplication.WORD;
    } else if (typeof PowerPoint !== 'undefined') {
      return OfficeApplication.POWERPOINT;
    } else if (typeof Outlook !== 'undefined') {
      return OfficeApplication.OUTLOOK;
    }
    
    // Fallback detection
    const host = Office.context.host;
    switch (host) {
      case Office.HostType.Word:
        return OfficeApplication.WORD;
      case Office.HostType.PowerPoint:
        return OfficeApplication.POWERPOINT;
      case Office.HostType.Outlook:
        return OfficeApplication.OUTLOOK;
      default:
        return OfficeApplication.WORD; // Default fallback
    }
  }

  private updateDocumentContext(): void {
    const app = this.getCurrentApplication();
    
    this.currentContext = {
      application: app,
      documentId: this.generateDocumentId(),
      currentLanguage: Language.AUTO_DETECT
    };
  }

  private generateDocumentId(): string {
    return `doc-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  public getCurrentContext(): DocumentContext | null {
    return this.currentContext;
  }

  public isOfficeReady(): boolean {
    return this.isInitialized;
  }

  public async getSelectedText(): Promise<string> {
    const app = this.getCurrentApplication();
    
    switch (app) {
      case OfficeApplication.WORD:
        return await this.getWordSelectedText();
      case OfficeApplication.POWERPOINT:
        return await this.getPowerPointSelectedText();
      default:
        return '';
    }
  }

  private async getWordSelectedText(): Promise<string> {
    return new Promise((resolve) => {
      Word.run(async (context) => {
        try {
          const selection = context.document.getSelection();
          context.load(selection, 'text');
          await context.sync();
          resolve(selection.text);
        } catch (error) {
          console.error('Failed to get Word selection:', error);
          resolve('');
        }
      });
    });
  }

  private async getPowerPointSelectedText(): Promise<string> {
    return new Promise((resolve) => {
      PowerPoint.run(async (context) => {
        try {
          const slides = context.presentation.slides;
          const currentSlide = slides.getActiveSlide();
          const shapes = currentSlide.shapes;
          const textBoxes = shapes.getTextBoxes();
          
          context.load(textBoxes);
          await context.sync();
          
          if (textBoxes.items.length > 0) {
            const textRange = textBoxes.items[0].textFrame.textRange;
            context.load(textRange, 'text');
            await context.sync();
            resolve(textRange.text);
          } else {
            resolve('');
          }
        } catch (error) {
          console.error('Failed to get PowerPoint selection:', error);
          resolve('');
        }
      });
    });
  }
}
